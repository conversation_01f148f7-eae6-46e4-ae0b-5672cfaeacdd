<?php
return [
  'activity-logs' => 'Activity Logs',
  'activity-logs.list' => 'List Activity Logs',
  'activity-logs.view' => 'View Activity Logs',
  'invoices.create' => 'Create Invoices',
  'invoices.delete' => 'Delete Invoices',
  'invoices.edit' => 'Edit Invoices',
  'invoices.export' => 'Export Invoices',
  'invoices.list' => 'List Invoices',
  'invoices.print' => 'Print Invoices',
  'invoices.send' => 'Send Invoices',
  'invoices.view' => 'View Invoices',
  'permissions' => 'Manage Permissions',
  'products.create' => 'Create Products',
  'products.delete' => 'Delete Products',
  'products.edit' => 'Edit Products',
  'products.list' => 'List Products',
  'products.sync' => 'Sync Products',
  'products.view' => 'View Products',
  'products.import' => 'Import Products',
  'products.export' => 'Export Products',
  'roles.create' => 'Create Roles',
  'roles.delete' => 'Delete Roles',
  'roles.edit' => 'Edit Roles',
  'roles.list' => 'List Roles',
  'settings.authorize' => 'Authorize Settings',
  'settings.cache' => 'Cache Settings',
  'settings.health' => 'Health Settings',
  'settings.list' => 'List Settings',
  'settings.logs' => 'System Logs',
  'settings.update.asus' => 'Update ASUS Settings',
  'settings.update.general' => 'Update General Settings',
  'settings.update.nhattin' => 'Update Nhattin Settings',
  'settings.update.notification' => 'Update Notification Settings',
  'settings.update.sap' => 'Update SAP Settings',
  'settings.update.smtp' => 'Update SMTP Settings',
  'settings.update.vnpt' => 'Update VNPT Settings',
  'settings.update.zalo' => 'Update Zalo Settings',
  'users.create' => 'Create Users',
  'users.delete' => 'Delete Users',
  'users.edit' => 'Edit Users',
  'users.list' => 'List Users',
];
