<?php

return [
    'attributes' => 'Attributes',
    'attribute' => 'Attribute',
    'list' => 'Attribute List',
    'create' => 'Create Attribute',
    'edit' => 'Edit Attribute',
    'show' => 'Attribute Details',
    'name' => 'Name',
    'code' => 'Code',
    'display_name' => 'Display Name',
    'type' => 'Type',
    'description' => 'Description',
    'sort_order' => 'Sort Order',
    'is_required' => 'Required',
    'is_filterable' => 'Filterable',
    'is_active' => 'Active',
    'values' => 'Attribute Values',
    'value' => 'Value',
    'color_code' => 'Color Code',
    'add_value' => 'Add Value',
    'edit_value' => 'Edit Value',
    'delete_value' => 'Delete Value',
    'select' => 'Select',
    'text' => 'Text',
    'number' => 'Number',
    'boolean' => 'Boolean',
    'date' => 'Date',
    'product_attributes' => 'Product Attributes',
    'add_attribute' => 'Add Attribute',
    'edit_attribute' => 'Edit Attribute',
    'delete_attribute' => 'Delete Attribute',
    'batch_update' => 'Batch Update',
    'no_attributes' => 'No attributes found',
    'attribute_added' => 'Attribute added successfully',
    'attribute_updated' => 'Attribute updated successfully',
    'attribute_deleted' => 'Attribute deleted successfully',
    'value_added' => 'Attribute value added successfully',
    'value_updated' => 'Attribute value updated successfully',
    'value_deleted' => 'Attribute value deleted successfully',
    'product_attribute_added' => 'Product attribute added successfully',
    'product_attribute_updated' => 'Product attribute updated successfully',
    'product_attribute_deleted' => 'Product attribute deleted successfully',
    'product_attributes_updated' => 'Product attributes updated successfully',
];
