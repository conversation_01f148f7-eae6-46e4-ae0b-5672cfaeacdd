<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            // Thêm các trường liên quan đến hóa đơn
            $table->string('invoice_number')->nullable()->after('notes');
            $table->string('invoice_series')->nullable()->after('invoice_number');
            $table->string('invoice_template')->nullable()->after('invoice_series');
            $table->date('invoice_date')->nullable()->after('invoice_template');
            $table->boolean('has_documents')->default(false)->after('invoice_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            // Xóa các trường đã thêm
            $table->dropColumn([
                'invoice_number',
                'invoice_series',
                'invoice_template',
                'invoice_date',
                'has_documents'
            ]);
        });
    }
};
