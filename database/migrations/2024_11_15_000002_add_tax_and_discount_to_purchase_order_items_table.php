<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_order_items', function (Blueprint $table) {
            $table->decimal('tax_rate', 5, 2)->default(0)->after('unit_price');
            $table->decimal('unit_price_after_tax', 15, 2)->default(0)->after('tax_rate');
            $table->decimal('discount', 15, 2)->default(0)->after('unit_price_after_tax');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchase_order_items', function (Blueprint $table) {
            $table->dropColumn('tax_rate');
            $table->dropColumn('unit_price_after_tax');
            $table->dropColumn('discount');
        });
    }
};
