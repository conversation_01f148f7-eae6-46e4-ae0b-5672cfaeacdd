<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ki<PERSON>m tra xem bảng product_serials đã tồn tại chưa
        if (!Schema::hasTable('product_serials')) {
            // Tạo bảng product_serials
            Schema::create('product_serials', function (Blueprint $table) {
                $table->id();
                $table->foreignId('product_id')->constrained()->onDelete('cascade');
                $table->string('serial_number')->unique();
                $table->unsignedBigInteger('purchase_order_item_id')->nullable();
                $table->foreignId('warehouse_id')->nullable()->constrained()->onDelete('set null');
                $table->foreignId('warehouse_area_id')->nullable()->constrained()->onDelete('set null');
                $table->enum('status', ['available', 'sold', 'reserved', 'defective'])->default('available');
                $table->text('notes')->nullable();
                $table->softDeletes();
                $table->timestamps();
                
                // Thêm foreign key nếu bảng purchase_order_items đã tồn tại
                if (Schema::hasTable('purchase_order_items')) {
                    $table->foreign('purchase_order_item_id')->references('id')->on('purchase_order_items')->onDelete('set null');
                }
            });
            
            // Log thông báo
            DB::statement("INSERT INTO migrations (migration, batch) VALUES ('2025_04_20_213945_create_product_serials_table', (SELECT MAX(batch) FROM migrations))");
        } else {
            // Kiểm tra xem cột purchase_order_item_id đã tồn tại chưa
            if (!Schema::hasColumn('product_serials', 'purchase_order_item_id')) {
                // Thêm cột purchase_order_item_id
                Schema::table('product_serials', function (Blueprint $table) {
                    $table->unsignedBigInteger('purchase_order_item_id')->nullable();
                    
                    // Thêm foreign key nếu bảng purchase_order_items đã tồn tại
                    if (Schema::hasTable('purchase_order_items')) {
                        $table->foreign('purchase_order_item_id')->references('id')->on('purchase_order_items')->onDelete('set null');
                    }
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Không cần làm gì trong phương thức down vì đây là migration kiểm tra
    }
};
