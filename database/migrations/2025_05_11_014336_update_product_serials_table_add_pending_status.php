<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Thêm trạng thái 'pending' vào cột status của bảng product_serials
        // Trạng thái này sẽ được sử dụng khi IMEI được quét vào phiếu nhập hàng nhưng chưa được duyệt
        DB::statement("ALTER TABLE product_serials MODIFY COLUMN status ENUM('in_stock', 'sold', 'transferred', 'returned', 'cancelled', 'pending') NOT NULL DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Khôi phục lại các trạng thái cũ
        DB::statement("ALTER TABLE product_serials MODIFY COLUMN status ENUM('in_stock', 'sold', 'transferred', 'returned', 'cancelled') NOT NULL DEFAULT 'in_stock'");
    }
};
