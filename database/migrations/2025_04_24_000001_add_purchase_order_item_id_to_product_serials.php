<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Kiểm tra xem bảng product_serials đã tồn tại chưa
        if (Schema::hasTable('product_serials')) {
            Schema::table('product_serials', function (Blueprint $table) {
                if (!Schema::hasColumn('product_serials', 'purchase_order_item_id')) {
                    // Kiểm tra xem bảng purchase_order_items đã tồn tại chưa
                    if (Schema::hasTable('purchase_order_items')) {
                        $table->foreignId('purchase_order_item_id')->nullable()->constrained()->onDelete('set null');
                    } else {
                        // Nếu bảng purchase_order_items chưa tồn tại, chỉ tạo cột mà không tạo foreign key
                        $table->unsignedBigInteger('purchase_order_item_id')->nullable();
                    }
                }
            });
        } else {
            // Nếu bảng product_serials chưa tồn tại, tạo bảng mới
            Schema::create('product_serials', function (Blueprint $table) {
                $table->id();
                $table->foreignId('product_id')->constrained()->onDelete('cascade');
                $table->string('serial_number')->unique();
                // Kiểm tra xem bảng inventory_items đã tồn tại chưa
                if (Schema::hasTable('inventory_items')) {
                    $table->foreignId('inventory_item_id')->nullable()->constrained()->onDelete('set null');
                }
                $table->unsignedBigInteger('purchase_order_item_id')->nullable();
                $table->foreignId('warehouse_id')->nullable()->constrained()->onDelete('set null');
                $table->foreignId('warehouse_area_id')->nullable()->constrained()->onDelete('set null');
                $table->enum('status', ['available', 'sold', 'reserved', 'defective'])->default('available');
                $table->text('notes')->nullable();
                $table->softDeletes();
                $table->timestamps();

                // Thêm foreign key nếu bảng purchase_order_items đã tồn tại
                if (Schema::hasTable('purchase_order_items')) {
                    $table->foreign('purchase_order_item_id')->references('id')->on('purchase_order_items')->onDelete('set null');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('product_serials')) {
            Schema::table('product_serials', function (Blueprint $table) {
                if (Schema::hasColumn('product_serials', 'purchase_order_item_id')) {
                    // Kiểm tra xem có foreign key không trước khi xóa
                    $foreignKeys = Schema::getConnection()->getDoctrineSchemaManager()->listTableForeignKeys('product_serials');
                    $hasForeignKey = false;

                    foreach ($foreignKeys as $foreignKey) {
                        if (in_array('purchase_order_item_id', $foreignKey->getLocalColumns())) {
                            $hasForeignKey = true;
                            break;
                        }
                    }

                    if ($hasForeignKey) {
                        $table->dropForeign(['purchase_order_item_id']);
                    }

                    $table->dropColumn('purchase_order_item_id');
                }
            });
        }
    }
};
