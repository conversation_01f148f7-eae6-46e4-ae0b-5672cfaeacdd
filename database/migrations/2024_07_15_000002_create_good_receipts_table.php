<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('good_receipts', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique()->comment('Mã phiếu nhập kho');
            $table->foreignId('purchase_order_id')->nullable()->constrained('purchase_orders')->onDelete('set null');
            $table->foreignId('warehouse_id')->constrained('warehouses')->onDelete('cascade');
            $table->enum('status', ['completed', 'cancelled'])->default('completed')->comment('Trạng thái phiếu nhập kho');
            $table->text('notes')->nullable()->comment('Ghi chú');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('good_receipt_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('good_receipt_id')->constrained('good_receipts')->onDelete('cascade');
            $table->foreignId('purchase_order_item_id')->nullable()->constrained('purchase_order_items')->onDelete('set null');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignId('warehouse_area_id')->nullable()->constrained('warehouse_areas')->onDelete('set null');
            $table->decimal('quantity', 10, 2)->comment('Số lượng nhập');
            $table->decimal('unit_price', 15, 2)->default(0)->comment('Đơn giá');
            $table->string('batch_number')->nullable()->comment('Số lô');
            $table->date('expiry_date')->nullable()->comment('Hạn sử dụng');
            $table->text('notes')->nullable()->comment('Ghi chú');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('good_receipt_items');
        Schema::dropIfExists('good_receipts');
    }
};
