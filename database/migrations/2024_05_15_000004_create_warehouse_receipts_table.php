<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_receipts', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique()->comment('Mã phiếu nhận hàng');
            $table->foreignId('warehouse_transfer_id')->constrained()->onDelete('cascade')->comment('Phiếu chuyển kho');
            $table->foreignId('warehouse_id')->constrained()->onDelete('cascade')->comment('Kho nhận');
            $table->enum('status', ['pending', 'completed', 'cancelled'])->default('pending')->comment('Trạng thái phiếu nhận hàng');
            $table->text('notes')->nullable()->comment('<PERSON><PERSON> chú');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('completed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('completed_at')->nullable();
            $table->foreignId('cancelled_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('cancelled_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_receipts');
    }
};
