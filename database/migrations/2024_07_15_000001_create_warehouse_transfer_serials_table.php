<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_transfer_serials', function (Blueprint $table) {
            $table->id();
            $table->foreignId('warehouse_transfer_id')->constrained()->onDelete('cascade')->comment('Phiếu chuyển kho');
            $table->foreignId('warehouse_transfer_item_id')->constrained()->onDelete('cascade')->comment('Chi tiết phiếu chuyển kho');
            $table->foreignId('product_id')->constrained()->onDelete('cascade')->comment('Sản phẩm');
            $table->foreignId('product_serial_id')->constrained()->onDelete('cascade')->comment('Serial/IMEI');
            $table->string('serial_number')->comment('Số serial/IMEI');
            $table->enum('status', ['pending', 'transferred', 'received'])->default('pending')->comment('Trạng thái');
            $table->timestamps();
            $table->softDeletes();
            
            // Thêm index
            $table->index('warehouse_transfer_id');
            $table->index('warehouse_transfer_item_id');
            $table->index('product_id');
            $table->index('product_serial_id');
            $table->index('serial_number');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_transfer_serials');
    }
};
