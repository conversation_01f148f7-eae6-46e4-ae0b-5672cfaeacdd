<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transfer_receipt_batches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('transfer_receipt_item_id')->constrained()->onDelete('cascade');
            $table->string('batch_number')->comment('Số lô');
            $table->decimal('quantity', 15, 2)->comment('Số lượng');
            $table->date('expiry_date')->nullable()->comment('<PERSON><PERSON><PERSON> hết hạn');
            $table->timestamps();

            // Index để tăng hiệu suất truy vấn
            $table->index(['transfer_receipt_item_id', 'batch_number'], 'tr_batch_item_batch_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transfer_receipt_batches');
    }
};
