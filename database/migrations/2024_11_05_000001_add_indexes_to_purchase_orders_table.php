<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            // Thêm index cho các trường tìm kiếm và sắp xếp
            $table->index('code');
            $table->index('warehouse_id');
            $table->index('supplier_id');
            $table->index('status');
            $table->index('created_at');
            $table->index('expected_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            // Xóa index
            $table->dropIndex(['code']);
            $table->dropIndex(['warehouse_id']);
            $table->dropIndex(['supplier_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['created_at']);
            $table->dropIndex(['expected_date']);
        });
    }
};
