<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\GoodReceipt;
use App\Models\GoodReceiptInvoice;
use App\Models\GoodReceiptAttachment;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate data from good_receipts to good_receipt_invoices
        $goodReceipts = GoodReceipt::whereNotNull('invoice_number')->get();

        foreach ($goodReceipts as $goodReceipt) {
            // Create new invoice
            $invoice = new GoodReceiptInvoice([
                'good_receipt_id' => $goodReceipt->id,
                'invoice_number' => $goodReceipt->invoice_number,
                'invoice_series' => $goodReceipt->invoice_series,
                'invoice_template' => $goodReceipt->invoice_template,
                'invoice_date' => $goodReceipt->invoice_date,
                'notes' => null,
                'created_by' => $goodReceipt->created_by,
            ]);

            $invoice->save();

            // Update attachments to link to the new invoice
            GoodReceiptAttachment::where('good_receipt_id', $goodReceipt->id)
                ->update(['invoice_id' => $invoice->id]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be reversed
    }
};
