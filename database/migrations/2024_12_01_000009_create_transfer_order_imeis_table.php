<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transfer_order_imeis', function (Blueprint $table) {
            $table->id();
            $table->foreignId('transfer_order_item_id')->constrained()->onDelete('cascade');
            $table->string('imei')->comment('Số IMEI');
            $table->foreignId('scanned_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('scanned_at')->nullable();
            $table->timestamps();

            // Index để tăng hiệu suất truy vấn
            $table->index(['transfer_order_item_id', 'imei'], 'to_imei_item_imei_idx');
            $table->unique(['transfer_order_item_id', 'imei'], 'to_imei_item_imei_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transfer_order_imeis');
    }
};
