<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventory_transactions', function (Blueprint $table) {
            $table->foreignId('product_batch_id')->nullable()->after('batch_number')->constrained('product_batches');

            // Thêm index cho product_batch_id
            $table->index('product_batch_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventory_transactions', function (Blueprint $table) {
            // Xóa index trước
            $table->dropIndex(['product_batch_id']);

            // Xóa foreign key và cột
            $table->dropForeign(['product_batch_id']);
            $table->dropColumn('product_batch_id');
        });
    }
};
