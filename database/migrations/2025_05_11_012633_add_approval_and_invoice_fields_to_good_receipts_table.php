<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('good_receipts', function (Blueprint $table) {
            // Thêm các trường liên quan đến duyệt phiếu
            $table->foreignId('approved_by')->nullable()->after('created_by');
            $table->timestamp('approved_at')->nullable()->after('approved_by');

            // Thêm các trường liên quan đến hóa đơn
            $table->string('invoice_number')->nullable()->after('notes');
            $table->string('invoice_series')->nullable()->after('invoice_number');
            $table->string('invoice_template')->nullable()->after('invoice_series');
            $table->date('invoice_date')->nullable()->after('invoice_template');
            $table->boolean('has_documents')->default(false)->after('invoice_date');

            // Cập nhật trường status để hỗ trợ các trạng thái mới
            $table->dropColumn('status');
            $table->enum('status', ['draft', 'pending', 'completed', 'cancelled'])->default('draft')->after('warehouse_id');

            // Thêm foreign key cho approved_by
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('good_receipts', function (Blueprint $table) {
            // Xóa các trường đã thêm
            $table->dropForeign(['approved_by']);
            $table->dropColumn([
                'approved_by',
                'approved_at',
                'invoice_number',
                'invoice_series',
                'invoice_template',
                'invoice_date',
                'has_documents',
                'status'
            ]);

            // Thêm lại trường status với các giá trị cũ
            $table->enum('status', ['completed', 'cancelled'])->default('completed')->after('warehouse_id');
        });
    }
};
