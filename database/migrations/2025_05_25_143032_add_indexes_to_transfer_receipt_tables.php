<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to transfer_receipt_batches table
        Schema::table('transfer_receipt_batches', function (Blueprint $table) {
            $table->index(['transfer_receipt_item_id', 'batch_number'], 'tr_batch_item_batch_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transfer_receipt_batches', function (Blueprint $table) {
            $table->dropIndex('tr_batch_item_batch_idx');
        });
    }
};
