<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_transfer_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('warehouse_transfer_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('source_warehouse_area_id')->nullable()->constrained('warehouse_areas')->onDelete('set null');
            $table->foreignId('destination_warehouse_area_id')->nullable()->constrained('warehouse_areas')->onDelete('set null');
            $table->decimal('quantity', 15, 2)->comment('Số lượng chuyển');
            $table->decimal('received_quantity', 15, 2)->default(0)->comment('Số lượng đã nhận');
            $table->string('batch_number', 50)->nullable()->comment('Số lô');
            $table->date('expiry_date')->nullable()->comment('Ngày hết hạn');
            $table->text('notes')->nullable()->comment('Ghi chú');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_transfer_items');
    }
};
