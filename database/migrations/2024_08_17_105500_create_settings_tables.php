<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('settings', function (Blueprint $table) {
      $table->id();
      //INVOICE
      $table->string('invoice_currency')->nullable();
      $table->string('invoice_name')->nullable();
      $table->string('invoice_website')->nullable();
      $table->string('invoice_address')->nullable();
      $table->string('invoice_city')->nullable();
      $table->string('invoice_state')->nullable();
      $table->string('invoice_postal')->nullable();
      $table->string('invoice_country')->nullable();
      $table->string('invoice_phone')->nullable();
      $table->string('invoice_vat')->nullable();

      //GLOBAL SETTINGS
      $table->string('site_name')->nullable()->default('WMS');
      $table->string('site_url')->nullable()->default('https://wms.1digital.vn');
      $table->string('site_email')->nullable();
      $table->string('google_analytics_active')->default(0);
      $table->text('google_analytics_code')->nullable();
      $table->string('logo')->default('logo.svg')->nullable();
      $table->string('logo_path')->default('logo.svg')->nullable();
      $table->string('favicon')->nullable();
      $table->string('favicon_path')->nullable();
      $table->text('meta_title')->nullable();
      $table->text('meta_description')->nullable();

      //SOCIAL LOGIN
      //fb
      $table->boolean('facebook_active')->default(0);
      $table->text('facebook_api_key')->nullable();
      $table->text('facebook_api_secret')->nullable();
      $table->text('facebook_redirect_url')->nullable();
      //github
      $table->boolean('github_active')->default(0);
      $table->text('github_api_key')->nullable();
      $table->text('github_api_secret')->nullable();
      $table->text('github_redirect_url')->nullable();
      //google
      $table->boolean('google_active')->default(0);
      $table->text('google_api_key')->nullable();
      $table->text('google_api_secret')->nullable();
      $table->text('google_redirect_url')->nullable();
      //twitter
      $table->boolean('twitter_active')->default(0);
      $table->text('twitter_api_key')->nullable();
      $table->text('twitter_api_secret')->nullable();
      $table->text('twitter_redirect_url')->nullable();

      //REGISTER
      $table->boolean('register_active')->default(1);
      $table->string('default_country')->default('Viet Nam');

      //SMTP
      $table->string('smtp_host')->nullable();
      $table->string('smtp_port')->nullable();
      $table->string('smtp_username')->nullable();
      $table->string('smtp_password')->nullable();
      $table->string('smtp_sender_email')->nullable();
      $table->string('smtp_sender_name')->nullable();
      $table->string('smtp_encryption')->nullable()->default('TLS');

      //VERSION
      $table->double('script_version')->default('1.0');

      //Recapcha
      $table->boolean('recaptcha_login')->default(false);
      $table->boolean('recaptcha_register')->default(false);
      $table->string('recaptcha_sitekey')->nullable();
      $table->string('recaptcha_secretkey')->nullable();
      //OTP
      $table->boolean('login_with_otp')->default(false);
      //Notification
      $table->boolean('push_notification')->default(false);
      $table->string('push_app_id')->nullable();
      $table->string('push_api_key')->nullable();
      $table->string('push_secret_key')->nullable();
      $table->string('push_cluster')->nullable();

      $table->boolean('reverb_notification')->default(false);
      $table->string('reverb_app_id')->nullable();
      $table->string('reverb_api_key')->nullable();
      $table->string('reverb_secret_key')->nullable();
      $table->string('reverb_host')->nullable();
      $table->string('reverb_port')->nullable();
      $table->string('reverb_scheme')->nullable();

      $table->boolean('slack_notification')->default(false);
      //VNPT Production
      $table->boolean('vnpt_active')->default(false);
      $table->string('vnpt_public_service_endpoint')->nullable();
      $table->string('vnpt_portal_service_endpoint')->nullable();
      $table->string('vnpt_biz_service_endpoint')->nullable();
      $table->string('vnpt_username')->nullable();
      $table->string('vnpt_password')->nullable();
      $table->string('vnpt_account_username')->nullable();
      $table->string('vnpt_account_password')->nullable();
      $table->string('vnpt_pattern')->nullable();
      $table->string('vnpt_serial')->nullable();
      //VNPT Sandbox
      $table->boolean('vnpt_sandbox_active')->default(false);
      $table->string('vnpt_sandbox_public_service_endpoint')->nullable();
      $table->string('vnpt_sandbox_portal_service_endpoint')->nullable();
      $table->string('vnpt_sandbox_biz_service_endpoint')->nullable();
      $table->string('vnpt_sandbox_username')->nullable();
      $table->string('vnpt_sandbox_password')->nullable();
      $table->string('vnpt_sandbox_account_username')->nullable();
      $table->string('vnpt_sandbox_account_password')->nullable();
      $table->string('vnpt_sandbox_pattern')->nullable();
      $table->string('vnpt_sandbox_serial')->nullable();

      //Nhat tin production
      $table->boolean('nhattin_active')->default(false);
      $table->string('nhattin_host')->nullable();
      $table->integer('nhattin_parner_id')->nullable();
      $table->string('nhattin_username')->nullable();
      $table->string('nhattin_password')->nullable();
      $table->integer('nhattin_service_id')->nullable();
      $table->integer('nhattin_payment_method')->nullable();
      $table->string('nhattin_cod')->nullable();
      $table->integer('nhattin_cargo_type')->nullable();
      //Nhat tin sandbox
      $table->boolean('nhattin_sandbox_active')->default(false);
      $table->string('nhattin_sandbox_host')->nullable();
      $table->integer('nhattin_sandbox_parner_id')->nullable();
      $table->string('nhattin_sandbox_username')->nullable();
      $table->string('nhattin_sandbox_password')->nullable();
      $table->integer('nhattin_sandbox_service_id')->nullable();
      $table->integer('nhattin_sandbox_payment_method')->nullable();
      $table->string('nhattin_sandbox_cod')->nullable();
      $table->integer('nhattin_sandbox_cargo_type')->nullable();
      //Nhat tin sender
      $table->string('nhattin_sender_name')->nullable();
      $table->string('nhattin_sender_phone')->nullable();
      $table->string('nhattin_sender_address')->nullable();
      $table->string('nhattin_ufm_source')->nullable();

      //SAP production
      $table->boolean('sap_active')->default(false);
      $table->string('sap_server')->nullable();
      $table->string('sap_database')->nullable();
      $table->string('sap_username')->nullable();
      $table->string('sap_password')->nullable();
      //SAP sandbox
      $table->boolean('sap_sandbox_active')->default(false);
      $table->string('sap_sandbox_server')->nullable();
      $table->string('sap_sandbox_database')->nullable();
      $table->string('sap_sandbox_username')->nullable();
      $table->string('sap_sandbox_password')->nullable();

      //Zalo
      $table->boolean('zalo_active')->default(false);
      $table->string('zalo_host')->nullable();
      $table->string('zalo_redirect_url')->nullable();
      $table->integer('zalo_app_id')->nullable();
      $table->string('zalo_app_secret')->nullable();
      $table->string('zalo_code_verifier')->nullable();

      //ASUS production
      $table->boolean('asus_active')->default(false);
      $table->string('asus_host')->nullable();
      $table->string('asus_token')->nullable();
      //ASUS sandbox
      $table->boolean('asus_sandbox_active')->default(false);
      $table->string('asus_sandbox_host')->nullable();
      $table->string('asus_sandbox_token')->nullable();

      $table->timestamps();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('settings');
  }
};
