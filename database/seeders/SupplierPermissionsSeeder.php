<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class SupplierPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create supplier permissions
        $permissions = [
            'suppliers.list' => 'Danh sách nhà cung cấp',
            'suppliers.create' => 'Thêm nhà cung cấp',
            'suppliers.view' => 'Xem chi tiết nhà cung cấp',
            'suppliers.edit' => 'Sửa nhà cung cấp',
            'suppliers.delete' => 'Xóa nhà cung cấp',
        ];

        foreach ($permissions as $permission => $description) {
            Permission::firstOrCreate(['name' => $permission], [
                'guard_name' => 'web'
            ]);
        }

        // Assign permissions to super admin role
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if ($superAdminRole) {
            $superAdminRole->givePermissionTo(array_keys($permissions));
        }
    }
}
