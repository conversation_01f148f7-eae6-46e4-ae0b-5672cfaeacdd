<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class WarehouseTransferPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define permissions
        $permissions = [
            // Warehouse Transfer Requests
            'warehouses.transfer_requests.view' => 'Xem yêu cầu chuyển hàng',
            'warehouses.transfer_requests.create' => 'Tạo yêu cầu chuyển hàng',
            'warehouses.transfer_requests.edit' => 'Sửa yêu cầu chuyển hàng',
            'warehouses.transfer_requests.delete' => 'Xóa yêu cầu chuyển hàng',
            'warehouses.transfer_requests.approve' => 'Duyệt yêu cầu chuyển hàng',

            // Warehouse Transfers
            'warehouses.transfers.view' => 'Xem phiếu chuyển kho',
            'warehouses.transfers.create' => 'Tạo phiếu chuyển kho',
            'warehouses.transfers.edit' => 'Sửa phiếu chuyển kho',
            'warehouses.transfers.delete' => 'Xóa phiếu chuyển kho',
            'warehouses.transfers.approve' => 'Duyệt phiếu chuyển kho',

            // Warehouse Receipts
            'warehouses.receipts.view' => 'Xem phiếu nhận hàng',
            'warehouses.receipts.edit' => 'Xác nhận phiếu nhận hàng',
        ];

        foreach ($permissions as $permission => $description) {
            Permission::firstOrCreate(['name' => $permission], [
                'guard_name' => 'web'
            ]);
        }

        // Assign permissions to super admin role
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if ($superAdminRole) {
            $superAdminRole->givePermissionTo(array_keys($permissions));
        }
    }
}
