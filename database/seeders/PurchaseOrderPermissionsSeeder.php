<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PurchaseOrderPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create purchase order permissions
        $permissions = [
            'warehouses.purchase-orders.view' => 'Xem phiếu nhập hàng',
            'warehouses.purchase-orders.create' => 'Tạo phiếu nhập hàng',
            'warehouses.purchase-orders.edit' => 'Sửa phiếu nhập hàng',
            'warehouses.purchase-orders.approve' => 'Duyệt phiếu nhập hàng',
            'warehouses.purchase-orders.receive' => 'Nhập kho theo phiếu',
        ];

        foreach ($permissions as $permission => $description) {
            Permission::firstOrCreate(['name' => $permission], [
                'guard_name' => 'web'
            ]);
        }

        // Assign permissions to super admin role
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if ($superAdminRole) {
            $superAdminRole->givePermissionTo(array_keys($permissions));
        }
    }
}
