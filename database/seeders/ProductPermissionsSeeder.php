<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class ProductPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create product permissions
        $permissions = [
            // Basic product management
            'products.list' => 'Xem danh sách sản phẩm',
            'products.create' => 'Thêm sản phẩm mới',
            'products.edit' => 'Chỉnh sửa sản phẩm',
            'products.delete' => 'Xóa sản phẩm',
            'products.import' => 'Nhập sản phẩm từ file',
            'products.export' => 'Xuất sản phẩm ra file',

            // Category management
            'categories.list' => 'Xem danh sách danh mục',
            'categories.create' => 'Thêm danh mục mới',
            'categories.edit' => 'Chỉnh sửa danh mục',
            'categories.delete' => 'Xóa danh mục',

            // Brand management
            'brands.list' => 'Xem danh sách thương hiệu',
            'brands.create' => 'Thêm thương hiệu mới',
            'brands.edit' => 'Chỉnh sửa thương hiệu',
            'brands.delete' => 'Xóa thương hiệu',
        ];

        foreach ($permissions as $permission => $description) {
            Permission::firstOrCreate(['name' => $permission], [
                'guard_name' => 'web'
            ]);
        }

        // Assign permissions to super admin role
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if ($superAdminRole) {
            $superAdminRole->givePermissionTo(array_keys($permissions));
        }
    }
}
