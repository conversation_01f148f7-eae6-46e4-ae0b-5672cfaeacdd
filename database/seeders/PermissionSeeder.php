<?php

namespace Database\Seeders;

use App\Enums\Permissions;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class PermissionSeeder extends Seeder
{
  /**
   * Run the database seeds.
   */
  public function run(): void
  {
    // Reset cached roles and permissions
    app()[PermissionRegistrar::class]->forgetCachedPermissions();

    // Get all permissions from enum
    $permissions = Permissions::getAll();

    // Create permissions
    foreach ($permissions as $permission) {
      Permission::findOrCreate($permission, 'web');
    }

    // Create roles and assign permissions
    $this->createRoles();
  }

  /**
   * Create permission
   */
  private function createPermission(string $name, string $guardName): void
  {
    Permission::query()
      ->firstOrCreate([
        'name' => $name,
        'guard_name' => $guardName,
      ]);
  }

  /**
   * Create roles and assign permissions
   */
  private function createRoles(): void
  {
    // Create super-admin role if it doesn't exist
    $superAdminRole = Role::firstOrCreate(['name' => 'super-admin']);

    // Create admin role if it doesn't exist
    $adminRole = Role::firstOrCreate(['name' => 'admin']);

    // Assign all permissions to admin role
    $adminRole->syncPermissions(Permission::all());

    // Create user role if it doesn't exist
    $userRole = Role::firstOrCreate(['name' => 'user']);

    // Assign basic permissions to user role
    $userRole->syncPermissions([
      Permissions::PRODUCTS_VIEW->value,
      Permissions::INVOICES_VIEW->value,
    ]);
  }
}
