<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Warehouse;
use App\Models\Product;
use App\Models\InventoryItem;
use App\Models\TransferRequest;
use App\Models\TransferRequestItem;
use App\Models\User;

class TransferSampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample warehouses if they don't exist
        $warehouses = [
            [
                'name' => 'Kho Hà Nội',
                'code' => 'WH-HN',
                'address' => 'Số 1 Đường ABC, Hà Nội',
                'is_active' => true,
                'is_transit' => false,
            ],
            [
                'name' => 'Kho TP.HCM',
                'code' => 'WH-HCM',
                'address' => 'Số 2 Đường XYZ, TP.HCM',
                'is_active' => true,
                'is_transit' => false,
            ],
            [
                'name' => 'Kho Trung Gian',
                'code' => 'WH-TRANSIT',
                'address' => 'Kho trung gian vận chuyển',
                'is_active' => true,
                'is_transit' => true,
            ],
        ];

        foreach ($warehouses as $warehouseData) {
            Warehouse::firstOrCreate(
                ['code' => $warehouseData['code']],
                $warehouseData
            );
        }

        // Create sample products if they don't exist
        $products = [
            [
                'name' => 'Laptop Dell Inspiron 15',
                'code' => 'LAPTOP-DELL-001',
                'barcode' => 'LAPTOP001',
                'unit' => 'Chiếc',
                'is_active' => true,
            ],
            [
                'name' => 'iPhone 15 Pro Max',
                'code' => 'IPHONE-15-PM',
                'barcode' => 'IPHONE001',
                'unit' => 'Chiếc',
                'is_active' => true,
            ],
            [
                'name' => 'Samsung Galaxy S24',
                'code' => 'SAMSUNG-S24',
                'barcode' => 'SAMSUNG001',
                'unit' => 'Chiếc',
                'is_active' => true,
            ],
            [
                'name' => 'Thuốc Paracetamol 500mg',
                'code' => 'PARA-500',
                'barcode' => 'PARA001',
                'unit' => 'Hộp',
                'is_active' => true,
            ],
            [
                'name' => 'Máy tính bảng iPad Air',
                'code' => 'IPAD-AIR',
                'barcode' => 'IPAD001',
                'unit' => 'Chiếc',
                'is_active' => true,
            ],
        ];

        foreach ($products as $productData) {
            Product::firstOrCreate(
                ['code' => $productData['code']],
                $productData
            );
        }

        // Create inventory items for warehouses
        $warehouseHN = Warehouse::where('code', 'WH-HN')->first();
        $warehouseHCM = Warehouse::where('code', 'WH-HCM')->first();
        $products = Product::all();

        foreach ($products as $product) {
            // Add inventory to Hanoi warehouse
            InventoryItem::firstOrCreate(
                [
                    'warehouse_id' => $warehouseHN->id,
                    'product_id' => $product->id,
                ],
                [
                    'quantity' => rand(50, 200),
                    'available_quantity' => rand(30, 150),
                    'reserved_quantity' => 0,
                ]
            );

            // Add inventory to HCM warehouse
            InventoryItem::firstOrCreate(
                [
                    'warehouse_id' => $warehouseHCM->id,
                    'product_id' => $product->id,
                ],
                [
                    'quantity' => rand(20, 100),
                    'available_quantity' => rand(10, 80),
                    'reserved_quantity' => 0,
                ]
            );
        }

        // Create sample transfer requests
        $user = User::first();
        if (!$user) {
            echo "No users found. Please create a user first.\n";
            return;
        }

        $transferRequests = [
            [
                'from_warehouse_id' => $warehouseHN->id,
                'to_warehouse_id' => $warehouseHCM->id,
                'notes' => 'Chuyển hàng từ Hà Nội xuống TP.HCM để bổ sung tồn kho',
                'status' => 'draft',
                'created_by' => $user->id,
            ],
            [
                'from_warehouse_id' => $warehouseHCM->id,
                'to_warehouse_id' => $warehouseHN->id,
                'notes' => 'Chuyển hàng từ TP.HCM lên Hà Nội theo yêu cầu khách hàng',
                'status' => 'pending',
                'created_by' => $user->id,
            ],
            [
                'from_warehouse_id' => $warehouseHN->id,
                'to_warehouse_id' => $warehouseHCM->id,
                'notes' => 'Chuyển hàng điện tử từ Hà Nội xuống TP.HCM',
                'status' => 'approved',
                'created_by' => $user->id,
                'approved_by' => $user->id,
                'approved_at' => now(),
            ],
        ];

        foreach ($transferRequests as $index => $requestData) {
            $requestData['code'] = TransferRequest::generateCode();
            $transferRequest = TransferRequest::create($requestData);

            // Add items to transfer request
            $selectedProducts = $products->random(rand(2, 4));
            foreach ($selectedProducts as $product) {
                $inventoryItem = InventoryItem::where('warehouse_id', $requestData['from_warehouse_id'])
                    ->where('product_id', $product->id)
                    ->first();

                if ($inventoryItem && $inventoryItem->available_quantity > 0) {
                    TransferRequestItem::create([
                        'transfer_request_id' => $transferRequest->id,
                        'product_id' => $product->id,
                        'quantity' => min(rand(1, 10), $inventoryItem->available_quantity),
                        'notes' => 'Sản phẩm ' . $product->name,
                    ]);
                }
            }
        }

        echo "Transfer sample data seeded successfully!\n";
        echo "Created:\n";
        echo "- " . count($warehouses) . " warehouses\n";
        echo "- " . count($products) . " products\n";
        echo "- " . ($products->count() * 2) . " inventory items\n";
        echo "- " . count($transferRequests) . " transfer requests\n";
    }
}
