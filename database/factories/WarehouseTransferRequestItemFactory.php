<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\WarehouseTransferRequest;
use App\Models\WarehouseTransferRequestItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class WarehouseTransferRequestItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = WarehouseTransferRequestItem::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'warehouse_transfer_request_id' => WarehouseTransferRequest::factory(),
            'product_id' => Product::factory(),
            'quantity' => $this->faker->numberBetween(1, 100),
            'transferred_quantity' => 0,
            'notes' => $this->faker->sentence,
        ];
    }
}
