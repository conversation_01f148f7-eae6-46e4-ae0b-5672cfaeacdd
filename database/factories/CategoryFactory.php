<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Category::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->unique()->words(rand(1, 3), true),
            'description' => fake()->paragraph(),
            'parent_id' => null,
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the category is a child category.
     *
     * @param int $parentId
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function child(int $parentId): Factory
    {
        return $this->state(function (array $attributes) use ($parentId) {
            return [
                'parent_id' => $parentId,
            ];
        });
    }
}
