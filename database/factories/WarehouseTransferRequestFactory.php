<?php

namespace Database\Factories;

use App\Models\Warehouse;
use App\Models\WarehouseTransferRequest;
use Illuminate\Database\Eloquent\Factories\Factory;

class WarehouseTransferRequestFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = WarehouseTransferRequest::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'code' => 'TR' . $this->faker->unique()->numberBetween(1000, 9999),
            'source_warehouse_id' => Warehouse::factory(),
            'destination_warehouse_id' => Warehouse::factory(),
            'request_date' => $this->faker->date(),
            'expected_date' => $this->faker->dateTimeBetween('+1 week', '+4 weeks')->format('Y-m-d'),
            'status' => $this->faker->randomElement(['draft', 'pending', 'approved', 'completed', 'cancelled']),
            'notes' => $this->faker->paragraph,
            'created_by' => 1,
        ];
    }
}
