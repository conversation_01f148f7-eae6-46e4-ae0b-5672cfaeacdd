<?php

namespace Database\Factories;

use App\Models\PurchaseOrder;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Factory;

class PurchaseOrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PurchaseOrder::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'code' => 'PO' . $this->faker->unique()->numberBetween(1000, 9999),
            'supplier_id' => 1,
            'warehouse_id' => Warehouse::factory(),
            'order_date' => $this->faker->date(),
            'expected_date' => $this->faker->dateTimeBetween('+1 week', '+4 weeks')->format('Y-m-d'),
            'status' => $this->faker->randomElement(['draft', 'pending', 'approved', 'completed', 'cancelled']),
            'notes' => $this->faker->paragraph,
            'created_by' => 1,
        ];
    }
}
