/**
 * Quét IMEI cho phiếu chuyển kho
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
  // DOM Elements
  const productSelect = document.getElementById('productSelect');
  const imeiInput = document.getElementById('imeiInput');
  const scanBtn = document.getElementById('scanBtn');
  const completeBtn = document.getElementById('completeBtn');
  const imeiTableBody = document.getElementById('imeiTableBody');
  const noImeiRow = document.getElementById('noImeiRow');

  // Variables
  let scannedImeis = [];

  // Initialize
  init();

  /**
   * Initialize
   */
  function init() {
    // Load scanned IMEIs
    loadScannedImeis();

    // Event listeners
    if (scanBtn) {
      scanBtn.addEventListener('click', scanImei);
    }

    if (imeiInput) {
      imeiInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          e.preventDefault();
          scanImei();
        }
      });
    }

    if (completeBtn) {
      completeBtn.addEventListener('click', completeImeiScan);
    }
  }

  /**
   * Load scanned IMEIs
   */
  function loadScannedImeis() {
    showLoading();

    fetch(`/warehouses/transfers/${window.transferId}/imeis`)
      .then(response => response.json())
      .then(data => {
        hideLoading();

        if (data.code === 200) {
          scannedImeis = data.data;
          renderImeiTable();
        }
      })
      .catch(error => {
        hideLoading();
        console.error('Error:', error);

        Swal.fire({
          title: 'Lỗi',
          text: 'Đã xảy ra lỗi khi tải danh sách IMEI đã quét',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
      });
  }

  /**
   * Render IMEI table
   */
  function renderImeiTable() {
    // Clear table
    imeiTableBody.innerHTML = '';

    if (scannedImeis.length === 0) {
      imeiTableBody.appendChild(noImeiRow);
      return;
    }

    // Render IMEIs
    scannedImeis.forEach((imei, index) => {
      const row = document.createElement('tr');

      row.innerHTML = `
        <td>${index + 1}</td>
        <td>${imei.product ? imei.product.name : 'N/A'}</td>
        <td>${imei.serial_number}</td>
        <td>${formatDate(imei.created_at)}</td>
        <td>
          <button type="button" class="btn btn-sm btn-icon btn-label-danger delete-imei" data-id="${imei.id}">
            <i class="ri-delete-bin-line"></i>
          </button>
        </td>
      `;

      imeiTableBody.appendChild(row);
    });

    // Add event listeners to delete buttons
    document.querySelectorAll('.delete-imei').forEach(button => {
      button.addEventListener('click', function() {
        const imeiId = this.dataset.id;
        deleteImei(imeiId);
      });
    });

    // Update product select options
    updateProductSelectOptions();
  }

  /**
   * Update product select options
   */
  function updateProductSelectOptions() {
    if (!productSelect) return;

    // Get all options
    const options = productSelect.querySelectorAll('option');

    // Skip the first option (placeholder)
    for (let i = 1; i < options.length; i++) {
      const option = options[i];
      const itemId = option.value;

      // Count scanned IMEIs for this item
      const scannedCount = scannedImeis.filter(imei => imei.warehouse_transfer_item_id == itemId).length;
      const quantity = parseInt(option.dataset.quantity);

      // Update option text
      option.textContent = `${option.textContent.split(' - ')[0]} - Đã quét: ${scannedCount}/${quantity}`;
      option.dataset.scanned = scannedCount;

      // Disable option if all IMEIs are scanned
      if (scannedCount >= quantity) {
        option.disabled = true;
      } else {
        option.disabled = false;
      }
    }
  }

  /**
   * Scan IMEI
   */
  function scanImei() {
    // Validate input
    if (!productSelect.value) {
      Swal.fire({
        title: 'Lỗi',
        text: 'Vui lòng chọn sản phẩm',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
      return;
    }

    if (!imeiInput.value) {
      Swal.fire({
        title: 'Lỗi',
        text: 'Vui lòng nhập IMEI',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
      return;
    }

    // Check if IMEI is already scanned
    const isScanned = scannedImeis.some(imei => imei.serial_number === imeiInput.value);

    if (isScanned) {
      Swal.fire({
        title: 'Lỗi',
        text: 'IMEI này đã được quét',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
      return;
    }

    // Send request
    showLoading();

    fetch(`/warehouses/transfers/${window.transferId}/scan-imei`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        serial_number: imeiInput.value,
        item_id: productSelect.value
      })
    })
      .then(response => response.json())
      .then(data => {
        hideLoading();

        if (data.code === 200) {
          // Clear input
          imeiInput.value = '';
          imeiInput.focus();

          // Add IMEI to list
          scannedImeis.push(data.data);

          // Render table
          renderImeiTable();

          // Show success message
          Swal.fire({
            title: 'Thành công',
            text: data.text,
            icon: 'success',
            customClass: {
              confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false,
            timer: 1500
          });
        } else {
          Swal.fire({
            title: 'Lỗi',
            text: data.text,
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
          });
        }
      })
      .catch(error => {
        hideLoading();
        console.error('Error:', error);

        Swal.fire({
          title: 'Lỗi',
          text: 'Đã xảy ra lỗi khi quét IMEI',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
      });
  }

  /**
   * Delete IMEI
   */
  function deleteImei(imeiId) {
    Swal.fire({
      title: 'Xác nhận',
      text: 'Bạn có chắc chắn muốn xóa IMEI này?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy',
      customClass: {
        confirmButton: 'btn btn-primary me-2',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(result => {
      if (result.isConfirmed) {
        showLoading();

        fetch(`/warehouses/transfers/${window.transferId}/imeis/${imeiId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
          .then(response => response.json())
          .then(data => {
            hideLoading();

            if (data.code === 200) {
              // Remove IMEI from list
              scannedImeis = scannedImeis.filter(imei => imei.id != imeiId);

              // Render table
              renderImeiTable();

              // Show success message
              Swal.fire({
                title: 'Thành công',
                text: data.text,
                icon: 'success',
                customClass: {
                  confirmButton: 'btn btn-primary'
                },
                buttonsStyling: false,
                timer: 1500
              });
            } else {
              Swal.fire({
                title: 'Lỗi',
                text: data.text,
                icon: 'error',
                customClass: {
                  confirmButton: 'btn btn-primary'
                },
                buttonsStyling: false
              });
            }
          })
          .catch(error => {
            hideLoading();
            console.error('Error:', error);

            Swal.fire({
              title: 'Lỗi',
              text: 'Đã xảy ra lỗi khi xóa IMEI',
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-primary'
              },
              buttonsStyling: false
            });
          });
      }
    });
  }

  /**
   * Complete IMEI scan
   */
  function completeImeiScan() {
    Swal.fire({
      title: 'Xác nhận',
      text: 'Bạn có chắc chắn muốn hoàn thành quét IMEI?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Hoàn thành',
      cancelButtonText: 'Hủy',
      customClass: {
        confirmButton: 'btn btn-primary me-2',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(result => {
      if (result.isConfirmed) {
        showLoading();

        fetch(`/warehouses/transfers/${window.transferId}/complete-imei-scan`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
          .then(response => response.json())
          .then(data => {
            hideLoading();

            if (data.code === 200) {
              Swal.fire({
                title: 'Thành công',
                text: data.text,
                icon: 'success',
                customClass: {
                  confirmButton: 'btn btn-primary'
                },
                buttonsStyling: false
              }).then(() => {
                if (data.redirect) {
                  window.location.href = data.redirect;
                }
              });
            } else {
              Swal.fire({
                title: 'Lỗi',
                text: data.text,
                icon: 'error',
                customClass: {
                  confirmButton: 'btn btn-primary'
                },
                buttonsStyling: false
              });
            }
          })
          .catch(error => {
            hideLoading();
            console.error('Error:', error);

            Swal.fire({
              title: 'Lỗi',
              text: 'Đã xảy ra lỗi khi hoàn thành quét IMEI',
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-primary'
              },
              buttonsStyling: false
            });
          });
      }
    });
  }

  /**
   * Format date
   */
  function formatDate(dateString) {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    return date.toLocaleString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * Show loading
   */
  function showLoading() {
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
      button.disabled = true;
    });
  }

  /**
   * Hide loading
   */
  function hideLoading() {
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
      button.disabled = false;
    });
  }
});
