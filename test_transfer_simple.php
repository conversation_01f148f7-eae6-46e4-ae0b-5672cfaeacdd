<?php

echo "=== Testing Transfer System (Simple) ===\n";

// Test 1: Check if files exist
echo "1. Checking Files:\n";

$files = [
    'Models' => [
        'app/Models/TransferRequest.php',
        'app/Models/TransferOrder.php', 
        'app/Models/TransferReceipt.php',
        'app/Models/TransferRequestItem.php',
        'app/Models/TransferOrderItem.php',
        'app/Models/TransferReceiptItem.php'
    ],
    'Contracts' => [
        'app/Contracts/TransferRequestInterface.php',
        'app/Contracts/TransferOrderInterface.php',
        'app/Contracts/TransferReceiptInterface.php'
    ],
    'Repositories' => [
        'app/Repositories/TransferRequestRepository.php',
        'app/Repositories/TransferOrderRepository.php',
        'app/Repositories/TransferReceiptRepository.php'
    ],
    'Controllers' => [
        'app/Http/Controllers/Pages/TransferRequestController.php',
        'app/Http/Controllers/Pages/TransferOrderController.php',
        'app/Http/Controllers/Pages/TransferReceiptController.php'
    ],
    'Views' => [
        'resources/views/content/pages/transfers/transfer-requests/index.blade.php',
        'resources/views/content/pages/transfers/transfer-requests/create.blade.php',
        'resources/views/content/pages/transfers/transfer-requests/show.blade.php',
        'resources/views/content/pages/transfers/transfer-requests/edit.blade.php',
        'resources/views/content/pages/transfers/transfer-orders/index.blade.php',
        'resources/views/content/pages/transfers/transfer-orders/create.blade.php',
        'resources/views/content/pages/transfers/transfer-orders/show.blade.php',
        'resources/views/content/pages/transfers/transfer-receipts/index.blade.php',
        'resources/views/content/pages/transfers/transfer-receipts/create.blade.php'
    ],
    'JavaScript' => [
        'resources/js/pages/transfers/transfer-requests.js',
        'resources/js/pages/transfers/transfer-request-form.js',
        'resources/js/pages/transfers/transfer-request-detail.js',
        'resources/js/pages/transfers/transfer-orders.js',
        'resources/js/pages/transfers/transfer-order-form.js',
        'resources/js/pages/transfers/transfer-order-detail.js',
        'resources/js/pages/transfers/transfer-receipts.js',
        'resources/js/pages/transfers/transfer-receipt-form.js'
    ],
    'Migrations' => [
        'database/migrations/2024_05_15_000001_create_transfer_requests_table.php',
        'database/migrations/2024_05_15_000002_create_transfer_request_items_table.php',
        'database/migrations/2024_05_15_000003_create_transfer_orders_table.php',
        'database/migrations/2024_05_15_000004_create_transfer_order_items_table.php',
        'database/migrations/2024_05_15_000005_create_transfer_receipts_table.php',
        'database/migrations/2024_05_15_000006_create_transfer_receipt_items_table.php'
    ]
];

foreach ($files as $category => $fileList) {
    echo "   $category:\n";
    foreach ($fileList as $file) {
        $exists = file_exists($file);
        echo "     - " . basename($file) . ": " . ($exists ? "OK" : "MISSING") . "\n";
    }
}

// Test 2: Check syntax of PHP files
echo "\n2. Checking PHP Syntax:\n";
$phpFiles = array_merge(
    $files['Models'], 
    $files['Contracts'], 
    $files['Repositories'], 
    $files['Controllers']
);

foreach ($phpFiles as $file) {
    if (file_exists($file)) {
        $output = [];
        $return_var = 0;
        exec("php -l \"$file\" 2>&1", $output, $return_var);
        $status = ($return_var === 0) ? "OK" : "SYNTAX ERROR";
        echo "   - " . basename($file) . ": $status\n";
        if ($return_var !== 0) {
            echo "     Error: " . implode("\n     ", $output) . "\n";
        }
    }
}

// Test 3: Check if routes file contains transfer routes
echo "\n3. Checking Routes:\n";
if (file_exists('routes/panel.php')) {
    $routeContent = file_get_contents('routes/panel.php');
    $transferRoutes = [
        'transfer-requests' => strpos($routeContent, 'transfer-requests') !== false,
        'transfer-orders' => strpos($routeContent, 'transfer-orders') !== false,
        'transfer-receipts' => strpos($routeContent, 'transfer-receipts') !== false
    ];
    
    foreach ($transferRoutes as $route => $exists) {
        echo "   - $route: " . ($exists ? "OK" : "MISSING") . "\n";
    }
} else {
    echo "   - routes/panel.php: MISSING\n";
}

// Test 4: Check menu configuration
echo "\n4. Checking Menu:\n";
if (file_exists('resources/menu/verticalMenu.json')) {
    $menuContent = file_get_contents('resources/menu/verticalMenu.json');
    $hasTransferMenu = strpos($menuContent, 'transfer_management') !== false;
    echo "   - Transfer menu: " . ($hasTransferMenu ? "OK" : "MISSING") . "\n";
} else {
    echo "   - verticalMenu.json: MISSING\n";
}

// Test 5: Check permissions seeder
echo "\n5. Checking Permissions Seeder:\n";
if (file_exists('database/seeders/TransferPermissionsSeeder.php')) {
    echo "   - TransferPermissionsSeeder.php: OK\n";
} else {
    echo "   - TransferPermissionsSeeder.php: MISSING\n";
}

echo "\n=== Test Completed ===\n";
echo "Summary: Transfer system files have been created and are ready for testing.\n";
echo "Next steps:\n";
echo "1. Run 'php artisan migrate' to create database tables\n";
echo "2. Run 'php artisan db:seed --class=TransferPermissionsSeeder' to create permissions\n";
echo "3. Access the transfer system via the admin panel\n";
