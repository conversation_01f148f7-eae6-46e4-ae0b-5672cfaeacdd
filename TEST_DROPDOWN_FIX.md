# Test Plan - <PERSON><PERSON><PERSON><PERSON> phục vấn đề dropdown trong cột "Thao tác"

## Mục tiêu
<PERSON>m tra xem các thay đổi đã khắc phục được vấn đề hiển thị dropdown trong cột "Thao tác" của trang danh sách yêu cầu chuyển kho hay chưa.

## Các thay đổi đã thực hiện

### 1. JavaScript (DataTable Configuration)
- ✅ Thêm `width: '120px'` cho cột actions
- ✅ Thêm `className: 'text-center actions-column'`
- ✅ Cải thiện responsive priority cho tất cả các cột
- ✅ Thêm `scrollX: false` và `autoWidth: false`

### 2. CSS tùy chỉnh
- ✅ Tạo file `resources/css/pages/transfers/transfer-requests.css`
- ✅ Cố định width cho cột actions
- ✅ Style dropdown button và menu
- ✅ Xử lý positioning để tránh overflow
- ✅ Responsive design cho mobile/tablet
- ✅ Dark mode support
- ✅ Animation mượt mà

### 3. HTML Dropdown
- ✅ Thêm `aria-expanded="false"` cho accessibility
- ✅ Sử dụng `dropdown-menu-end` class

### 4. Blade Template
- ✅ Thêm CSS vào `@section('page-style')`

## Test Cases

### Test Case 1: Desktop (>1200px)
**Mục tiêu:** Kiểm tra hiển thị trên màn hình lớn
**Các bước:**
1. Mở trang `/admin/transfer-requests` trên desktop
2. Kiểm tra cột "Thao tác" có width cố định 140px
3. Click vào nút dropdown (3 chấm)
4. Kiểm tra dropdown menu hiển thị đúng vị trí
5. Kiểm tra các menu item hoạt động bình thường

**Kết quả mong đợi:**
- ✅ Cột actions có width cố định
- ✅ Dropdown không làm vỡ layout
- ✅ Menu hiển thị đúng vị trí (căn phải)
- ✅ Animation mượt mà

### Test Case 2: Tablet (768px-1200px)
**Mục tiêu:** Kiểm tra responsive design
**Các bước:**
1. Resize browser xuống 1024px
2. Kiểm tra cột "Thao tác" có width 120px
3. Test dropdown functionality
4. Kiểm tra menu không bị overflow

**Kết quả mong đợi:**
- ✅ Width cột actions điều chỉnh phù hợp
- ✅ Dropdown vẫn hoạt động tốt
- ✅ Không có thanh cuộn ngang

### Test Case 3: Mobile (≤768px)
**Mục tiêu:** Kiểm tra trên mobile
**Các bước:**
1. Resize browser xuống 480px
2. Kiểm tra cột "Thao tác" có width 60px
3. Test dropdown với kích thước nhỏ
4. Kiểm tra responsive table

**Kết quả mong đợi:**
- ✅ Cột actions thu nhỏ phù hợp
- ✅ Dropdown menu điều chỉnh size
- ✅ Table responsive hoạt động

### Test Case 4: Dark Mode
**Mục tiêu:** Kiểm tra dark theme
**Các bước:**
1. Chuyển sang dark mode
2. Kiểm tra màu sắc dropdown
3. Test hover effects
4. Kiểm tra contrast

**Kết quả mong đợi:**
- ✅ Dropdown có màu phù hợp với dark theme
- ✅ Hover effects hoạt động
- ✅ Text contrast đủ rõ

### Test Case 5: Functionality
**Mục tiêu:** Kiểm tra chức năng
**Các bước:**
1. Test từng menu item trong dropdown
2. Kiểm tra "Xem chi tiết"
3. Kiểm tra "Chỉnh sửa" (nếu có quyền)
4. Kiểm tra "Gửi duyệt", "Duyệt", "Từ chối"
5. Kiểm tra "Hủy"

**Kết quả mong đợi:**
- ✅ Tất cả menu items hoạt động bình thường
- ✅ Không có lỗi JavaScript
- ✅ Modal/redirect hoạt động đúng

### Test Case 6: Performance
**Mục tiêu:** Kiểm tra hiệu suất
**Các bước:**
1. Load trang với nhiều dữ liệu
2. Kiểm tra thời gian render
3. Test scroll performance
4. Kiểm tra memory usage

**Kết quả mong đợi:**
- ✅ Không ảnh hưởng đến performance
- ✅ Smooth scrolling
- ✅ Không memory leak

## Checklist trước khi deploy

### Code Quality
- [x] CSS được scope đúng (`.datatables-transfer-requests`)
- [x] JavaScript không conflict với code khác
- [x] Responsive breakpoints hợp lý
- [x] Dark mode được support

### Browser Compatibility
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### Device Testing
- [ ] Desktop (1920x1080)
- [ ] Laptop (1366x768)
- [ ] Tablet (768x1024)
- [ ] Mobile (375x667)

### Accessibility
- [x] Keyboard navigation
- [x] Screen reader support
- [x] ARIA attributes
- [x] Color contrast

## Rollback Plan

Nếu có vấn đề, có thể rollback bằng cách:

1. **Xóa CSS file:**
   ```bash
   rm resources/css/pages/transfers/transfer-requests.css
   ```

2. **Revert blade template:**
   ```php
   // Xóa dòng này trong index.blade.php
   @vite(['resources/css/pages/transfers/transfer-requests.css'])
   ```

3. **Revert JavaScript:**
   ```javascript
   // Xóa các thuộc tính đã thêm trong columnDefs
   width: '120px',
   className: 'text-center actions-column',
   responsivePriority: 3,
   ```

4. **Revert Controller:**
   ```php
   // Xóa aria-expanded và dropdown-menu-end
   ```

5. **Rebuild assets:**
   ```bash
   npm run build
   ```

## Notes

- File CSS được tạo riêng để dễ maintain
- Sử dụng CSS scope để tránh conflict
- Responsive design tuân theo breakpoints của Bootstrap 5
- Animation sử dụng CSS transform để tối ưu performance
- Dark mode tương thích với theme hiện tại
