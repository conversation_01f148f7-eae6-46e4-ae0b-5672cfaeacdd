<?php

namespace Tests\Unit\Controllers;

use App\Http\Controllers\Pages\ProductSerialController;
use App\Models\GoodReceipt;
use App\Models\GoodReceiptItem;
use App\Models\Product;
use App\Models\ProductSerial;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WarehouseArea;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class ProductSerialControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $controller;
    protected $warehouse;
    protected $warehouseArea;
    protected $product;
    protected $user;
    protected $purchaseOrder;
    protected $purchaseOrderItem;
    protected $goodReceipt;
    protected $goodReceiptItem;

    public function setUp(): void
    {
        parent::setUp();

        // Tạo controller
        $this->controller = new ProductSerialController();

        // Tạo dữ liệu mẫu
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $this->warehouse = Warehouse::factory()->create([
            'name' => 'Test Warehouse',
            'code' => 'TW001',
        ]);

        $this->warehouseArea = WarehouseArea::factory()->create([
            'name' => 'Test Area',
            'code' => 'TA001',
            'warehouse_id' => $this->warehouse->id,
        ]);

        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'code' => 'TP001',
            'unit' => 'pcs',
            'price' => 100000,
            'inventory_tracking_type' => 'serial',
        ]);

        // Tạo đơn đặt hàng
        $this->purchaseOrder = PurchaseOrder::factory()->create([
            'code' => 'PO001',
            'supplier_id' => 1,
            'warehouse_id' => $this->warehouse->id,
            'status' => PurchaseOrder::STATUS_APPROVED,
            'created_by' => $this->user->id,
        ]);

        // Tạo chi tiết đơn đặt hàng
        $this->purchaseOrderItem = PurchaseOrderItem::factory()->create([
            'purchase_order_id' => $this->purchaseOrder->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'unit_price' => 100000,
            'received_quantity' => 0,
        ]);

        // Tạo phiếu nhập kho
        $this->goodReceipt = GoodReceipt::factory()->create([
            'code' => 'GR001',
            'purchase_order_id' => $this->purchaseOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'status' => GoodReceipt::STATUS_PENDING,
            'notes' => 'Test notes',
            'created_by' => $this->user->id,
        ]);

        // Tạo chi tiết phiếu nhập kho
        $this->goodReceiptItem = GoodReceiptItem::factory()->create([
            'good_receipt_id' => $this->goodReceipt->id,
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'quantity' => 5,
            'unit_price' => 100000,
            'batch_number' => 'BATCH001',
            'expiry_date' => now()->addYear()->format('Y-m-d'),
        ]);
    }

    /**
     * Test kiểm tra IMEI
     */
    public function testCheckImei()
    {
        // Tạo IMEI mẫu
        $imei = '123456789012345';
        $serial = ProductSerial::create([
            'product_id' => $this->product->id,
            'serial_number' => $imei,
            'status' => 'available',
        ]);

        // Tạo request
        $request = new Request([
            'imei' => $imei,
        ]);

        // Gọi phương thức checkImei
        $response = $this->controller->checkImei($request);

        // Kiểm tra kết quả
        $this->assertEquals(200, $response->getStatusCode());
        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertTrue($data['exists']);
        $this->assertEquals($imei, $data['imei']);
    }

    /**
     * Test kiểm tra nhiều IMEI
     */
    public function testCheckMultipleImeis()
    {
        // Tạo IMEI mẫu
        $imei1 = '123456789012345';
        $imei2 = '987654321098765';
        $serial1 = ProductSerial::create([
            'product_id' => $this->product->id,
            'serial_number' => $imei1,
            'status' => 'available',
        ]);

        // Tạo request
        $request = new Request([
            'imeis' => [$imei1, $imei2],
        ]);

        // Gọi phương thức checkMultipleImeis
        $response = $this->controller->checkMultipleImeis($request);

        // Kiểm tra kết quả
        $this->assertEquals(200, $response->getStatusCode());
        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertCount(1, $data['existing_imeis']);
        $this->assertEquals($imei1, $data['existing_imeis'][0]);
        $this->assertEquals(2, $data['total_checked']);
        $this->assertEquals(1, $data['total_existing']);
    }

    /**
     * Test lấy danh sách IMEI theo purchase_order_item_id
     */
    public function testGetImeisByPurchaseOrderItemId()
    {
        // Tạo IMEI mẫu
        $imei1 = '123456789012345';
        $imei2 = '987654321098765';
        $serial1 = ProductSerial::create([
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'serial_number' => $imei1,
            'status' => 'available',
        ]);
        $serial2 = ProductSerial::create([
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'serial_number' => $imei2,
            'status' => 'available',
        ]);

        // Tạo request
        $request = new Request([
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
        ]);

        // Gọi phương thức getImeis
        $response = $this->controller->getImeis($request);

        // Kiểm tra kết quả
        $this->assertEquals(200, $response->getStatusCode());
        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertCount(2, $data['serials']);
        $this->assertEquals(2, $data['count']);
    }

    /**
     * Test lấy danh sách IMEI theo good_receipt_item_id
     */
    public function testGetImeisByGoodReceiptItemId()
    {
        // Tạo IMEI mẫu
        $imei1 = '123456789012345';
        $imei2 = '987654321098765';
        $serial1 = ProductSerial::create([
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'good_receipt_item_id' => $this->goodReceiptItem->id,
            'serial_number' => $imei1,
            'status' => 'available',
        ]);
        $serial2 = ProductSerial::create([
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'good_receipt_item_id' => $this->goodReceiptItem->id,
            'serial_number' => $imei2,
            'status' => 'available',
        ]);

        // Tạo request
        $request = new Request([
            'good_receipt_item_id' => $this->goodReceiptItem->id,
        ]);

        // Gọi phương thức getImeis
        $response = $this->controller->getImeis($request);

        // Kiểm tra kết quả
        $this->assertEquals(200, $response->getStatusCode());
        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertCount(2, $data['serials']);
        $this->assertEquals(2, $data['count']);
    }

    /**
     * Test lưu IMEI
     */
    public function testStoreImeis()
    {
        // Tạo request
        $request = new Request([
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'good_receipt_item_id' => $this->goodReceiptItem->id,
            'warehouse_id' => $this->warehouse->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'imeis' => ['123456789012345', '987654321098765'],
        ]);

        // Gọi phương thức storeImeis
        $response = $this->controller->storeImeis($request);

        // Kiểm tra kết quả
        $this->assertEquals(200, $response->getStatusCode());
        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertCount(2, $data['saved_imeis']);

        // Kiểm tra IMEI đã được lưu vào cơ sở dữ liệu
        $serials = ProductSerial::where('product_id', $this->product->id)
            ->where('purchase_order_item_id', $this->purchaseOrderItem->id)
            ->where('good_receipt_item_id', $this->goodReceiptItem->id)
            ->get();

        $this->assertCount(2, $serials);
        $this->assertEquals('123456789012345', $serials[0]->serial_number);
        $this->assertEquals('987654321098765', $serials[1]->serial_number);
    }

    /**
     * Test lưu IMEI khi không có good_receipt_item_id
     */
    public function testStoreImeisWithoutGoodReceiptItemId()
    {
        // Tạo request
        $request = new Request([
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'warehouse_id' => $this->warehouse->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'imeis' => ['123456789012345', '987654321098765'],
        ]);

        // Gọi phương thức storeImeis
        $response = $this->controller->storeImeis($request);

        // Kiểm tra kết quả
        $this->assertEquals(200, $response->getStatusCode());
        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertCount(2, $data['saved_imeis']);

        // Kiểm tra IMEI đã được lưu vào cơ sở dữ liệu
        $serials = ProductSerial::where('product_id', $this->product->id)
            ->where('purchase_order_item_id', $this->purchaseOrderItem->id)
            ->get();

        $this->assertCount(2, $serials);
        $this->assertEquals('123456789012345', $serials[0]->serial_number);
        $this->assertEquals('987654321098765', $serials[1]->serial_number);

        // Kiểm tra good_receipt_item_id đã được tìm và gán
        $this->assertEquals($this->goodReceiptItem->id, $serials[0]->good_receipt_item_id);
        $this->assertEquals($this->goodReceiptItem->id, $serials[1]->good_receipt_item_id);
    }

    /**
     * Test lưu IMEI trùng lặp
     */
    public function testStoreImeisWithDuplicateImei()
    {
        // Tạo IMEI mẫu
        $imei = '123456789012345';
        $serial = ProductSerial::create([
            'product_id' => $this->product->id,
            'serial_number' => $imei,
            'status' => 'available',
        ]);

        // Tạo request
        $request = new Request([
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'good_receipt_item_id' => $this->goodReceiptItem->id,
            'warehouse_id' => $this->warehouse->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'imeis' => [$imei, '987654321098765'],
        ]);

        // Gọi phương thức storeImeis
        $response = $this->controller->storeImeis($request);

        // Kiểm tra kết quả
        $this->assertEquals(400, $response->getStatusCode());
        $data = json_decode($response->getContent(), true);
        $this->assertFalse($data['success']);
        $this->assertStringContainsString('đã tồn tại', $data['message']);
        $this->assertCount(1, $data['existing_imeis']);
        $this->assertEquals($imei, $data['existing_imeis'][0]);
    }
}
