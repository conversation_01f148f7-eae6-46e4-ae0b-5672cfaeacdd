<?php

namespace Tests\Unit\Repositories;

use App\Models\GoodReceipt;
use App\Models\GoodReceiptItem;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use App\Models\Product;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WarehouseArea;
use App\Repositories\GoodReceiptRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class GoodReceiptRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected $goodReceiptRepository;
    protected $warehouse;
    protected $warehouseArea;
    protected $product;
    protected $user;
    protected $purchaseOrder;
    protected $purchaseOrderItem;

    public function setUp(): void
    {
        parent::setUp();

        // Tạo repository
        $this->goodReceiptRepository = new GoodReceiptRepository(new GoodReceipt());

        // Tạo dữ liệu mẫu
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $this->warehouse = Warehouse::factory()->create([
            'name' => 'Test Warehouse',
            'code' => 'TW001',
        ]);

        $this->warehouseArea = WarehouseArea::factory()->create([
            'name' => 'Test Area',
            'code' => 'TA001',
            'warehouse_id' => $this->warehouse->id,
        ]);

        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'code' => 'TP001',
            'unit' => 'pcs',
            'price' => 100000,
        ]);

        // Tạo đơn đặt hàng
        $this->purchaseOrder = PurchaseOrder::factory()->create([
            'code' => 'PO001',
            'supplier_id' => 1,
            'warehouse_id' => $this->warehouse->id,
            'status' => PurchaseOrder::STATUS_APPROVED,
            'created_by' => $this->user->id,
        ]);

        // Tạo chi tiết đơn đặt hàng
        $this->purchaseOrderItem = PurchaseOrderItem::factory()->create([
            'purchase_order_id' => $this->purchaseOrder->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'unit_price' => 100000,
            'received_quantity' => 0,
        ]);
    }

    /**
     * Test tạo phiếu nhập kho
     */
    public function testCreateGoodReceipt()
    {
        // Dữ liệu đầu vào
        $data = [
            'code' => 'GR001',
            'purchase_order_id' => $this->purchaseOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'status' => GoodReceipt::STATUS_PENDING,
            'notes' => 'Test notes',
            'created_by' => $this->user->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'purchase_order_item_id' => $this->purchaseOrderItem->id,
                    'warehouse_area_id' => $this->warehouseArea->id,
                    'quantity' => 5,
                    'unit_price' => 100000,
                    'batch_number' => 'BATCH001',
                    'expiry_date' => now()->addYear()->format('Y-m-d'),
                ]
            ]
        ];

        // Thực hiện tạo phiếu nhập kho
        $goodReceipt = $this->goodReceiptRepository->create($data);

        // Kiểm tra phiếu nhập kho đã được tạo
        $this->assertInstanceOf(GoodReceipt::class, $goodReceipt);
        $this->assertEquals('GR001', $goodReceipt->code);
        $this->assertEquals($this->purchaseOrder->id, $goodReceipt->purchase_order_id);
        $this->assertEquals($this->warehouse->id, $goodReceipt->warehouse_id);
        $this->assertEquals(GoodReceipt::STATUS_PENDING, $goodReceipt->status);
        $this->assertEquals('Test notes', $goodReceipt->notes);
        $this->assertEquals($this->user->id, $goodReceipt->created_by);

        // Kiểm tra chi tiết phiếu nhập kho
        $this->assertCount(1, $goodReceipt->items);
        $item = $goodReceipt->items->first();
        $this->assertEquals($this->product->id, $item->product_id);
        $this->assertEquals($this->purchaseOrderItem->id, $item->purchase_order_item_id);
        $this->assertEquals($this->warehouseArea->id, $item->warehouse_area_id);
        $this->assertEquals(5, $item->quantity);
        $this->assertEquals(100000, $item->unit_price);
        $this->assertEquals('BATCH001', $item->batch_number);
    }

    /**
     * Test duyệt phiếu nhập kho
     */
    public function testApproveGoodReceipt()
    {
        // Tạo phiếu nhập kho
        $goodReceipt = GoodReceipt::factory()->create([
            'code' => 'GR001',
            'purchase_order_id' => $this->purchaseOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'status' => GoodReceipt::STATUS_PENDING,
            'notes' => 'Test notes',
            'created_by' => $this->user->id,
        ]);

        // Tạo chi tiết phiếu nhập kho
        $goodReceiptItem = GoodReceiptItem::factory()->create([
            'good_receipt_id' => $goodReceipt->id,
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'quantity' => 5,
            'unit_price' => 100000,
            'batch_number' => 'BATCH001',
            'expiry_date' => now()->addYear()->format('Y-m-d'),
        ]);

        // Duyệt phiếu nhập kho
        $result = $this->goodReceiptRepository->approve($goodReceipt->id);

        // Kiểm tra kết quả
        $this->assertTrue($result);

        // Kiểm tra trạng thái phiếu nhập kho
        $goodReceipt->refresh();
        $this->assertEquals(GoodReceipt::STATUS_APPROVED, $goodReceipt->status);

        // Kiểm tra tồn kho đã được cập nhật
        $inventoryItem = InventoryItem::where('warehouse_id', $this->warehouse->id)
            ->where('product_id', $this->product->id)
            ->first();

        $this->assertNotNull($inventoryItem);
        $this->assertEquals(5, $inventoryItem->quantity);
        $this->assertEquals(5, $inventoryItem->available_quantity);

        // Kiểm tra giao dịch tồn kho đã được tạo
        $transaction = InventoryTransaction::where('warehouse_id', $this->warehouse->id)
            ->where('product_id', $this->product->id)
            ->where('reference_type', GoodReceipt::class)
            ->where('reference_id', $goodReceipt->id)
            ->first();

        $this->assertNotNull($transaction);
        $this->assertEquals(5, $transaction->quantity);
        $this->assertEquals('in', $transaction->transaction_type);

        // Kiểm tra số lượng đã nhận của đơn đặt hàng đã được cập nhật
        $this->purchaseOrderItem->refresh();
        $this->assertEquals(5, $this->purchaseOrderItem->received_quantity);
    }

    /**
     * Test xử lý race condition khi cập nhật tồn kho
     */
    public function testHandleRaceConditionWhenUpdatingInventory()
    {
        // Tạo phiếu nhập kho
        $goodReceipt = GoodReceipt::factory()->create([
            'code' => 'GR001',
            'purchase_order_id' => $this->purchaseOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'status' => GoodReceipt::STATUS_PENDING,
            'notes' => 'Test notes',
            'created_by' => $this->user->id,
        ]);

        // Tạo chi tiết phiếu nhập kho
        $goodReceiptItem = GoodReceiptItem::factory()->create([
            'good_receipt_id' => $goodReceipt->id,
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'quantity' => 5,
            'unit_price' => 100000,
            'batch_number' => 'BATCH001',
            'expiry_date' => now()->addYear()->format('Y-m-d'),
        ]);

        // Tạo một bản ghi tồn kho trước
        InventoryItem::create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'quantity' => 10,
            'available_quantity' => 10,
            'reserved_quantity' => 0,
        ]);

        // Duyệt phiếu nhập kho
        $result = $this->goodReceiptRepository->approve($goodReceipt->id);

        // Kiểm tra kết quả
        $this->assertTrue($result);

        // Kiểm tra tồn kho đã được cập nhật
        $inventoryItem = InventoryItem::where('warehouse_id', $this->warehouse->id)
            ->where('product_id', $this->product->id)
            ->first();

        $this->assertNotNull($inventoryItem);
        $this->assertEquals(15, $inventoryItem->quantity); // 10 + 5
        $this->assertEquals(15, $inventoryItem->available_quantity);
    }

    /**
     * Test xử lý lỗi Duplicate entry khi cập nhật tồn kho
     */
    public function testHandleDuplicateEntryErrorWhenUpdatingInventory()
    {
        // Tạo phiếu nhập kho
        $goodReceipt = GoodReceipt::factory()->create([
            'code' => 'GR001',
            'purchase_order_id' => $this->purchaseOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'status' => GoodReceipt::STATUS_PENDING,
            'notes' => 'Test notes',
            'created_by' => $this->user->id,
        ]);

        // Tạo chi tiết phiếu nhập kho
        $goodReceiptItem = GoodReceiptItem::factory()->create([
            'good_receipt_id' => $goodReceipt->id,
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'quantity' => 5,
            'unit_price' => 100000,
            'batch_number' => 'BATCH001',
            'expiry_date' => now()->addYear()->format('Y-m-d'),
        ]);

        // Mô phỏng lỗi Duplicate entry bằng cách tạo một bản ghi tồn kho trước
        InventoryItem::create([
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'quantity' => 10,
            'available_quantity' => 10,
            'reserved_quantity' => 0,
        ]);

        // Mô phỏng race condition bằng cách tạo một transaction giả
        DB::shouldReceive('transaction')
            ->once()
            ->andThrow(new \Exception('Integrity constraint violation: 1062 Duplicate entry'));

        // Duyệt phiếu nhập kho
        $result = $this->goodReceiptRepository->approve($goodReceipt->id);

        // Kiểm tra kết quả
        $this->assertTrue($result);

        // Kiểm tra tồn kho đã được cập nhật
        $inventoryItem = InventoryItem::where('warehouse_id', $this->warehouse->id)
            ->where('product_id', $this->product->id)
            ->first();

        $this->assertNotNull($inventoryItem);
        $this->assertEquals(15, $inventoryItem->quantity); // 10 + 5
        $this->assertEquals(15, $inventoryItem->available_quantity);
    }

    /**
     * Test tạo nhiều phiếu nhập kho cùng lúc
     */
    public function testCreateMultipleGoodReceiptsSimultaneously()
    {
        // Tạo 5 phiếu nhập kho cùng lúc
        $goodReceipts = [];
        for ($i = 1; $i <= 5; $i++) {
            $data = [
                'code' => 'GR00' . $i,
                'purchase_order_id' => $this->purchaseOrder->id,
                'warehouse_id' => $this->warehouse->id,
                'status' => GoodReceipt::STATUS_PENDING,
                'notes' => 'Test notes ' . $i,
                'created_by' => $this->user->id,
                'items' => [
                    [
                        'product_id' => $this->product->id,
                        'purchase_order_item_id' => $this->purchaseOrderItem->id,
                        'warehouse_area_id' => $this->warehouseArea->id,
                        'quantity' => 2,
                        'unit_price' => 100000,
                        'batch_number' => 'BATCH00' . $i,
                        'expiry_date' => now()->addYear()->format('Y-m-d'),
                    ]
                ]
            ];

            $goodReceipts[] = $this->goodReceiptRepository->create($data);
        }

        // Kiểm tra 5 phiếu nhập kho đã được tạo
        $this->assertCount(5, $goodReceipts);

        // Duyệt 5 phiếu nhập kho cùng lúc
        foreach ($goodReceipts as $goodReceipt) {
            $result = $this->goodReceiptRepository->approve($goodReceipt->id);
            $this->assertTrue($result);
        }

        // Kiểm tra tồn kho đã được cập nhật
        $inventoryItem = InventoryItem::where('warehouse_id', $this->warehouse->id)
            ->where('product_id', $this->product->id)
            ->first();

        $this->assertNotNull($inventoryItem);
        $this->assertEquals(10, $inventoryItem->quantity); // 5 phiếu x 2 sản phẩm
        $this->assertEquals(10, $inventoryItem->available_quantity);

        // Kiểm tra số lượng đã nhận của đơn đặt hàng đã được cập nhật
        $this->purchaseOrderItem->refresh();
        $this->assertEquals(10, $this->purchaseOrderItem->received_quantity);
    }
}
