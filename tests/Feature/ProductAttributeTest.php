<?php

namespace Tests\Feature;

use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\Product;
use App\Models\ProductAttribute;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ProductAttributeTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $product;
    protected $attribute;
    protected $attributeValue;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user
        $this->user = User::factory()->create();

        // Create a product
        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'code' => 'TEST-001',
            'price' => 100,
            'is_active' => true,
        ]);

        // Create an attribute
        $this->attribute = Attribute::create([
            'name' => 'Color',
            'code' => 'color',
            'type' => 'select',
            'is_required' => false,
            'is_filterable' => true,
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Create an attribute value
        $this->attributeValue = AttributeValue::create([
            'attribute_id' => $this->attribute->id,
            'value' => 'Red',
            'display_value' => 'Red',
            'sort_order' => 1,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_can_get_available_attributes()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/attributes/available');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'code',
                        'type',
                        'activeValues',
                    ],
                ],
            ]);
    }

    /** @test */
    public function it_can_add_attribute_to_product()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson("/api/products/{$this->product->id}/attributes", [
                'attribute_id' => $this->attribute->id,
                'attribute_value_id' => $this->attributeValue->id,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'product_id',
                    'attribute_id',
                    'attribute_name',
                    'attribute_code',
                    'attribute_type',
                    'attribute_value_id',
                    'text_value',
                ],
            ]);

        $this->assertDatabaseHas('product_attributes', [
            'product_id' => $this->product->id,
            'attribute_id' => $this->attribute->id,
            'attribute_value_id' => $this->attributeValue->id,
        ]);
    }

    /** @test */
    public function it_can_get_product_attributes()
    {
        // Add an attribute to the product
        ProductAttribute::create([
            'product_id' => $this->product->id,
            'attribute_id' => $this->attribute->id,
            'attribute_value_id' => $this->attributeValue->id,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson("/api/products/{$this->product->id}/attributes");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'product_id',
                        'attribute_id',
                        'attribute_name',
                        'attribute_code',
                        'attribute_type',
                        'attribute_value_id',
                        'attribute_value',
                        'text_value',
                        'display_value',
                    ],
                ],
            ]);
    }

    /** @test */
    public function it_can_update_product_attribute()
    {
        // Add an attribute to the product
        ProductAttribute::create([
            'product_id' => $this->product->id,
            'attribute_id' => $this->attribute->id,
            'attribute_value_id' => $this->attributeValue->id,
        ]);

        // Create another attribute value
        $newAttributeValue = AttributeValue::create([
            'attribute_id' => $this->attribute->id,
            'value' => 'Blue',
            'display_value' => 'Blue',
            'sort_order' => 2,
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->putJson("/api/products/{$this->product->id}/attributes/{$this->attribute->id}", [
                'attribute_value_id' => $newAttributeValue->id,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'product_id',
                    'attribute_id',
                    'attribute_name',
                    'attribute_code',
                    'attribute_type',
                    'attribute_value_id',
                    'text_value',
                ],
            ]);

        $this->assertDatabaseHas('product_attributes', [
            'product_id' => $this->product->id,
            'attribute_id' => $this->attribute->id,
            'attribute_value_id' => $newAttributeValue->id,
        ]);
    }

    /** @test */
    public function it_can_delete_product_attribute()
    {
        // Add an attribute to the product
        ProductAttribute::create([
            'product_id' => $this->product->id,
            'attribute_id' => $this->attribute->id,
            'attribute_value_id' => $this->attributeValue->id,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->deleteJson("/api/products/{$this->product->id}/attributes/{$this->attribute->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data',
            ]);

        $this->assertDatabaseMissing('product_attributes', [
            'product_id' => $this->product->id,
            'attribute_id' => $this->attribute->id,
        ]);
    }

    /** @test */
    public function it_can_batch_update_product_attributes()
    {
        // Create another attribute
        $sizeAttribute = Attribute::create([
            'name' => 'Size',
            'code' => 'size',
            'type' => 'select',
            'is_required' => false,
            'is_filterable' => true,
            'is_active' => true,
            'sort_order' => 2,
        ]);

        // Create attribute values for size
        $sizeValueM = AttributeValue::create([
            'attribute_id' => $sizeAttribute->id,
            'value' => 'M',
            'display_value' => 'Medium',
            'sort_order' => 1,
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson("/api/products/{$this->product->id}/attributes/batch", [
                'attributes' => [
                    [
                        'attribute_id' => $this->attribute->id,
                        'attribute_value_id' => $this->attributeValue->id,
                    ],
                    [
                        'attribute_id' => $sizeAttribute->id,
                        'attribute_value_id' => $sizeValueM->id,
                    ],
                ],
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'product_id',
                        'attribute_id',
                        'attribute_name',
                        'attribute_code',
                        'attribute_type',
                        'attribute_value_id',
                        'text_value',
                    ],
                ],
            ]);

        $this->assertDatabaseHas('product_attributes', [
            'product_id' => $this->product->id,
            'attribute_id' => $this->attribute->id,
            'attribute_value_id' => $this->attributeValue->id,
        ]);

        $this->assertDatabaseHas('product_attributes', [
            'product_id' => $this->product->id,
            'attribute_id' => $sizeAttribute->id,
            'attribute_value_id' => $sizeValueM->id,
        ]);
    }

    /** @test */
    public function it_can_get_attribute_value_by_code()
    {
        // Add an attribute to the product
        ProductAttribute::create([
            'product_id' => $this->product->id,
            'attribute_id' => $this->attribute->id,
            'attribute_value_id' => $this->attributeValue->id,
        ]);

        // Refresh the product model
        $this->product->refresh();

        // Get attribute value by code
        $value = $this->product->getAttributeValue('color');

        $this->assertEquals('Red', $value);
    }

    /** @test */
    public function it_can_get_all_attribute_values()
    {
        // Create another attribute
        $sizeAttribute = Attribute::create([
            'name' => 'Size',
            'code' => 'size',
            'type' => 'select',
            'is_required' => false,
            'is_filterable' => true,
            'is_active' => true,
            'sort_order' => 2,
        ]);

        // Create attribute values for size
        $sizeValueM = AttributeValue::create([
            'attribute_id' => $sizeAttribute->id,
            'value' => 'M',
            'display_value' => 'Medium',
            'sort_order' => 1,
            'is_active' => true,
        ]);

        // Add attributes to the product
        ProductAttribute::create([
            'product_id' => $this->product->id,
            'attribute_id' => $this->attribute->id,
            'attribute_value_id' => $this->attributeValue->id,
        ]);

        ProductAttribute::create([
            'product_id' => $this->product->id,
            'attribute_id' => $sizeAttribute->id,
            'attribute_value_id' => $sizeValueM->id,
        ]);

        // Refresh the product model
        $this->product->refresh();

        // Get all attribute values
        $values = $this->product->getAllAttributeValues();

        $this->assertEquals([
            'color' => 'Red',
            'size' => 'M',
        ], $values);
    }
}
