<?php

namespace Tests\Feature\Warehouses;

use App\Models\GoodReceipt;
use App\Models\GoodReceiptItem;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use App\Models\Product;
use App\Models\ProductSerial;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WarehouseArea;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class GoodReceiptTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $warehouse;
    protected $warehouseArea;
    protected $product;
    protected $purchaseOrder;
    protected $purchaseOrderItem;

    public function setUp(): void
    {
        parent::setUp();

        // Tạo dữ liệu mẫu
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $this->warehouse = Warehouse::factory()->create([
            'name' => 'Test Warehouse',
            'code' => 'TW001',
        ]);

        $this->warehouseArea = WarehouseArea::factory()->create([
            'name' => 'Test Area',
            'code' => 'TA001',
            'warehouse_id' => $this->warehouse->id,
        ]);

        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'code' => 'TP001',
            'unit' => 'pcs',
            'price' => 100000,
            'inventory_tracking_type' => 'serial',
        ]);

        // Tạo đơn đặt hàng
        $this->purchaseOrder = PurchaseOrder::factory()->create([
            'code' => 'PO001',
            'supplier_id' => 1,
            'warehouse_id' => $this->warehouse->id,
            'status' => PurchaseOrder::STATUS_APPROVED,
            'created_by' => $this->user->id,
        ]);

        // Tạo chi tiết đơn đặt hàng
        $this->purchaseOrderItem = PurchaseOrderItem::factory()->create([
            'purchase_order_id' => $this->purchaseOrder->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'unit_price' => 100000,
            'received_quantity' => 0,
        ]);

        // Đăng nhập
        $this->actingAs($this->user);
    }

    /**
     * Test tạo phiếu nhập kho
     */
    public function testCreateGoodReceipt()
    {
        // Tạo dữ liệu đầu vào
        $data = [
            'code' => 'GR001',
            'purchase_order_id' => $this->purchaseOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'status' => GoodReceipt::STATUS_PENDING,
            'notes' => 'Test notes',
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'purchase_order_item_id' => $this->purchaseOrderItem->id,
                    'warehouse_area_id' => $this->warehouseArea->id,
                    'quantity' => 5,
                    'unit_price' => 100000,
                    'batch_number' => 'BATCH001',
                    'expiry_date' => now()->addYear()->format('Y-m-d'),
                ]
            ]
        ];

        // Gửi request tạo phiếu nhập kho
        $response = $this->post(route('admin.warehouses.good-receipts.store', ['locale' => 'vi']), $data);

        // Kiểm tra kết quả
        $response->assertStatus(302); // Redirect sau khi tạo thành công
        $response->assertSessionHasNoErrors();

        // Kiểm tra phiếu nhập kho đã được tạo
        $this->assertDatabaseHas('good_receipts', [
            'code' => 'GR001',
            'purchase_order_id' => $this->purchaseOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'status' => GoodReceipt::STATUS_PENDING,
            'notes' => 'Test notes',
        ]);

        // Kiểm tra chi tiết phiếu nhập kho
        $this->assertDatabaseHas('good_receipt_items', [
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'quantity' => 5,
            'unit_price' => 100000,
            'batch_number' => 'BATCH001',
        ]);
    }

    /**
     * Test duyệt phiếu nhập kho
     */
    public function testApproveGoodReceipt()
    {
        // Tạo phiếu nhập kho
        $goodReceipt = GoodReceipt::factory()->create([
            'code' => 'GR001',
            'purchase_order_id' => $this->purchaseOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'status' => GoodReceipt::STATUS_PENDING,
            'notes' => 'Test notes',
            'created_by' => $this->user->id,
        ]);

        // Tạo chi tiết phiếu nhập kho
        $goodReceiptItem = GoodReceiptItem::factory()->create([
            'good_receipt_id' => $goodReceipt->id,
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'quantity' => 5,
            'unit_price' => 100000,
            'batch_number' => 'BATCH001',
            'expiry_date' => now()->addYear()->format('Y-m-d'),
        ]);

        // Gửi request duyệt phiếu nhập kho
        $response = $this->post(route('admin.warehouses.good-receipts.approve', ['locale' => 'vi', 'id' => $goodReceipt->id]));

        // Kiểm tra kết quả
        $response->assertStatus(302); // Redirect sau khi duyệt thành công
        $response->assertSessionHasNoErrors();

        // Kiểm tra trạng thái phiếu nhập kho
        $goodReceipt->refresh();
        $this->assertEquals(GoodReceipt::STATUS_APPROVED, $goodReceipt->status);

        // Kiểm tra tồn kho đã được cập nhật
        $inventoryItem = InventoryItem::where('warehouse_id', $this->warehouse->id)
            ->where('product_id', $this->product->id)
            ->first();

        $this->assertNotNull($inventoryItem);
        $this->assertEquals(5, $inventoryItem->quantity);
        $this->assertEquals(5, $inventoryItem->available_quantity);

        // Kiểm tra giao dịch tồn kho đã được tạo
        $transaction = InventoryTransaction::where('warehouse_id', $this->warehouse->id)
            ->where('product_id', $this->product->id)
            ->where('reference_type', GoodReceipt::class)
            ->where('reference_id', $goodReceipt->id)
            ->first();

        $this->assertNotNull($transaction);
        $this->assertEquals(5, $transaction->quantity);
        $this->assertEquals('in', $transaction->transaction_type);

        // Kiểm tra số lượng đã nhận của đơn đặt hàng đã được cập nhật
        $this->purchaseOrderItem->refresh();
        $this->assertEquals(5, $this->purchaseOrderItem->received_quantity);
    }

    /**
     * Test nhập IMEI cho sản phẩm
     */
    public function testEnterImeiForProduct()
    {
        // Tạo phiếu nhập kho
        $goodReceipt = GoodReceipt::factory()->create([
            'code' => 'GR001',
            'purchase_order_id' => $this->purchaseOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'status' => GoodReceipt::STATUS_PENDING,
            'notes' => 'Test notes',
            'created_by' => $this->user->id,
        ]);

        // Tạo chi tiết phiếu nhập kho
        $goodReceiptItem = GoodReceiptItem::factory()->create([
            'good_receipt_id' => $goodReceipt->id,
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'quantity' => 2,
            'unit_price' => 100000,
            'batch_number' => 'BATCH001',
            'expiry_date' => now()->addYear()->format('Y-m-d'),
        ]);

        // Gửi request nhập IMEI
        $response = $this->post(route('admin.product-serials.store', ['locale' => 'vi']), [
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'good_receipt_item_id' => $goodReceiptItem->id,
            'warehouse_id' => $this->warehouse->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'imeis' => ['123456789012345', '987654321098765'],
        ]);

        // Kiểm tra kết quả
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'saved_imeis' => ['123456789012345', '987654321098765'],
        ]);

        // Kiểm tra IMEI đã được lưu vào cơ sở dữ liệu
        $this->assertDatabaseHas('product_serials', [
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'good_receipt_item_id' => $goodReceiptItem->id,
            'serial_number' => '123456789012345',
        ]);

        $this->assertDatabaseHas('product_serials', [
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'good_receipt_item_id' => $goodReceiptItem->id,
            'serial_number' => '987654321098765',
        ]);
    }

    /**
     * Test xử lý race condition khi cập nhật tồn kho
     */
    public function testHandleRaceConditionWhenUpdatingInventory()
    {
        // Tạo phiếu nhập kho
        $goodReceipt1 = GoodReceipt::factory()->create([
            'code' => 'GR001',
            'purchase_order_id' => $this->purchaseOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'status' => GoodReceipt::STATUS_PENDING,
            'notes' => 'Test notes 1',
            'created_by' => $this->user->id,
        ]);

        $goodReceipt2 = GoodReceipt::factory()->create([
            'code' => 'GR002',
            'purchase_order_id' => $this->purchaseOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'status' => GoodReceipt::STATUS_PENDING,
            'notes' => 'Test notes 2',
            'created_by' => $this->user->id,
        ]);

        // Tạo chi tiết phiếu nhập kho
        $goodReceiptItem1 = GoodReceiptItem::factory()->create([
            'good_receipt_id' => $goodReceipt1->id,
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'quantity' => 3,
            'unit_price' => 100000,
            'batch_number' => 'BATCH001',
            'expiry_date' => now()->addYear()->format('Y-m-d'),
        ]);

        $goodReceiptItem2 = GoodReceiptItem::factory()->create([
            'good_receipt_id' => $goodReceipt2->id,
            'product_id' => $this->product->id,
            'purchase_order_item_id' => $this->purchaseOrderItem->id,
            'warehouse_area_id' => $this->warehouseArea->id,
            'quantity' => 4,
            'unit_price' => 100000,
            'batch_number' => 'BATCH002',
            'expiry_date' => now()->addYear()->format('Y-m-d'),
        ]);

        // Duyệt phiếu nhập kho 1
        $response1 = $this->post(route('admin.warehouses.good-receipts.approve', ['locale' => 'vi', 'id' => $goodReceipt1->id]));
        $response1->assertStatus(302);

        // Duyệt phiếu nhập kho 2
        $response2 = $this->post(route('admin.warehouses.good-receipts.approve', ['locale' => 'vi', 'id' => $goodReceipt2->id]));
        $response2->assertStatus(302);

        // Kiểm tra tồn kho đã được cập nhật đúng
        $inventoryItem = InventoryItem::where('warehouse_id', $this->warehouse->id)
            ->where('product_id', $this->product->id)
            ->first();

        $this->assertNotNull($inventoryItem);
        $this->assertEquals(7, $inventoryItem->quantity); // 3 + 4
        $this->assertEquals(7, $inventoryItem->available_quantity);

        // Kiểm tra số lượng đã nhận của đơn đặt hàng đã được cập nhật
        $this->purchaseOrderItem->refresh();
        $this->assertEquals(7, $this->purchaseOrderItem->received_quantity);
    }
}
