{"arrowParens": "avoid", "bracketSpacing": true, "bracketSameLine": true, "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "printWidth": 120, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false, "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "overrides": [{"files": ["resources/assets/vendor/js/*.js"], "options": {"semi": false}}]}