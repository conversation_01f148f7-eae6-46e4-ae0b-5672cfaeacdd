<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Http\Kernel');

// Tạo request giả lập
$request = Illuminate\Http\Request::create('/admin/transfer-requests/warehouse/1/products', 'GET');

try {
    // X<PERSON> lý request
    $response = $kernel->handle($request);
    
    echo "=== TEST API ENDPOINT ===\n";
    echo "URL: /admin/transfer-requests/warehouse/1/products\n";
    echo "Status Code: " . $response->getStatusCode() . "\n";
    echo "Content Type: " . $response->headers->get('Content-Type') . "\n";
    echo "\nResponse Body:\n";
    echo $response->getContent() . "\n";
    
} catch (Exception $e) {
    echo "❌ Lỗi: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n=== END TEST ===\n";
