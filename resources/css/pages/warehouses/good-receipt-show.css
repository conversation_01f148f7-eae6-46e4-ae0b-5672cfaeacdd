/* CSS cho badge trạng thái */
.badge-status {
  font-size: 0.85rem;
  padding: 0.35em 0.65em;
}

/* CSS cho bảng sản phẩm */
.table-products {
  table-layout: fixed;
  width: 100%;
}

.table-products th,
.table-products td {
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-products th:nth-child(1) { width: 3%; }
.table-products th:nth-child(2) { width: 7%; }
.table-products th:nth-child(3) { width: 18%; }
.table-products th:nth-child(4) { width: 5%; }
.table-products th:nth-child(5) { width: 7%; }
.table-products th:nth-child(6) { width: 12%; }
.table-products th:nth-child(7) { width: 12%; }
.table-products th:nth-child(8) { width: 8%; }
.table-products th:nth-child(9) { width: 10%; }
.table-products th:nth-child(10) { width: 8%; }
.table-products th:nth-child(11) { width: 10%; }

.table-products .product-name {
  white-space: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* CSS cho nút thao tác */
.action-buttons .btn {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .action-buttons .btn {
    width: 100%;
    margin-right: 0;
  }
}