/**
 * Purchase Order Create Styles
 */

/* Styles for Select2 product results */
.select2-container--default .select2-results > .select2-results__options {
  max-height: 400px;
}

.select2-result-product {
  border-bottom: 1px solid #f0f0f0;
  padding: 8px;
}

.select2-result-product:last-child {
  border-bottom: none;
}

.select2-result-product__image img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.select2-result-product__title {
  font-weight: 500;
  margin-bottom: 2px;
}

.select2-result-product__code {
  font-size: 0.85rem;
  color: #6c757d;
}

.select2-result-product__price {
  font-weight: 500;
  color: #5d87ff;
}

/* Styles for product table */
#productsTable th, 
#productsTable td {
  vertical-align: middle;
}

.product-item-image {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 4px;
}

.product-item-info {
  display: flex;
  align-items: center;
}

.product-item-details {
  margin-left: 10px;
}

.product-item-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.product-item-code {
  font-size: 0.85rem;
  color: #6c757d;
}

/* Responsive styles */
@media (max-width: 768px) {
  .select2-result-product__price {
    display: none;
  }
}
