/**
 * Styles for warehouse transfer index page
 */

/* Loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* Filter section responsive adjustments */
@media (max-width: 576px) {
  .filter-buttons {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  #filter-button, 
  #reset-filter {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  #reset-filter {
    margin-left: 0 !important;
  }
}

/* DataTable adjustments */
.card-datatable {
  padding: 0;
}

.dataTables_wrapper .dt-buttons {
  float: right;
}

.dataTables_wrapper .dataTables_filter {
  margin-top: 0.5rem;
}

/* Badge styling */
.badge {
  text-transform: capitalize;
}

/* Action buttons in table */
.btn-icon {
  padding: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
}

.dropdown-menu .dropdown-item {
  padding: 0.5rem 1rem;
}
