/**
 * CSS tùy chỉnh cho trang danh sách yêu cầu chuyển kho
 * Styling cho cột thao tác với icon buttons
 */

/* C<PERSON>t thao tác - cố định width và căn giữa */
.datatables-transfer-requests .actions-column {
    width: 100px !important;
    min-width: 100px !important;
    max-width: 100px !important;
    text-align: center !important;
    vertical-align: middle !important;
}

/* Container cho các icon buttons */
.datatables-transfer-requests .d-flex {
    gap: 0.25rem;
}

/* Styling cho icon buttons */
.datatables-transfer-requests .btn-icon {
    width: 32px;
    height: 32px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    border: 1px solid transparent;
    background-color: transparent;
    color: #697a8d;
    transition: all 0.15s ease-in-out;
    text-decoration: none;
}

/* Hover effects cho icon buttons */
.datatables-transfer-requests .btn-icon:hover {
    background-color: rgba(105, 122, 141, 0.1);
    color: #5a6c7d;
    transform: translateY(-1px);
    text-decoration: none;
}

/* Icon Xem - màu xanh dương */
.datatables-transfer-requests .btn-icon:first-child {
    color: #696cff;
}

.datatables-transfer-requests .btn-icon:first-child:hover {
    background-color: rgba(105, 108, 255, 0.1);
    color: #5f61e6;
}

/* Icon Chỉnh sửa - màu cam */
.datatables-transfer-requests .btn-icon:last-child {
    color: #ff9f43;
}

.datatables-transfer-requests .btn-icon:last-child:hover {
    background-color: rgba(255, 159, 67, 0.1);
    color: #e6893a;
}

/* Responsive - ẩn text trên mobile */
@media (max-width: 768px) {
    .datatables-transfer-requests .actions-column {
        width: 80px !important;
        min-width: 80px !important;
        max-width: 80px !important;
    }
    
    .datatables-transfer-requests .btn-icon {
        width: 28px;
        height: 28px;
        font-size: 0.875rem;
    }
}

/* Tooltip styling */
.tooltip {
    font-size: 0.75rem;
}

.tooltip-inner {
    background-color: #233446;
    color: #fff;
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: #233446;
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: #233446;
}
