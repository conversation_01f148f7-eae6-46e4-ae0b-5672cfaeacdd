<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddUser" aria-labelledby="offcanvasAddUserLabel">
  <div class="offcanvas-header border-bottom">
    <h5 id="offcanvasAddUserLabel" class="offcanvas-title">Add User</h5>
    <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body mx-0 flex-grow-0 h-100">
    <form class="add-new-user pt-0" id="addNewUserForm">
      <input type="hidden" name="id" id="user_id">
      <div class="form-floating form-floating-outline mb-5">
        <input type="text" class="form-control" id="add-user-fullname" placeholder="Meow meow" name="name"
               aria-label="Meow meow"/>
        <label for="add-user-fullname">{{ __('') }}</label>
      </div>
      <div class="form-floating form-floating-outline mb-5">
        <input type="text" id="add-user-email" class="form-control" placeholder="<EMAIL>"
               aria-label="<EMAIL>" name="email"/>
        <label for="add-user-email">Email</label>
      </div>
      <div class="mb-5 form-password-toggle">
        <div class="input-group input-group-merge">
          <div class="form-floating form-floating-outline">
            <input type="password" id="password"
                   class="form-control" name="password"
                   placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                   aria-describedby="password"/>
            <label for="password">{{ __('auth.password_field') }}</label>
          </div>
          <span class="input-group-text cursor-pointer"><i class="ri-eye-off-line"></i></span>
        </div>
      </div>
      <div class="mb-5 form-password-toggle">
        <div class="input-group input-group-merge">
          <div class="form-floating form-floating-outline">
            <input type="password" id="password-confirm" class="form-control"
                   name="password_confirmation"
                   placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                   aria-describedby="password"/>
            <label for="password-confirm">{{ __('auth.confirm_password') }}</label>
          </div>
          <span class="input-group-text cursor-pointer"><i class="ri-eye-off-line"></i></span>
        </div>
      </div>
      <div class="form-floating form-floating-outline mb-5">
        <select id="user-role" class="form-select h-px-150 select2" multiple name="roles[]">
          @foreach($roles as $role)
            <option value="{{ $role->value }}">{{ $role->label() }}</option>
          @endforeach
        </select>
        <label for="user-role">{{ __('roles.roles') }}</label>
      </div>
      <div class="mb-5 form-floating form-floating-outline">
        <div class="form-check form-switch mb-2">
          <input class="form-check-input" type="checkbox" id="userActive" name="active">
          <label class="form-check-label" for="userActive">{{ __('common.active') }}</label>
        </div>
      </div>
      <button type="submit" class="btn btn-primary me-sm-3 me-1 data-submit">Submit</button>
      <button type="reset" class="btn btn-outline-secondary" data-bs-dismiss="offcanvas">Cancel</button>
    </form>
  </div>
</div>