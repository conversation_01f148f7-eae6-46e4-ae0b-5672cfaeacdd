<!-- BEGIN: Vendor JS-->

@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/popper/popper.js',
'resources/assets/vendor/js/bootstrap.js',
'resources/assets/vendor/libs/node-waves/node-waves.js',
'resources/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js',
'resources/assets/vendor/libs/hammer/hammer.js',
'resources/assets/vendor/libs/typeahead-js/typeahead.js',
'resources/assets/vendor/js/menu.js',
'resources/js/app.js',
])

@yield('vendor-script')
<!-- END: Page Vendor JS-->
<!-- BEGIN: Theme JS-->
@vite(['resources/assets/js/main.js', 'resources/js/utils/loading.js'])

<!-- Inline loading functions as fallback -->
<script>
  // Ensure loading functions are available
  if (typeof window.showLoading !== 'function') {
    window.showLoading = function() {
      console.log('Using inline showLoading');
      // Create loading overlay if it doesn't exist
      if (!document.getElementById('ajax-loading')) {
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'ajax-loading';
        loadingOverlay.className = 'loading';
        loadingOverlay.style.position = 'fixed';
        loadingOverlay.style.width = '100%';
        loadingOverlay.style.height = '100vh';
        loadingOverlay.style.zIndex = '9999';
        loadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
        loadingOverlay.style.display = 'flex';
        loadingOverlay.style.justifyContent = 'center';
        loadingOverlay.style.alignItems = 'center';
        loadingOverlay.style.top = '0';
        loadingOverlay.style.left = '0';

        // Check if dark mode is enabled
        const isDarkMode = document.documentElement.classList.contains('dark-style');

        if (isDarkMode) {
          loadingOverlay.style.backgroundColor = 'rgba(40, 42, 66, 0.7)';
          // Create sk-fold spinner for dark mode
          loadingOverlay.innerHTML = `
            <div class="sk-fold sk-primary">
              <div class="sk-fold-cube"></div>
              <div class="sk-fold-cube"></div>
              <div class="sk-fold-cube"></div>
              <div class="sk-fold-cube"></div>
            </div>
          `;
        } else {
          // Use loading image for light mode
          const img = document.createElement('img');
          img.src = '/assets/img/pages/loading-light.webp';
          img.alt = 'Loading';
          loadingOverlay.appendChild(img);
        }

        document.body.appendChild(loadingOverlay);
      } else {
        // Show existing loading overlay
        document.getElementById('ajax-loading').style.display = 'flex';
      }
    };
  }

  if (typeof window.hideLoading !== 'function') {
    window.hideLoading = function() {
      console.log('Using inline hideLoading');
      const loadingOverlay = document.getElementById('ajax-loading');
      if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
      }
    };
  }
</script>

<!-- END: Theme JS-->
<!-- Pricing Modal JS-->
@stack('pricing-script')
<!-- END: Pricing Modal JS-->
<!-- BEGIN: Page JS-->
@yield('page-script')
<!-- END: Page JS-->

@stack('modals')
@livewireScripts
