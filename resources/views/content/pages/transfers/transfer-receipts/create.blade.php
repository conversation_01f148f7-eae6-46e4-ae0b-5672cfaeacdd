@extends('layouts/layoutMaster')

@section('title', 'Tạo phiếu nhận hàng')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite(['resources/js/pages/transfers/transfer-receipt-form.js'])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header border-bottom">
        <h5 class="card-title mb-0">Tạo phiếu nhận hàng</h5>
      </div>
      <div class="card-body">
        <!-- Option Selection -->
        <div class="row g-4 mb-4">
          <div class="col-12">
            <div class="card border">
              <div class="card-header">
                <h6 class="card-title mb-0">Chọn cách tạo phiếu nhận hàng</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="creation_type" id="from_order" value="from_order" checked>
                      <label class="form-check-label" for="from_order">
                        <strong>Từ phiếu chuyển hàng đã duyệt</strong>
                        <br><small class="text-muted">Tạo phiếu nhận hàng từ phiếu chuyển hàng đã được duyệt</small>
                      </label>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="creation_type" id="manual" value="manual">
                      <label class="form-check-label" for="manual">
                        <strong>Tạo thủ công</strong>
                        <br><small class="text-muted">Tạo phiếu nhận hàng mới không dựa trên phiếu chuyển</small>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- From Order Form -->
        <div id="fromOrderForm" class="creation-form">
          <form id="transferReceiptFromOrderForm" method="POST" action="{{ route('transfer-receipts.store') }}">
            @csrf
            <input type="hidden" name="creation_type" value="from_order">
            
            <div class="row g-4 mb-4">
              <div class="col-12">
                <label class="form-label" for="transfer_order_id">Phiếu chuyển hàng <span class="text-danger">*</span></label>
                <select id="transfer_order_id" name="transfer_order_id" class="form-select select2" required>
                  <option value="">Chọn phiếu chuyển hàng</option>
                  @foreach($approvedOrders as $order)
                    <option value="{{ $order->id }}" 
                            data-from-warehouse="{{ $order->fromWarehouse->name }}"
                            data-to-warehouse="{{ $order->toWarehouse->name }}"
                            data-transit-warehouse="{{ $order->transitWarehouse->name ?? '' }}"
                            data-created-at="{{ $order->created_at->format('d/m/Y H:i') }}">
                      {{ $order->code }} - {{ $order->fromWarehouse->name }} → {{ $order->toWarehouse->name }}
                    </option>
                  @endforeach
                </select>
              </div>
            </div>

            <!-- Order Details -->
            <div id="orderDetails" class="card border mb-4" style="display: none;">
              <div class="card-header">
                <h6 class="card-title mb-0">Chi tiết phiếu chuyển hàng</h6>
              </div>
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-md-3">
                    <label class="form-label fw-medium">Kho nguồn:</label>
                    <p class="mb-0" id="detail-from-warehouse">-</p>
                  </div>
                  <div class="col-md-3">
                    <label class="form-label fw-medium">Kho đích:</label>
                    <p class="mb-0" id="detail-to-warehouse">-</p>
                  </div>
                  <div class="col-md-3">
                    <label class="form-label fw-medium">Kho trung gian:</label>
                    <p class="mb-0" id="detail-transit-warehouse">-</p>
                  </div>
                  <div class="col-md-3">
                    <label class="form-label fw-medium">Ngày tạo:</label>
                    <p class="mb-0" id="detail-created-at">-</p>
                  </div>
                  <div class="col-md-3">
                    <a href="#" id="view-order-link" class="btn btn-sm btn-outline-primary" target="_blank">
                      <i class="ri-eye-line me-1"></i>Xem chi tiết
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div class="row g-4 mb-4">
              <div class="col-12">
                <label class="form-label" for="notes_order">Ghi chú</label>
                <textarea id="notes_order" name="notes" class="form-control" rows="3" placeholder="Nhập ghi chú..."></textarea>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="d-flex justify-content-between">
                  <a href="{{ route('transfer-receipts.index') }}" class="btn btn-outline-secondary">
                    <i class="ri-arrow-left-line me-1"></i>Quay lại
                  </a>
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-save-line me-1"></i>Tạo phiếu nhận hàng
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Manual Form -->
        <div id="manualForm" class="creation-form" style="display: none;">
          <form id="transferReceiptManualForm" method="POST" action="{{ route('transfer-receipts.store') }}">
            @csrf
            <input type="hidden" name="creation_type" value="manual">
            
            <div class="row g-4 mb-4">
              <div class="col-md-4">
                <label class="form-label" for="from_warehouse_id_manual">Kho nguồn <span class="text-danger">*</span></label>
                <select id="from_warehouse_id_manual" name="from_warehouse_id" class="form-select select2" required>
                  <option value="">Chọn kho nguồn</option>
                  @foreach($warehouses as $warehouse)
                    <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-4">
                <label class="form-label" for="to_warehouse_id_manual">Kho đích <span class="text-danger">*</span></label>
                <select id="to_warehouse_id_manual" name="to_warehouse_id" class="form-select select2" required>
                  <option value="">Chọn kho đích</option>
                  @foreach($warehouses as $warehouse)
                    <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-4">
                <label class="form-label" for="transit_warehouse_id_manual">Kho trung gian</label>
                <select id="transit_warehouse_id_manual" name="transit_warehouse_id" class="form-select select2">
                  <option value="">Chọn kho trung gian</option>
                  @foreach($warehouses->where('is_transit', true) as $warehouse)
                    <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
                  @endforeach
                </select>
              </div>
            </div>

            <div class="row g-4 mb-4">
              <div class="col-12">
                <label class="form-label" for="notes_manual">Ghi chú</label>
                <textarea id="notes_manual" name="notes" class="form-control" rows="3" placeholder="Nhập ghi chú..."></textarea>
              </div>
            </div>

            <!-- Products Section -->
            <div class="card border">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">Danh sách sản phẩm</h6>
                <button type="button" class="btn btn-primary btn-sm" id="addProductBtn" disabled>
                  <i class="ri-add-line me-1"></i>Thêm sản phẩm
                </button>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-bordered" id="productsTable">
                    <thead class="table-light">
                      <tr>
                        <th width="35%">Sản phẩm</th>
                        <th width="15%">Mã SKU</th>
                        <th width="15%">Đơn vị</th>
                        <th width="15%">Số lượng</th>
                        <th width="15%">Ghi chú</th>
                        <th width="5%">Thao tác</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr id="noProductsRow">
                        <td colspan="6" class="text-center text-muted">Chưa có sản phẩm nào được thêm</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <div class="row mt-4">
              <div class="col-12">
                <div class="d-flex justify-content-between">
                  <a href="{{ route('transfer-receipts.index') }}" class="btn btn-outline-secondary">
                    <i class="ri-arrow-left-line me-1"></i>Quay lại
                  </a>
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-save-line me-1"></i>Tạo phiếu nhận hàng
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Thêm sản phẩm</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="addProductForm">
          <div class="row g-3">
            <div class="col-12">
              <label class="form-label" for="modal_product_id">Sản phẩm <span class="text-danger">*</span></label>
              <select id="modal_product_id" name="product_id" class="form-select select2" required>
                <option value="">Chọn sản phẩm</option>
                @foreach($products as $product)
                  <option value="{{ $product->id }}" data-code="{{ $product->code }}" data-unit="{{ $product->unit }}">
                    {{ $product->name }}
                  </option>
                @endforeach
              </select>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="modal_quantity">Số lượng <span class="text-danger">*</span></label>
              <input type="number" id="modal_quantity" name="quantity" class="form-control" min="0.01" step="0.01" required>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="modal_notes">Ghi chú</label>
              <textarea id="modal_notes" name="notes" class="form-control" rows="2" placeholder="Nhập ghi chú cho sản phẩm..."></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-primary" id="confirmAddProduct">Thêm sản phẩm</button>
      </div>
    </div>
  </div>
</div>
@endsection
