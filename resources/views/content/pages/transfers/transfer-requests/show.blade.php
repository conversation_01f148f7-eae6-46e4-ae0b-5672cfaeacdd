@extends('layouts/layoutMaster')

@section('title', 'Chi tiết yêu cầu chuyển kho')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite(['resources/js/pages/transfers/transfer-request-detail.js'])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <!-- Transfer Request Info -->
    <div class="card mb-4">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Thông tin yêu cầu chuyển kho</h5>
        <div class="d-flex gap-2">
          @if($transferRequest->canBeEdited())
            @can('transfer-requests.edit')
              <a href="{{ route('transfer-requests.edit', $transferRequest->id) }}" class="btn btn-outline-primary btn-sm">
                <i class="ri-pencil-line me-1"></i>Chỉnh sửa
              </a>
            @endcan
          @endif
          
          @if($transferRequest->canBeSubmitted())
            @can('transfer-requests.submit')
              <button type="button" class="btn btn-warning btn-sm submit-approval-btn" data-id="{{ $transferRequest->id }}">
                <i class="ri-send-plane-line me-1"></i>Gửi duyệt
              </button>
            @endcan
          @endif

          @if($transferRequest->canBeApproved())
            @can('transfer-requests.approve')
              <button type="button" class="btn btn-success btn-sm approve-btn" data-id="{{ $transferRequest->id }}">
                <i class="ri-check-line me-1"></i>Duyệt
              </button>
            @endcan
            @can('transfer-requests.reject')
              <button type="button" class="btn btn-danger btn-sm reject-btn" data-id="{{ $transferRequest->id }}">
                <i class="ri-close-line me-1"></i>Từ chối
              </button>
            @endcan
          @endif

          @if($transferRequest->canBeCancelled())
            @can('transfer-requests.cancel')
              <button type="button" class="btn btn-outline-danger btn-sm cancel-btn" data-id="{{ $transferRequest->id }}">
                <i class="ri-delete-bin-7-line me-1"></i>Hủy
              </button>
            @endcan
          @endif

          @if($transferRequest->status === 'approved' && !$transferRequest->transferOrder)
            @can('transfer-orders.create')
              <a href="{{ route('transfer-orders.create-from-request', $transferRequest->id) }}" class="btn btn-primary btn-sm">
                <i class="ri-truck-line me-1"></i>Tạo phiếu chuyển hàng
              </a>
            @endcan
          @endif
        </div>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label fw-medium">Mã yêu cầu:</label>
            <p class="mb-0">{{ $transferRequest->code }}</p>
          </div>
          <div class="col-md-3">
            <label class="form-label fw-medium">Trạng thái:</label>
            <p class="mb-0">
              <span class="badge {{ $transferRequest->status_badge_class }}">{{ $transferRequest->status_text }}</span>
            </p>
          </div>
          <div class="col-md-3">
            <label class="form-label fw-medium">Kho nguồn:</label>
            <p class="mb-0">{{ $transferRequest->fromWarehouse->name }}</p>
          </div>
          <div class="col-md-3">
            <label class="form-label fw-medium">Kho đích:</label>
            <p class="mb-0">{{ $transferRequest->toWarehouse->name }}</p>
          </div>
          <div class="col-md-3">
            <label class="form-label fw-medium">Người tạo:</label>
            <p class="mb-0">{{ $transferRequest->createdBy->name }}</p>
          </div>
          <div class="col-md-3">
            <label class="form-label fw-medium">Ngày tạo:</label>
            <p class="mb-0">{{ $transferRequest->created_at->format('d/m/Y H:i') }}</p>
          </div>
          @if($transferRequest->approved_by)
            <div class="col-md-3">
              <label class="form-label fw-medium">Người duyệt:</label>
              <p class="mb-0">{{ $transferRequest->approvedBy->name }}</p>
            </div>
            <div class="col-md-3">
              <label class="form-label fw-medium">Ngày duyệt:</label>
              <p class="mb-0">{{ $transferRequest->approved_at->format('d/m/Y H:i') }}</p>
            </div>
          @endif
          @if($transferRequest->notes)
            <div class="col-12">
              <label class="form-label fw-medium">Ghi chú:</label>
              <p class="mb-0">{{ $transferRequest->notes }}</p>
            </div>
          @endif
        </div>
      </div>
    </div>

    <!-- Transfer Request Items -->
    <div class="card">
      <div class="card-header">
        <h6 class="card-title mb-0">Danh sách sản phẩm</h6>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead class="table-light">
              <tr>
                <th>STT</th>
                <th>Mã SKU</th>
                <th>Tên sản phẩm</th>
                <th>Đơn vị tính</th>
                <th>Số lượng</th>
                <th>Ghi chú</th>
              </tr>
            </thead>
            <tbody>
              @forelse($transferRequest->items as $index => $item)
                <tr>
                  <td>{{ $index + 1 }}</td>
                  <td>{{ $item->product->code }}</td>
                  <td>{{ $item->product->name }}</td>
                  <td>{{ $item->product->unit }}</td>
                  <td>{{ number_format($item->quantity, 0) }}</td>
                  <td>{{ $item->notes ?? '-' }}</td>
                </tr>
              @empty
                <tr>
                  <td colspan="6" class="text-center text-muted">Không có sản phẩm nào</td>
                </tr>
              @endforelse
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Related Transfer Order -->
    @if($transferRequest->transferOrder)
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="card-title mb-0">Phiếu chuyển hàng liên quan</h6>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <p class="mb-1"><strong>Mã phiếu:</strong> {{ $transferRequest->transferOrder->code }}</p>
              <p class="mb-1"><strong>Trạng thái:</strong> 
                <span class="badge {{ $transferRequest->transferOrder->status_badge_class }}">
                  {{ $transferRequest->transferOrder->status_text }}
                </span>
              </p>
              <p class="mb-0"><strong>Ngày tạo:</strong> {{ $transferRequest->transferOrder->created_at->format('d/m/Y H:i') }}</p>
            </div>
            <div>
              <a href="{{ route('transfer-orders.show', $transferRequest->transferOrder->id) }}" class="btn btn-outline-primary btn-sm">
                <i class="ri-eye-line me-1"></i>Xem chi tiết
              </a>
            </div>
          </div>
        </div>
      </div>
    @endif

    <!-- Back Button -->
    <div class="row mt-4">
      <div class="col-12">
        <a href="{{ route('transfer-requests.index') }}" class="btn btn-outline-secondary">
          <i class="ri-arrow-left-line me-1"></i>Quay lại danh sách
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Từ chối yêu cầu chuyển kho</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="rejectForm">
        <div class="modal-body">
          <div class="mb-3">
            <label for="reject-reason" class="form-label">Lý do từ chối <span class="text-danger">*</span></label>
            <textarea class="form-control" id="reject-reason" name="reason" rows="3" placeholder="Nhập lý do từ chối..." required></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-danger">Từ chối</button>
        </div>
      </form>
    </div>
  </div>
</div>
@endsection
