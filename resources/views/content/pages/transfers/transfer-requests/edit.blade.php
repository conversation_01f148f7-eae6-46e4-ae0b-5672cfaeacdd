@extends('layouts/layoutMaster')

@section('title', 'Chỉnh sửa yêu cầu chuyển kho')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite(['resources/js/pages/transfers/transfer-request-form.js'])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header border-bottom">
        <h5 class="card-title mb-0">Chỉnh sửa yêu cầu chuyển kho: {{ $transferRequest->code }}</h5>
      </div>
      <div class="card-body">
        <form id="transferRequestForm" method="POST" action="{{ route('transfer-requests.update', $transferRequest->id) }}">
          @csrf
          @method('PATCH')
          
          <div class="row g-4 mb-4">
            <div class="col-md-6">
              <label class="form-label" for="from_warehouse_id">Kho nguồn <span class="text-danger">*</span></label>
              <select id="from_warehouse_id" name="from_warehouse_id" class="form-select select2" required>
                <option value="">Chọn kho nguồn</option>
                @foreach($warehouses as $warehouse)
                  <option value="{{ $warehouse->id }}" {{ $transferRequest->from_warehouse_id == $warehouse->id ? 'selected' : '' }}>
                    {{ $warehouse->name }}
                  </option>
                @endforeach
              </select>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="to_warehouse_id">Kho đích <span class="text-danger">*</span></label>
              <select id="to_warehouse_id" name="to_warehouse_id" class="form-select select2" required>
                <option value="">Chọn kho đích</option>
                @foreach($warehouses as $warehouse)
                  <option value="{{ $warehouse->id }}" {{ $transferRequest->to_warehouse_id == $warehouse->id ? 'selected' : '' }}>
                    {{ $warehouse->name }}
                  </option>
                @endforeach
              </select>
            </div>
          </div>

          <div class="row g-4 mb-4">
            <div class="col-12">
              <label class="form-label" for="notes">Ghi chú</label>
              <textarea id="notes" name="notes" class="form-control" rows="3" placeholder="Nhập ghi chú...">{{ $transferRequest->notes }}</textarea>
            </div>
          </div>

          <!-- Products Section -->
          <div class="card border">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h6 class="card-title mb-0">Danh sách sản phẩm</h6>
              <button type="button" class="btn btn-primary btn-sm" id="addProductBtn">
                <i class="ri-add-line me-1"></i>Thêm sản phẩm
              </button>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-bordered" id="productsTable">
                  <thead class="table-light">
                    <tr>
                      <th width="35%">Sản phẩm</th>
                      <th width="15%">Mã SKU</th>
                      <th width="15%">Đơn vị</th>
                      <th width="15%">Tồn kho</th>
                      <th width="15%">Số lượng</th>
                      <th width="5%">Thao tác</th>
                    </tr>
                  </thead>
                  <tbody>
                    @forelse($transferRequest->items as $item)
                      <tr data-product-id="{{ $item->product_id }}">
                        <td>
                          {{ $item->product->name }}
                          <input type="hidden" name="items[{{ $loop->index }}][product_id]" value="{{ $item->product_id }}">
                        </td>
                        <td>{{ $item->product->code }}</td>
                        <td>{{ $item->product->unit }}</td>
                        <td class="available-quantity">-</td>
                        <td>
                          <input type="number" name="items[{{ $loop->index }}][quantity]" class="form-control quantity-input" 
                                 value="{{ $item->quantity }}" min="0.01" step="0.01" required>
                          <input type="hidden" name="items[{{ $loop->index }}][notes]" value="{{ $item->notes }}">
                        </td>
                        <td>
                          <button type="button" class="btn btn-sm btn-outline-danger remove-product-btn">
                            <i class="ri-delete-bin-7-line"></i>
                          </button>
                        </td>
                      </tr>
                    @empty
                      <tr id="noProductsRow">
                        <td colspan="6" class="text-center text-muted">Chưa có sản phẩm nào được thêm</td>
                      </tr>
                    @endforelse
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="row mt-4">
            <div class="col-12">
              <div class="d-flex justify-content-between">
                <a href="{{ route('transfer-requests.show', $transferRequest->id) }}" class="btn btn-outline-secondary">
                  <i class="ri-arrow-left-line me-1"></i>Quay lại
                </a>
                <div>
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-save-line me-1"></i>Cập nhật yêu cầu
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Thêm sản phẩm</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="addProductForm">
          <div class="row g-3">
            <div class="col-12">
              <label class="form-label" for="modal_product_id">Sản phẩm <span class="text-danger">*</span></label>
              <select id="modal_product_id" name="product_id" class="form-select select2" required>
                <option value="">Chọn sản phẩm</option>
              </select>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="modal_available_quantity">Số lượng tồn kho</label>
              <input type="text" id="modal_available_quantity" class="form-control" readonly>
            </div>
            <div class="col-md-6">
              <label class="form-label" for="modal_quantity">Số lượng chuyển <span class="text-danger">*</span></label>
              <input type="number" id="modal_quantity" name="quantity" class="form-control" min="0.01" step="0.01" required>
            </div>
            <div class="col-12">
              <label class="form-label" for="modal_notes">Ghi chú</label>
              <textarea id="modal_notes" name="notes" class="form-control" rows="2" placeholder="Nhập ghi chú cho sản phẩm..."></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-primary" id="confirmAddProduct">Thêm sản phẩm</button>
      </div>
    </div>
  </div>
</div>

<script>
// Pass existing items to JavaScript
window.existingItems = @json($transferRequest->items->pluck('product_id')->toArray());
window.isEditMode = true;
</script>
@endsection
