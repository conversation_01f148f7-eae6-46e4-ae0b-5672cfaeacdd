@extends('layouts/layoutMaster')

@section('title', 'Tạo phiếu chuyển hàng từ yêu cầu')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite(['resources/js/pages/transfers/transfer-order-form.js'])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header border-bottom">
        <h5 class="card-title mb-0">Tạo phiếu chuyển hàng từ yêu cầu: {{ $transferRequest->code }}</h5>
      </div>
      <div class="card-body">
        <!-- Transfer Request Details -->
        <div class="card border mb-4">
          <div class="card-header">
            <h6 class="card-title mb-0">Thông tin yêu cầu chuyển kho</h6>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-3">
                <label class="form-label fw-medium">Mã yêu cầu:</label>
                <p class="mb-0">{{ $transferRequest->code }}</p>
              </div>
              <div class="col-md-3">
                <label class="form-label fw-medium">Trạng thái:</label>
                <p class="mb-0">
                  <span class="badge {{ $transferRequest->status_badge_class }}">{{ $transferRequest->status_text }}</span>
                </p>
              </div>
              <div class="col-md-3">
                <label class="form-label fw-medium">Kho nguồn:</label>
                <p class="mb-0">{{ $transferRequest->fromWarehouse->name }}</p>
              </div>
              <div class="col-md-3">
                <label class="form-label fw-medium">Kho đích:</label>
                <p class="mb-0">{{ $transferRequest->toWarehouse->name }}</p>
              </div>
              <div class="col-md-3">
                <label class="form-label fw-medium">Người tạo:</label>
                <p class="mb-0">{{ $transferRequest->createdBy->name }}</p>
              </div>
              <div class="col-md-3">
                <label class="form-label fw-medium">Ngày tạo:</label>
                <p class="mb-0">{{ $transferRequest->created_at->format('d/m/Y H:i') }}</p>
              </div>
              <div class="col-md-3">
                <label class="form-label fw-medium">Ngày duyệt:</label>
                <p class="mb-0">{{ $transferRequest->approved_at->format('d/m/Y H:i') }}</p>
              </div>
              <div class="col-md-3">
                <a href="{{ route('transfer-requests.show', $transferRequest->id) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                  <i class="ri-eye-line me-1"></i>Xem chi tiết
                </a>
              </div>
              @if($transferRequest->notes)
                <div class="col-12">
                  <label class="form-label fw-medium">Ghi chú yêu cầu:</label>
                  <p class="mb-0">{{ $transferRequest->notes }}</p>
                </div>
              @endif
            </div>
          </div>
        </div>

        <!-- Products from Request -->
        <div class="card border mb-4">
          <div class="card-header">
            <h6 class="card-title mb-0">Danh sách sản phẩm từ yêu cầu</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered">
                <thead class="table-light">
                  <tr>
                    <th>STT</th>
                    <th>Mã SKU</th>
                    <th>Tên sản phẩm</th>
                    <th>Đơn vị tính</th>
                    <th>Số lượng</th>
                    <th>Ghi chú</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($transferRequest->items as $index => $item)
                    <tr>
                      <td>{{ $index + 1 }}</td>
                      <td>{{ $item->product->code }}</td>
                      <td>{{ $item->product->name }}</td>
                      <td>{{ $item->product->unit }}</td>
                      <td>{{ number_format($item->quantity, 0) }}</td>
                      <td>{{ $item->notes ?? '-' }}</td>
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Create Transfer Order Form -->
        <form id="transferOrderFromRequestForm" method="POST" action="{{ route('transfer-orders.store') }}">
          @csrf
          <input type="hidden" id="transfer_request_id" name="transfer_request_id" value="{{ $transferRequest->id }}">

          <div class="row g-4 mb-4">
            <div class="col-12">
              <label class="form-label" for="notes">Ghi chú phiếu chuyển hàng</label>
              <textarea id="notes" name="notes" class="form-control" rows="3" placeholder="Nhập ghi chú cho phiếu chuyển hàng...">{{ $transferRequest->notes }}</textarea>
            </div>
          </div>

          <div class="row">
            <div class="col-12">
              <div class="d-flex justify-content-between">
                <a href="{{ route('transfer-orders.index') }}" class="btn btn-outline-secondary">
                  <i class="ri-arrow-left-line me-1"></i>Quay lại
                </a>
                <button type="submit" class="btn btn-primary">
                  <i class="ri-save-line me-1"></i>Tạo phiếu chuyển hàng
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
@endsection
