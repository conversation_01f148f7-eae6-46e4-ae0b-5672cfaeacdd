@extends('layouts/layoutMaster')

@section('title', 'Chi tiết phiếu chuyển hàng')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite(['resources/js/pages/transfers/transfer-order-detail.js'])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <!-- Transfer Order Info -->
    <div class="card mb-4">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Thông tin phiếu chuyển hàng</h5>
        <div class="d-flex gap-2">
          @if($transferOrder->canBeEdited())
            @can('transfer-orders.edit')
              <a href="{{ route('transfer-orders.edit', $transferOrder->id) }}" class="btn btn-outline-primary btn-sm">
                <i class="ri-pencil-line me-1"></i>Chỉnh sửa
              </a>
            @endcan
          @endif

          @if($transferOrder->canBeSubmitted())
            @can('transfer-orders.submit')
              <button type="button" class="btn btn-warning btn-sm submit-approval-btn" data-id="{{ $transferOrder->id }}">
                <i class="ri-send-plane-line me-1"></i>Gửi duyệt
              </button>
            @endcan
          @endif

          @if($transferOrder->canBeApproved())
            @can('transfer-orders.approve')
              <button type="button" class="btn btn-success btn-sm approve-btn" data-id="{{ $transferOrder->id }}">
                <i class="ri-check-line me-1"></i>Duyệt
              </button>
            @endcan
            @can('transfer-orders.reject')
              <button type="button" class="btn btn-danger btn-sm reject-btn" data-id="{{ $transferOrder->id }}">
                <i class="ri-close-line me-1"></i>Từ chối
              </button>
            @endcan
          @endif

          @if($transferOrder->canBeCancelled())
            @can('transfer-orders.cancel')
              <button type="button" class="btn btn-outline-danger btn-sm cancel-btn" data-id="{{ $transferOrder->id }}">
                <i class="ri-delete-bin-7-line me-1"></i>Hủy
              </button>
            @endcan
          @endif

          @if($transferOrder->status === 'approved' && !$transferOrder->transferReceipt)
            @can('transfer-receipts.create-from-order')
              <button type="button" class="btn btn-primary btn-sm" id="createTransferReceiptBtn" data-order-id="{{ $transferOrder->id }}">
                <i class="ri-inbox-line me-1"></i>Tạo phiếu nhận hàng
              </button>
            @endcan
            @can('transfer-receipts.create')
              <a href="{{ route('transfer-receipts.create-from-order', $transferOrder->id) }}" class="btn btn-outline-primary btn-sm ms-2">
                <i class="ri-edit-line me-1"></i>Tạo thủ công
              </a>
            @endcan
          @endif
        </div>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label fw-medium">Mã phiếu chuyển:</label>
            <p class="mb-0">{{ $transferOrder->code }}</p>
          </div>
          <div class="col-md-3">
            <label class="form-label fw-medium">Trạng thái:</label>
            <p class="mb-0">
              <span class="badge {{ $transferOrder->status_badge_class }}">{{ $transferOrder->status_text }}</span>
            </p>
          </div>
          <div class="col-md-3">
            <label class="form-label fw-medium">Kho nguồn:</label>
            <p class="mb-0">{{ $transferOrder->fromWarehouse->name }}</p>
          </div>
          <div class="col-md-3">
            <label class="form-label fw-medium">Kho đích:</label>
            <p class="mb-0">{{ $transferOrder->toWarehouse->name }}</p>
          </div>
          @if($transferOrder->transitWarehouse)
            <div class="col-md-3">
              <label class="form-label fw-medium">Kho trung gian:</label>
              <p class="mb-0">{{ $transferOrder->transitWarehouse->name }}</p>
            </div>
          @endif
          <div class="col-md-3">
            <label class="form-label fw-medium">Người tạo:</label>
            <p class="mb-0">{{ $transferOrder->createdBy->name }}</p>
          </div>
          <div class="col-md-3">
            <label class="form-label fw-medium">Ngày tạo:</label>
            <p class="mb-0">{{ $transferOrder->created_at->format('d/m/Y H:i') }}</p>
          </div>
          @if($transferOrder->approved_by)
            <div class="col-md-3">
              <label class="form-label fw-medium">Người duyệt:</label>
              <p class="mb-0">{{ $transferOrder->approvedBy->name }}</p>
            </div>
            <div class="col-md-3">
              <label class="form-label fw-medium">Ngày duyệt:</label>
              <p class="mb-0">{{ $transferOrder->approved_at->format('d/m/Y H:i') }}</p>
            </div>
          @endif
          @if($transferOrder->notes)
            <div class="col-12">
              <label class="form-label fw-medium">Ghi chú:</label>
              <p class="mb-0">{{ $transferOrder->notes }}</p>
            </div>
          @endif
        </div>
      </div>
    </div>

    <!-- Related Transfer Request -->
    @if($transferOrder->transferRequest)
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">Yêu cầu chuyển kho liên quan</h6>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <p class="mb-1"><strong>Mã yêu cầu:</strong> {{ $transferOrder->transferRequest->code }}</p>
              <p class="mb-1"><strong>Trạng thái:</strong>
                <span class="badge {{ $transferOrder->transferRequest->status_badge_class }}">
                  {{ $transferOrder->transferRequest->status_text }}
                </span>
              </p>
              <p class="mb-0"><strong>Ngày tạo:</strong> {{ $transferOrder->transferRequest->created_at->format('d/m/Y H:i') }}</p>
            </div>
            <div>
              <a href="{{ route('transfer-requests.show', $transferOrder->transferRequest->id) }}" class="btn btn-outline-primary btn-sm">
                <i class="ri-eye-line me-1"></i>Xem chi tiết
              </a>
            </div>
          </div>
        </div>
      </div>
    @endif

    <!-- Transfer Order Items -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="card-title mb-0">Danh sách sản phẩm</h6>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead class="table-light">
              <tr>
                <th>STT</th>
                <th>Mã SKU</th>
                <th>Tên sản phẩm</th>
                <th>Đơn vị tính</th>
                <th>Số lượng</th>
                <th>Trạng thái chuẩn bị</th>
                <th>Ghi chú</th>
                @if($transferOrder->status === 'draft')
                  <th>Thao tác</th>
                @endif
              </tr>
            </thead>
            <tbody>
              @forelse($transferOrder->items as $index => $item)
                <tr>
                  <td>{{ $index + 1 }}</td>
                  <td>{{ $item->product->code }}</td>
                  <td>
                    {{ $item->product->name }}
                    @if($item->product->inventory_tracking_type === 'serial')
                      <span class="badge bg-label-info ms-1">IMEI</span>
                    @elseif($item->product->inventory_tracking_type === 'batch')
                      <span class="badge bg-label-warning ms-1">Batch</span>
                    @endif
                  </td>
                  <td>{{ $item->product->unit }}</td>
                  <td>{{ number_format($item->quantity, 0) }}</td>
                  <td>
                    @if($item->product->inventory_tracking_type === 'serial')
                      @php
                        $scannedCount = $item->scanned_imei_count;
                        $isComplete = $scannedCount >= $item->quantity;
                      @endphp
                      <span class="badge {{ $isComplete ? 'bg-label-success' : 'bg-label-warning' }}">
                        {{ $scannedCount }}/{{ number_format($item->quantity, 0) }} IMEI
                      </span>
                    @elseif($item->product->inventory_tracking_type === 'batch')
                      @php
                        $batchQuantity = $item->total_batch_quantity;
                        $isComplete = $batchQuantity >= $item->quantity;
                      @endphp
                      <span class="badge {{ $isComplete ? 'bg-label-success' : 'bg-label-warning' }}">
                        {{ number_format($batchQuantity, 0) }}/{{ number_format($item->quantity, 0) }} Batch
                      </span>
                    @else
                      <span class="badge bg-label-success">Sẵn sàng</span>
                    @endif
                  </td>
                  <td>{{ $item->notes ?? '-' }}</td>
                  @if($transferOrder->status === 'draft')
                    <td>
                      @if($item->product->inventory_tracking_type === 'serial')
                        <button type="button" class="btn btn-sm btn-outline-primary manage-imei-btn"
                                data-item-id="{{ $item->id }}"
                                data-product-name="{{ $item->product->name }}"
                                data-quantity="{{ $item->quantity }}">
                          <i class="ri-barcode-line me-1"></i>Quản lý IMEI
                        </button>
                      @elseif($item->product->inventory_tracking_type === 'batch')
                        <button type="button" class="btn btn-sm btn-outline-warning manage-batch-btn"
                                data-item-id="{{ $item->id }}"
                                data-product-name="{{ $item->product->name }}"
                                data-quantity="{{ $item->quantity }}">
                          <i class="ri-archive-line me-1"></i>Quản lý Batch
                        </button>
                      @else
                        <span class="text-muted">-</span>
                      @endif
                    </td>
                  @endif
                </tr>
              @empty
                <tr>
                  <td colspan="{{ $transferOrder->status === 'draft' ? '8' : '7' }}" class="text-center text-muted">Không có sản phẩm nào</td>
                </tr>
              @endforelse
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Related Transfer Receipt -->
    @if($transferOrder->transferReceipt)
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">Phiếu nhận hàng liên quan</h6>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <p class="mb-1"><strong>Mã phiếu:</strong> {{ $transferOrder->transferReceipt->code }}</p>
              <p class="mb-1"><strong>Trạng thái:</strong>
                <span class="badge {{ $transferOrder->transferReceipt->status_badge_class }}">
                  {{ $transferOrder->transferReceipt->status_text }}
                </span>
              </p>
              <p class="mb-0"><strong>Ngày tạo:</strong> {{ $transferOrder->transferReceipt->created_at->format('d/m/Y H:i') }}</p>
            </div>
            <div>
              <a href="{{ route('transfer-receipts.show', $transferOrder->transferReceipt->id) }}" class="btn btn-outline-primary btn-sm">
                <i class="ri-eye-line me-1"></i>Xem chi tiết
              </a>
            </div>
          </div>
        </div>
      </div>
    @endif

    <!-- Back Button -->
    <div class="row">
      <div class="col-12">
        <a href="{{ route('transfer-orders.index') }}" class="btn btn-outline-secondary">
          <i class="ri-arrow-left-line me-1"></i>Quay lại danh sách
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Từ chối phiếu chuyển hàng</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="rejectForm">
        <div class="modal-body">
          <div class="mb-3">
            <label for="reject-reason" class="form-label">Lý do từ chối <span class="text-danger">*</span></label>
            <textarea class="form-control" id="reject-reason" name="reason" rows="3" placeholder="Nhập lý do từ chối..." required></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-danger">Từ chối</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- IMEI Management Modal -->
<div class="modal fade" id="imeiModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Quản lý IMEI - <span id="imei-product-name"></span></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row mb-3">
          <div class="col-md-8">
            <input type="text" class="form-control" id="imei-input" placeholder="Nhập IMEI và nhấn Enter">
          </div>
          <div class="col-md-4">
            <button type="button" class="btn btn-primary" id="add-imei-btn">
              <i class="ri-add-line me-1"></i>Thêm IMEI
            </button>
          </div>
        </div>
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead class="table-light">
              <tr>
                <th>STT</th>
                <th>IMEI</th>
                <th>Người quét</th>
                <th>Thời gian</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody id="imei-table-body">
              <!-- IMEI list will be loaded here -->
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Đóng</button>
      </div>
    </div>
  </div>
</div>

<!-- Batch Management Modal -->
<div class="modal fade" id="batchModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Quản lý Batch - <span id="batch-product-name"></span></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="batch-form" class="mb-3">
          <div class="row">
            <div class="col-md-4">
              <label for="batch-number" class="form-label">Số lô <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="batch-number" required>
            </div>
            <div class="col-md-3">
              <label for="batch-quantity" class="form-label">Số lượng <span class="text-danger">*</span></label>
              <input type="number" class="form-control" id="batch-quantity" min="0.01" step="0.01" required>
            </div>
            <div class="col-md-3">
              <label for="batch-expiry" class="form-label">Ngày hết hạn</label>
              <input type="date" class="form-control" id="batch-expiry">
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <button type="submit" class="btn btn-primary d-block">
                <i class="ri-add-line me-1"></i>Thêm
              </button>
            </div>
          </div>
        </form>
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead class="table-light">
              <tr>
                <th>STT</th>
                <th>Số lô</th>
                <th>Số lượng</th>
                <th>Ngày hết hạn</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody id="batch-table-body">
              <!-- Batch list will be loaded here -->
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Đóng</button>
      </div>
    </div>
  </div>
</div>
@endsection
