@extends('layouts/layoutMaster')

@section('title', '<PERSON> tiết thuộc tính')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/attributes/attribute-show.js')
@endsection

@section('content')
  <h4 class="mb-1">Chi tiết thuộc tính</h4>
  <p class="mb-6">Xem thông tin chi tiết thuộc tính</p>

  <div class="row">
    <!-- Attribute Information -->
    <div class="col-md-6">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">Thông tin thuộc tính</h5>
          <div>
            @can('attributes.edit')
              <a href="{{ route('attributes.edit', $attribute->id) }}" class="btn btn-primary btn-sm">
                <i class="ri-edit-line me-1"></i>Chỉnh sửa
              </a>
            @endcan
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6 mb-3">
              <h6 class="fw-semibold">Tên thuộc tính:</h6>
              <p>{{ $attribute->name }}</p>
            </div>
            <div class="col-md-6 mb-3">
              <h6 class="fw-semibold">Mã thuộc tính:</h6>
              <p>{{ $attribute->code }}</p>
            </div>
            <div class="col-md-6 mb-3">
              <h6 class="fw-semibold">Tên hiển thị:</h6>
              <p>{{ $attribute->display_name ?: $attribute->name }}</p>
            </div>
            <div class="col-md-6 mb-3">
              <h6 class="fw-semibold">Loại thuộc tính:</h6>
              <p>
                @switch($attribute->type)
                  @case('select')
                    <span class="badge bg-label-info">Lựa chọn</span>
                    @break
                  @case('text')
                    <span class="badge bg-label-primary">Văn bản</span>
                    @break
                  @case('number')
                    <span class="badge bg-label-success">Số</span>
                    @break
                  @case('boolean')
                    <span class="badge bg-label-warning">Boolean</span>
                    @break
                  @case('date')
                    <span class="badge bg-label-danger">Ngày tháng</span>
                    @break
                  @default
                    <span class="badge bg-label-secondary">{{ $attribute->type }}</span>
                @endswitch
              </p>
            </div>
            <div class="col-md-12 mb-3">
              <h6 class="fw-semibold">Mô tả:</h6>
              <p>{{ $attribute->description ?: 'Không có mô tả' }}</p>
            </div>
            <div class="col-md-4 mb-3">
              <h6 class="fw-semibold">Thứ tự sắp xếp:</h6>
              <p>{{ $attribute->sort_order }}</p>
            </div>
            <div class="col-md-4 mb-3">
              <h6 class="fw-semibold">Bắt buộc:</h6>
              <p>
                @if($attribute->is_required)
                  <span class="badge bg-label-success">Có</span>
                @else
                  <span class="badge bg-label-secondary">Không</span>
                @endif
              </p>
            </div>
            <div class="col-md-4 mb-3">
              <h6 class="fw-semibold">Dùng để lọc:</h6>
              <p>
                @if($attribute->is_filterable)
                  <span class="badge bg-label-success">Có</span>
                @else
                  <span class="badge bg-label-secondary">Không</span>
                @endif
              </p>
            </div>
            <div class="col-md-4 mb-3">
              <h6 class="fw-semibold">Trạng thái:</h6>
              <p>
                @if($attribute->is_active)
                  <span class="badge bg-label-success">Đang hoạt động</span>
                @else
                  <span class="badge bg-label-danger">Không hoạt động</span>
                @endif
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Attribute Values -->
    <div class="col-md-6">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">Giá trị thuộc tính</h5>
          <div>
            @if($attribute->type === 'select')
              @can('attributes.values.create')
              <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addValueModal">
                <i class="ri-add-line me-1"></i>Thêm giá trị
              </button>
              @endcan
            @else
              <span class="text-muted">Chỉ thuộc tính loại "Lựa chọn" mới có giá trị</span>
            @endif
          </div>
        </div>
        <div class="card-body">
          @if($attribute->type === 'select')
            <div class="table-responsive">
              <table class="table table-striped" id="attributeValuesTable">
                <thead>
                  <tr>
                    <th>Giá trị</th>
                    <th>Tên hiển thị</th>
                    <th>Màu sắc</th>
                    <th>Thứ tự</th>
                    <th>Trạng thái</th>
                    <th>Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($attribute->values as $value)
                    <tr>
                      <td>{{ $value->value }}</td>
                      <td>{{ $value->display_value ?: $value->value }}</td>
                      <td>
                        @if($value->color_code)
                          <span class="color-box" style="background-color: {{ $value->color_code }}"></span>
                          <span class="ms-1">{{ $value->color_code }}</span>
                        @else
                          <span class="text-muted">Không có</span>
                        @endif
                      </td>
                      <td>{{ $value->sort_order }}</td>
                      <td>
                        @if($value->is_active)
                          <span class="badge bg-label-success">Đang hoạt động</span>
                        @else
                          <span class="badge bg-label-danger">Không hoạt động</span>
                        @endif
                      </td>
                      <td>
                        <div class="d-inline-block">
                          <button type="button" class="btn btn-sm btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                            <i class="ri-more-fill"></i>
                          </button>
                          <div class="dropdown-menu dropdown-menu-end">
                            @can('attributes.values.edit')
                            <a href="javascript:void(0);" class="dropdown-item edit-value" data-id="{{ $value->id }}" data-value="{{ $value->value }}" data-display-value="{{ $value->display_value }}" data-color-code="{{ $value->color_code }}" data-sort-order="{{ $value->sort_order }}" data-is-active="{{ $value->is_active }}">
                              <i class="ri-edit-line me-1"></i> Chỉnh sửa
                            </a>
                            @endcan
                            @can('attributes.values.delete')
                            <a href="javascript:void(0);" class="dropdown-item text-danger delete-value" data-id="{{ $value->id }}">
                              <i class="ri-delete-bin-line me-1"></i> Xóa
                            </a>
                            @endcan
                          </div>
                        </div>
                      </td>
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
          @else
            <div class="text-center py-5">
              <div class="mb-3">
                <i class="ri-information-line ri-3x text-primary"></i>
              </div>
              <h6>Thuộc tính loại "{{ $attribute->type }}" không có giá trị cụ thể</h6>
              <p class="mb-0">Giá trị sẽ được nhập trực tiếp khi gán thuộc tính cho sản phẩm</p>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>

  <!-- Add Value Modal -->
  @if($attribute->type === 'select')
    <div class="modal fade" id="addValueModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Thêm giá trị thuộc tính</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <form id="addValueForm" onsubmit="return false">
            <div class="modal-body">
              <input type="hidden" name="attribute_id" value="{{ $attribute->id }}">
              <div class="row">
                <div class="col-12 mb-3">
                  <div class="form-floating form-floating-outline">
                    <input type="text" id="value" name="value" class="form-control" placeholder="Nhập giá trị" />
                    <label for="value">Giá trị <span class="text-danger">*</span></label>
                  </div>
                </div>
                <div class="col-12 mb-3">
                  <div class="form-floating form-floating-outline">
                    <input type="text" id="display_value" name="display_value" class="form-control" placeholder="Nhập tên hiển thị" />
                    <label for="display_value">Tên hiển thị</label>
                    <div class="form-text">Nếu để trống, sẽ sử dụng giá trị</div>
                  </div>
                </div>
                <div class="col-12 mb-3">
                  <div class="form-floating form-floating-outline">
                    <input type="text" id="color_code" name="color_code" class="form-control" placeholder="Nhập mã màu (ví dụ: #FF0000)" />
                    <label for="color_code">Mã màu</label>
                    <div class="form-text">Định dạng HEX (ví dụ: #FF0000)</div>
                  </div>
                </div>
                <div class="col-6 mb-3">
                  <div class="form-floating form-floating-outline">
                    <input type="number" id="sort_order" name="sort_order" class="form-control" value="0" min="0" />
                    <label for="sort_order">Thứ tự sắp xếp</label>
                  </div>
                </div>
                <div class="col-6 mb-3">
                  <div class="form-check form-switch mt-3">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked />
                    <label class="form-check-label" for="is_active">Kích hoạt</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Hủy</button>
              <button type="submit" class="btn btn-primary">Lưu</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Edit Value Modal -->
    <div class="modal fade" id="editValueModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Chỉnh sửa giá trị thuộc tính</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <form id="editValueForm" onsubmit="return false">
            <div class="modal-body">
              <input type="hidden" name="attribute_id" value="{{ $attribute->id }}">
              <input type="hidden" id="edit_value_id" name="id">
              <div class="row">
                <div class="col-12 mb-3">
                  <div class="form-floating form-floating-outline">
                    <input type="text" id="edit_value" name="value" class="form-control" placeholder="Nhập giá trị" />
                    <label for="edit_value">Giá trị <span class="text-danger">*</span></label>
                  </div>
                </div>
                <div class="col-12 mb-3">
                  <div class="form-floating form-floating-outline">
                    <input type="text" id="edit_display_value" name="display_value" class="form-control" placeholder="Nhập tên hiển thị" />
                    <label for="edit_display_value">Tên hiển thị</label>
                    <div class="form-text">Nếu để trống, sẽ sử dụng giá trị</div>
                  </div>
                </div>
                <div class="col-12 mb-3">
                  <div class="form-floating form-floating-outline">
                    <input type="text" id="edit_color_code" name="color_code" class="form-control" placeholder="Nhập mã màu (ví dụ: #FF0000)" />
                    <label for="edit_color_code">Mã màu</label>
                    <div class="form-text">Định dạng HEX (ví dụ: #FF0000)</div>
                  </div>
                </div>
                <div class="col-6 mb-3">
                  <div class="form-floating form-floating-outline">
                    <input type="number" id="edit_sort_order" name="sort_order" class="form-control" value="0" min="0" />
                    <label for="edit_sort_order">Thứ tự sắp xếp</label>
                  </div>
                </div>
                <div class="col-6 mb-3">
                  <div class="form-check form-switch mt-3">
                    <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" />
                    <label class="form-check-label" for="edit_is_active">Kích hoạt</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Hủy</button>
              <button type="submit" class="btn btn-primary">Lưu</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  @endif

  <style>
    .color-box {
      display: inline-block;
      width: 20px;
      height: 20px;
      border-radius: 4px;
      vertical-align: middle;
    }
  </style>
@endsection
