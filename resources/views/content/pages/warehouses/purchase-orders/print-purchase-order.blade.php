<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phiếu đặt hàng - {{ $purchaseOrder->code }}</title>
    <style>
        body {
            font-family: DejaVu Sans, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .header h1 {
            font-size: 18px;
            margin: 0 0 5px;
        }
        .header p {
            margin: 0 0 5px;
            font-size: 12px;
        }
        .info-section {
            margin-bottom: 20px;
        }
        .info-section h2 {
            font-size: 14px;
            margin: 0 0 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        .info-row {
            display: flex;
            margin-bottom: 5px;
        }
        .info-label {
            font-weight: bold;
            width: 150px;
        }
        .info-value {
            flex: 1;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .footer {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        .signature-section {
            width: 30%;
            text-align: center;
        }
        .signature-section p {
            margin: 0 0 30px;
            font-weight: bold;
        }
        .signature-line {
            margin-top: 50px;
            border-top: 1px dotted #333;
            padding-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PHIẾU ĐẶT HÀNG</h1>
            <p>Mã phiếu: {{ $purchaseOrder->code }}</p>
            <p>Ngày tạo: {{ $purchaseOrder->created_at->format('d/m/Y H:i') }}</p>
        </div>

        <div class="info-section">
            <h2>Thông tin chung</h2>
            <div class="info-row">
                <div class="info-label">Kho hàng:</div>
                <div class="info-value">{{ $purchaseOrder->warehouse->name }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Nhà cung cấp:</div>
                <div class="info-value">{{ $purchaseOrder->supplier->name }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Địa chỉ nhà cung cấp:</div>
                <div class="info-value">{{ $purchaseOrder->supplier->address }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Số điện thoại:</div>
                <div class="info-value">{{ $purchaseOrder->supplier->phone }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Ngày dự kiến nhập:</div>
                <div class="info-value">{{ $purchaseOrder->expected_date ? $purchaseOrder->expected_date->format('d/m/Y') : 'N/A' }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Trạng thái:</div>
                <div class="info-value">{{ $purchaseOrder->status_text }}</div>
            </div>
            @if($purchaseOrder->invoice_number)
            <div class="info-row">
                <div class="info-label">Số hóa đơn:</div>
                <div class="info-value">{{ $purchaseOrder->invoice_number }}</div>
            </div>
            @endif
            @if($purchaseOrder->invoice_series)
            <div class="info-row">
                <div class="info-label">Seri hóa đơn:</div>
                <div class="info-value">{{ $purchaseOrder->invoice_series }}</div>
            </div>
            @endif
            @if($purchaseOrder->invoice_template)
            <div class="info-row">
                <div class="info-label">Mẫu hóa đơn:</div>
                <div class="info-value">{{ $purchaseOrder->invoice_template }}</div>
            </div>
            @endif
            @if($purchaseOrder->invoice_date)
            <div class="info-row">
                <div class="info-label">Ngày hóa đơn:</div>
                <div class="info-value">{{ $purchaseOrder->invoice_date->format('d/m/Y') }}</div>
            </div>
            @endif
            <div class="info-row">
                <div class="info-label">Ghi chú:</div>
                <div class="info-value">{{ $purchaseOrder->notes ?? 'Không có ghi chú' }}</div>
            </div>
        </div>

        <div class="info-section">
            <h2>Danh sách sản phẩm</h2>
            <table>
                <thead>
                    <tr>
                        <th style="width: 5%">STT</th>
                        <th style="width: 40%">Sản phẩm</th>
                        <th style="width: 10%">Số lượng</th>
                        <th style="width: 15%">Đơn giá</th>
                        <th style="width: 15%">Thành tiền</th>
                    </tr>
                </thead>
                <tbody>
                    @if($purchaseOrder->items->isEmpty())
                        <tr>
                            <td colspan="5" class="text-center">Không có sản phẩm nào</td>
                        </tr>
                    @else
                        @foreach($purchaseOrder->items as $index => $item)
                            <tr>
                                <td class="text-center">{{ $index + 1 }}</td>
                                <td>
                                    <div>
                                        @if($item->product)
                                            <strong>{{ $item->product->name }}</strong><br>
                                            <small>Mã: {{ $item->product->code }}</small>
                                        @else
                                            <span>Sản phẩm không tồn tại</span>
                                        @endif
                                    </div>
                                </td>
                                <td class="text-center">{{ number_format($item->quantity, 0, ',', '.') }}</td>
                                <td class="text-right">{{ number_format($item->unit_price, 0, ',', '.') }} đ</td>
                                <td class="text-right">{{ number_format($item->subtotal, 0, ',', '.') }} đ</td>
                            </tr>
                        @endforeach
                        <tr>
                            <td colspan="4" class="text-right"><strong>Tổng cộng:</strong></td>
                            <td class="text-right"><strong>{{ number_format($purchaseOrder->total_amount, 0, ',', '.') }} đ</strong></td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>

        <div class="footer">
            <div class="signature-section">
                <p>Người lập phiếu</p>
                <div class="signature-line">
                    {{ $purchaseOrder->createdBy ? $purchaseOrder->createdBy->name : 'N/A' }}
                </div>
            </div>
            <div class="signature-section">
                <p>Người duyệt</p>
                <div class="signature-line">
                    {{ $purchaseOrder->approvedBy ? $purchaseOrder->approvedBy->name : '' }}
                </div>
            </div>
            <div class="signature-section">
                <p>Nhà cung cấp</p>
                <div class="signature-line">
                    
                </div>
            </div>
        </div>
    </div>
</body>
</html>
