<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Phiếu nhập hàng - {{ $purchaseOrder->code }}</title>
    <style>
        body {
            font-family: <PERSON>ja<PERSON><PERSON> Sans, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            padding: 0;
        }
        .header p {
            margin: 5px 0;
        }
        .info-section {
            margin-bottom: 20px;
        }
        .info-section h2 {
            font-size: 14px;
            font-weight: bold;
            margin: 0 0 10px 0;
            padding: 0;
            border-bottom: 1px solid #ddd;
        }
        .info-row {
            display: flex;
            margin-bottom: 5px;
        }
        .info-label {
            font-weight: bold;
            width: 150px;
        }
        .info-value {
            flex: 1;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .footer {
            margin-top: 30px;
        }
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
        }
        .signature-box {
            width: 30%;
            text-align: center;
        }
        .signature-title {
            font-weight: bold;
            margin-bottom: 50px;
        }
        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>PHIẾU NHẬP HÀNG</h1>
        <p>Mã phiếu: {{ $purchaseOrder->code }}</p>
        <p>Ngày tạo: {{ $purchaseOrder->created_at->format('d/m/Y H:i') }}</p>
    </div>

    <div class="info-section">
        <h2>Thông tin phiếu nhập hàng</h2>
        <div class="info-row">
            <div class="info-label">Kho hàng:</div>
            <div class="info-value">{{ $purchaseOrder->warehouse->name }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Nhà cung cấp:</div>
            <div class="info-value">{{ $purchaseOrder->supplier->name }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Địa chỉ nhà cung cấp:</div>
            <div class="info-value">{{ $purchaseOrder->supplier->address ?? 'N/A' }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Người liên hệ:</div>
            <div class="info-value">{{ $purchaseOrder->supplier->contact_person ?? 'N/A' }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Điện thoại:</div>
            <div class="info-value">{{ $purchaseOrder->supplier->phone ?? 'N/A' }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Ngày dự kiến:</div>
            <div class="info-value">{{ $purchaseOrder->expected_date ? $purchaseOrder->expected_date->format('d/m/Y') : 'N/A' }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Trạng thái:</div>
            <div class="info-value">{{ $purchaseOrder->status_text }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Người tạo:</div>
            <div class="info-value">{{ $purchaseOrder->createdBy->name ?? 'N/A' }}</div>
        </div>
        @if($purchaseOrder->approved_by)
        <div class="info-row">
            <div class="info-label">Người duyệt:</div>
            <div class="info-value">{{ $purchaseOrder->approvedBy->name ?? 'N/A' }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Ngày duyệt:</div>
            <div class="info-value">{{ $purchaseOrder->approved_at ? $purchaseOrder->approved_at->format('d/m/Y H:i') : 'N/A' }}</div>
        </div>
        @endif
        @if($purchaseOrder->received_by)
        <div class="info-row">
            <div class="info-label">Người nhập kho:</div>
            <div class="info-value">{{ $purchaseOrder->receivedBy->name ?? 'N/A' }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Ngày nhập kho:</div>
            <div class="info-value">{{ $purchaseOrder->received_at ? $purchaseOrder->received_at->format('d/m/Y H:i') : 'N/A' }}</div>
        </div>
        @endif
    </div>

    <div class="info-section">
        <h2>Danh sách sản phẩm</h2>
        <table>
            <thead>
                <tr>
                    <th style="width: 5%">STT</th>
                    <th style="width: 10%">SKU</th>
                    <th style="width: 20%">Sản phẩm</th>
                    <th style="width: 5%">Đơn vị</th>
                    <th style="width: 5%">Số lượng</th>
                    <th style="width: 5%">Đã nhập</th>
                    <th style="width: 10%">Đơn giá (trước VAT)</th>
                    <th style="width: 10%">Đơn giá (sau VAT)</th>
                    <th style="width: 5%">Giảm giá</th>
                    <th style="width: 10%">Thành tiền</th>
                    <th style="width: 15%">Ghi chú</th>
                </tr>
            </thead>
            <tbody>
                @foreach($purchaseOrder->items as $index => $item)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>{{ $item->product->code }}</td>
                    <td>{{ $item->product->name }}</td>
                    <td>{{ $item->product->unit ?? 'N/A' }}</td>
                    <td class="text-right">{{ number_format($item->quantity, 2, ',', '.') }}</td>
                    <td class="text-right">{{ number_format($item->received_quantity, 2, ',', '.') }}</td>
                    <td class="text-right">{{ number_format($item->unit_price, 0, ',', '.') }} đ</td>
                    <td class="text-right">{{ number_format($item->unit_price_after_tax ?? ($item->unit_price * (1 + ($item->tax_rate ?? 0) / 100)), 0, ',', '.') }} đ</td>
                    <td class="text-right">{{ number_format($item->discount ?? 0, 0, ',', '.') }} đ</td>
                    <td class="text-right">{{ number_format($item->subtotal, 0, ',', '.') }} đ</td>
                    <td>
                        {{ $item->notes }}
                        @if($item->batch_number)
                        <div>Lô: {{ $item->batch_number }}</div>
                        @endif
                        @if($item->expiry_date)
                        <div>HSD: {{ $item->expiry_date->format('d/m/Y') }}</div>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="9" class="text-right"><strong>Tổng tiền hàng (trước VAT):</strong></td>
                    <td class="text-right"><strong>{{ number_format($purchaseOrder->items->sum(function($item) { return $item->quantity * $item->unit_price; }), 0, ',', '.') }} đ</strong></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="9" class="text-right"><strong>Tổng tiền VAT:</strong></td>
                    <td class="text-right"><strong>{{ number_format($purchaseOrder->items->sum(function($item) { return $item->quantity * $item->unit_price * (($item->tax_rate ?? 0) / 100); }), 0, ',', '.') }} đ</strong></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="9" class="text-right"><strong>Tổng tiền hàng (sau VAT):</strong></td>
                    <td class="text-right"><strong>{{ number_format($purchaseOrder->items->sum(function($item) { return $item->quantity * $item->unit_price * (1 + ($item->tax_rate ?? 0) / 100); }), 0, ',', '.') }} đ</strong></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="9" class="text-right"><strong>Tổng giảm giá:</strong></td>
                    <td class="text-right"><strong>{{ number_format($purchaseOrder->items->sum(function($item) { return $item->quantity * ($item->discount ?? 0); }), 0, ',', '.') }} đ</strong></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="9" class="text-right"><strong>Tổng tiền phải trả:</strong></td>
                    <td class="text-right"><strong>{{ number_format($purchaseOrder->total_amount, 0, ',', '.') }} đ</strong></td>
                    <td></td>
                </tr>
            </tfoot>
        </table>
    </div>

    @if($purchaseOrder->notes)
    <div class="info-section">
        <h2>Ghi chú</h2>
        <p>{{ $purchaseOrder->notes }}</p>
    </div>
    @endif

    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-title">Người lập phiếu</div>
            <div>{{ $purchaseOrder->createdBy->name ?? '' }}</div>
        </div>
        <div class="signature-box">
            <div class="signature-title">Người duyệt</div>
            <div>{{ $purchaseOrder->approvedBy->name ?? '' }}</div>
        </div>
        <div class="signature-box">
            <div class="signature-title">Người nhận hàng</div>
            <div>{{ $purchaseOrder->receivedBy->name ?? '' }}</div>
        </div>
    </div>
</body>
</html>
