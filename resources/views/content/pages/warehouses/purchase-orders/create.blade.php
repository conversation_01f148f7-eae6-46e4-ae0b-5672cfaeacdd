@extends('layouts/layoutMaster')

@section('title', 'Tạo phiếu đặt hàng')

@section('vendor-style')
@vite([
'resources/assets/vendor/libs/select2/select2.scss',
'resources/assets/vendor/libs/flatpickr/flatpickr.scss',
'resources/assets/vendor/libs/@form-validation/form-validation.scss',
'resources/assets/vendor/libs/animate-css/animate.scss',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
'resources/assets/vendor/libs/select2/select2.js',
'resources/assets/vendor/libs/flatpickr/flatpickr.js',
'resources/assets/vendor/libs/@form-validation/popular.js',
'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
'resources/assets/vendor/libs/@form-validation/auto-focus.js',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite('resources/js/pages/warehouses/purchase-order-create.js')
@endsection

@section('content')
<h4 class="mb-1">Tạo phiếu đặt hàng</h4>
<p class="mb-6">Tạo phiếu đặt hàng mới</p>

<form id="purchaseOrderForm" class="row" onsubmit="return false">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Thao tác</h5>
            </div>
            <div class="card-body">
                <div class="text-end gap-2">
                    <button type="button" class="btn btn-primary" id="submitBtn" data-submit="1">
                        <i class="ri-send-plane-line me-1"></i>Lưu và gửi duyệt
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="saveAsDraftBtn" data-submit="0">
                        <i class="ri-save-line me-1"></i>Lưu nháp
                    </button>
                    <a href="{{ route('warehouses.purchase-orders.list') }}" class="btn btn-label-secondary">
                        <i class="ri-arrow-left-line me-1"></i>Quay lại
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Thông tin phiếu đặt hàng</h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="form-floating form-floating-outline">
                            <select id="warehouse_id" name="warehouse_id" class="select2 form-select"
                                    data-placeholder="Chọn kho hàng">
                                <option value="">Chọn kho hàng</option>
                                @foreach($warehouses as $warehouse)
                                <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
                                @endforeach
                            </select>
                            <label for="warehouse_id">Kho hàng <span class="text-danger">*</span></label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating form-floating-outline">
                            <select id="supplier_id" name="supplier_id" class="select2 form-select"
                                    data-placeholder="Chọn nhà cung cấp">
                                <option value="">Chọn nhà cung cấp</option>
                                @foreach($suppliers as $supplier)
                                <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
                                @endforeach
                            </select>
                            <label for="supplier_id">Nhà cung cấp <span class="text-danger">*</span></label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating form-floating-outline">
                            <input type="text" id="expected_date" name="expected_date"
                                   class="form-control flatpickr-input" placeholder="YYYY-MM-DD"/>
                            <label for="expected_date">Ngày dự kiến nhập</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating form-floating-outline">
                            <select id="currency" name="currency" class="select2 form-select"
                                    data-placeholder="Chọn tiền tệ">
                                <option value="VND" selected>VND</option>
                                <option value="USD">USD</option>
                            </select>
                            <label for="currency">Tiền tệ</label>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-floating form-floating-outline">
                            <textarea id="notes" name="notes" class="form-control h-px-100"
                                      placeholder="Ghi chú"></textarea>
                            <label for="notes">Ghi chú</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Danh sách sản phẩm</h5>
                <button type="button" class="btn btn-primary" id="addProductBtn" data-bs-toggle="modal"
                        data-bs-target="#addProductModal">
                    <i class="ri-add-line me-1"></i>Thêm sản phẩm
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="productsTable">
                        <thead>
                        <tr>
                            <th style="width: 3%">#</th>
                            <th style="width: 10%">SKU</th>
                            <th style="width: 20%">Sản phẩm</th>
                            <th style="width: 8%">Đơn vị</th>
                            <th style="width: 8%">Số lượng</th>
                            <th style="width: 10%">Đơn giá (trước VAT)</th>
                            <th style="width: 10%">Đơn giá (sau VAT)</th>
                            <th style="width: 8%">Giảm giá</th>
                            <th style="width: 10%">Thành tiền (trước VAT)</th>
                            <th style="width: 13%">Thao tác</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr id="noProductRow">
                            <td colspan="10" class="text-center">Chưa có sản phẩm nào</td>
                        </tr>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="8" class="text-end fw-bold">Tổng tiền hàng (trước VAT):</td>
                            <td class="fw-bold" id="totalAmountBeforeVAT">0 đ</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td colspan="8" class="text-end fw-bold">Tổng tiền VAT:</td>
                            <td class="fw-bold" id="totalVAT">0 đ</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td colspan="8" class="text-end fw-bold">Tổng tiền hàng (sau VAT):</td>
                            <td class="fw-bold" id="totalAmountAfterVAT">0 đ</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td colspan="8" class="text-end fw-bold">Tổng giảm giá:</td>
                            <td class="fw-bold" id="totalDiscount">0 đ</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td colspan="8" class="text-end fw-bold">Tổng tiền phải trả:</td>
                            <td class="fw-bold" id="totalAmount">0 đ</td>
                            <td></td>
                        </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>


</form>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm sản phẩm</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addProductForm" class="row g-3">
                    <input type="hidden" id="edit_item_id" value="">
                    <input type="hidden" id="product_sku" value="">
                    <input type="hidden" id="product_unit" value="">
                    <div class="col-md-12">
                        <div class="form-floating form-floating-outline">
                            <select id="product_id" name="product_id" class="select2 form-select"
                                    data-placeholder="Tìm kiếm sản phẩm">
                                <option value="">Tìm kiếm sản phẩm</option>
                            </select>
                            <label for="product_id">Sản phẩm <span class="text-danger">*</span></label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating form-floating-outline">
                            <input type="number" id="quantity" name="quantity" class="form-control"
                                   placeholder="Số lượng" min="1" step="1"/>
                            <label for="quantity">Số lượng <span class="text-danger">*</span></label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating form-floating-outline">
                            <input type="number" id="unit_price" name="unit_price" class="form-control"
                                   placeholder="Đơn giá trước VAT" min="0" step="1"/>
                            <label for="unit_price">Đơn giá (trước VAT) <span class="text-danger">*</span></label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating form-floating-outline">
                            <input type="number" id="product_tax" name="product_tax" class="form-control"
                                   placeholder="Thuế VAT (%)" min="0" max="100" step="0.1" value="0"/>
                            <label for="product_tax">Thuế VAT (%)</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating form-floating-outline">
                            <input type="number" id="unit_price_after_tax" name="unit_price_after_tax"
                                   class="form-control" placeholder="Đơn giá sau VAT" min="0" step="1" readonly/>
                            <label for="unit_price_after_tax">Đơn giá (sau VAT)</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating form-floating-outline">
                            <input type="number" id="discount" name="discount" class="form-control"
                                   placeholder="Giảm giá" min="0" step="1" value="0"/>
                            <label for="discount">Giảm giá</label>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-floating form-floating-outline">
                            <textarea id="item_notes" name="item_notes" class="form-control h-px-100"
                                      placeholder="Ghi chú"></textarea>
                            <label for="item_notes">Ghi chú</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal" id="cancelProductBtn">
                    Hủy
                </button>
                <button type="button" class="btn btn-primary" id="saveProductBtn">Thêm</button>
            </div>
        </div>
    </div>
</div>
@endsection
