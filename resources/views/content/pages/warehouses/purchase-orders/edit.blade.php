@extends('layouts/layoutMaster')

@section('title', 'Chỉnh sửa phiếu đặt hàng')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
  ])
@endsection

@section('page-style')
  @vite([
    'resources/css/select2-fixes.css'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/warehouses/purchase-order-edit.js')
@endsection

@section('content')
  <h4 class="mb-1">Chỉnh sửa phiếu đặt hàng</h4>
  <p class="mb-6">Chỉnh sửa thông tin phiếu đặt hàng</p>

  <form id="purchaseOrderForm" class="row" onsubmit="return false">
    <input type="hidden" name="id" value="{{ $purchaseOrder->id }}">
    <div class="col-md-8">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Thông tin phiếu đặt hàng</h5>
        </div>
        <div class="card-body">
          <div class="row g-4">
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <select id="warehouse_id" name="warehouse_id" class="select2 form-select" data-placeholder="Chọn kho hàng">
                  <option value="">Chọn kho hàng</option>
                  @foreach($warehouses as $warehouse)
                    <option value="{{ $warehouse->id }}" {{ $purchaseOrder->warehouse_id == $warehouse->id ? 'selected' : '' }}>{{ $warehouse->name }}</option>
                  @endforeach
                </select>
                <label for="warehouse_id">Kho hàng <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <select id="supplier_id" name="supplier_id" class="select2 form-select" data-placeholder="Chọn nhà cung cấp">
                  <option value="">Chọn nhà cung cấp</option>
                  @foreach($suppliers as $supplier)
                    <option value="{{ $supplier->id }}" {{ $purchaseOrder->supplier_id == $supplier->id ? 'selected' : '' }}>{{ $supplier->name }}</option>
                  @endforeach
                </select>
                <label for="supplier_id">Nhà cung cấp <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="expected_date" name="expected_date" class="form-control flatpickr-input" placeholder="YYYY-MM-DD" value="{{ $purchaseOrder->expected_date ? $purchaseOrder->expected_date->format('Y-m-d') : '' }}" />
                <label for="expected_date">Ngày dự kiến nhập</label>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-floating form-floating-outline">
                <textarea id="notes" name="notes" class="form-control h-px-100" placeholder="Ghi chú">{{ $purchaseOrder->notes }}</textarea>
                <label for="notes">Ghi chú</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">Danh sách sản phẩm</h5>
          <button type="button" class="btn btn-primary" id="addProductBtn" data-bs-toggle="modal" data-bs-target="#addProductModal">
            <i class="ri-add-line me-1"></i>Thêm sản phẩm
          </button>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-bordered" id="productsTable">
              <thead>
                <tr>
                  <th style="width: 3%">#</th>
                  <th style="width: 40%">Sản phẩm</th>
                  <th style="width: 12%">Số lượng</th>
                  <th style="width: 15%">Đơn giá</th>
                  <th style="width: 15%">Thành tiền</th>
                  <th style="width: 15%">Thao tác</th>
                </tr>
              </thead>
              <tbody>
                @if($purchaseOrder->items->isEmpty())
                  <tr id="noProductRow">
                    <td colspan="6" class="text-center">Chưa có sản phẩm nào</td>
                  </tr>
                @else
                  @foreach($purchaseOrder->items as $index => $item)
                    <tr data-id="{{ $item->id }}" data-product-id="{{ $item->product_id }}" data-quantity="{{ $item->quantity }}" data-unit-price="{{ $item->unit_price }}" data-notes="{{ $item->notes }}">
                      <td>{{ $index + 1 }}</td>
                      <td>
                        <div class="d-flex flex-column">
                          <span class="fw-medium">{{ $item->product->name }}</span>
                          <small class="text-muted">Mã: {{ $item->product->code }}</small>
                        </div>
                      </td>
                      <td class="text-end">{{ number_format($item->quantity, 0, ',', '.') }}</td>
                      <td class="text-end">{{ number_format($item->unit_price, 0, ',', '.') }} đ</td>
                      <td class="text-end">{{ number_format($item->subtotal, 0, ',', '.') }} đ</td>
                      <td>
                        <div class="d-flex">
                          <button type="button" class="btn btn-sm btn-icon btn-label-primary me-1 edit-product-btn" data-id="{{ $item->id }}">
                            <i class="ri-edit-line"></i>
                          </button>
                          <button type="button" class="btn btn-sm btn-icon btn-label-danger delete-product-btn" data-id="{{ $item->id }}">
                            <i class="ri-delete-bin-line"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  @endforeach
                @endif
              </tbody>
              <tfoot>
                <tr>
                  <td colspan="4" class="text-end fw-bold">Tổng cộng:</td>
                  <td class="fw-bold text-end" id="totalAmount">{{ number_format($purchaseOrder->total_amount, 0, ',', '.') }} đ</td>
                  <td></td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Thao tác</h5>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <button type="button" class="btn btn-primary" id="submitBtn" data-submit="1">
              <i class="ri-send-plane-line me-1"></i>Lưu và gửi duyệt
            </button>
            <button type="button" class="btn btn-outline-primary" id="saveAsDraftBtn" data-submit="0">
              <i class="ri-save-line me-1"></i>Lưu nháp
            </button>
            <a href="{{ route('warehouses.purchase-orders.show', $purchaseOrder->id) }}" class="btn btn-label-secondary">
              <i class="ri-arrow-left-line me-1"></i>Quay lại
            </a>
          </div>
        </div>
      </div>
    </div>
  </form>

  <!-- Add Product Modal -->
  <div class="modal fade" id="addProductModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="productModalTitle">Thêm sản phẩm</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="addProductForm" class="row g-3">
            <input type="hidden" id="edit_item_id" value="">
            <div class="col-md-12">
              <div class="form-floating form-floating-outline">
                <select id="product_id" name="product_id" class="select2 form-select" data-placeholder="Tìm kiếm sản phẩm">
                  <option value="">Tìm kiếm sản phẩm</option>
                </select>
                <label for="product_id">Sản phẩm <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="number" id="quantity" name="quantity" class="form-control" placeholder="Số lượng" min="1" step="1" />
                <label for="quantity">Số lượng <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="number" id="unit_price" name="unit_price" class="form-control" placeholder="Đơn giá" min="0" step="1" />
                <label for="unit_price">Đơn giá <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-floating form-floating-outline">
                <textarea id="item_notes" name="item_notes" class="form-control h-px-100" placeholder="Ghi chú"></textarea>
                <label for="item_notes">Ghi chú</label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal" id="cancelProductBtn">Hủy</button>
          <button type="button" class="btn btn-primary" id="saveProductBtn">Thêm</button>
        </div>
      </div>
    </div>
  </div>
@endsection
