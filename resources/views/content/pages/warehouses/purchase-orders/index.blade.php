@extends('layouts/layoutMaster')

@section('title', 'Quản lý phiếu đặt hàng')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
  ])
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/js/ui-popover.js'
  ])
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
@endsection

@section('page-script')
  @vite('resources/js/pages/warehouses/purchase-order-index.js')
  <style>
    #loading-indicator {
      width: 1.5rem;
      height: 1.5rem;
      vertical-align: middle;
    }
    .filter-buttons {
      display: inline-flex;
      align-items: center;
    }
    #clear-filters.active {
      background-color: #28c76f;
      border-color: #28c76f;
      color: #fff;
      transition: all 0.2s ease-in-out;
    }
    #clear-filters i {
      transition: all 0.2s ease-in-out;
    }
    @media (max-width: 576px) {
      #clear-filters, #apply-filters {
        width: 100%;
        margin-bottom: 0.5rem;
      }
      .filter-buttons {
        display: flex;
        flex-direction: column;
        width: 100%;
      }
    }
  </style>
@endsection

@section('content')
  <h4 class="mb-1">Quản lý phiếu đặt hàng</h4>
  <p class="mb-6">Quản lý danh sách phiếu đặt hàng trong hệ thống</p>

  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="card-title mb-0">Bộ lọc</h5>
      <div class="d-flex">
        <button type="button" class="btn btn-primary me-2" id="add-new-btn">
          <i class="ri-add-line me-1"></i>Tạo phiếu đặt hàng
        </button>
      </div>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-md-3">
          <div class="form-floating form-floating-outline">
            <select id="warehouse-filter" class="select2 form-select" data-placeholder="Tất cả kho hàng">
              <option value="">Tất cả kho hàng</option>
              @foreach($warehouses as $warehouse)
                <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
              @endforeach
            </select>
            <label for="warehouse-filter">Kho hàng</label>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-floating form-floating-outline">
            <select id="supplier-filter" class="select2 form-select" data-placeholder="Tất cả nhà cung cấp">
              <option value="">Tất cả nhà cung cấp</option>
              @foreach($suppliers as $supplier)
                <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
              @endforeach
            </select>
            <label for="supplier-filter">Nhà cung cấp</label>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-floating form-floating-outline">
            <select id="status-filter" class="select2 form-select" data-placeholder="Tất cả trạng thái">
              <option value="">Tất cả trạng thái</option>
              <option value="draft">Nháp</option>
              <option value="pending">Chờ duyệt</option>
              <option value="approved">Đã duyệt</option>
              <option value="rejected">Từ chối</option>
              <option value="partially_received">Nhập một phần</option>
              <option value="received">Đã nhập kho</option>
              <option value="cancelled">Đã hủy</option>
            </select>
            <label for="status-filter">Trạng thái</label>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-floating form-floating-outline">
            <input type="text" id="date-from" class="form-control flatpickr-input" placeholder="YYYY-MM-DD" />
            <label for="date-from">Từ ngày</label>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-floating form-floating-outline">
            <input type="text" id="date-to" class="form-control flatpickr-input" placeholder="YYYY-MM-DD" />
            <label for="date-to">Đến ngày</label>
          </div>
        </div>
        <div class="col-md-3 d-flex align-items-center">
          <div class="filter-buttons">
            <button type="button" class="btn btn-primary me-2" id="apply-filters">
              <i class="ri-filter-2-line me-1"></i>Lọc
              <span id="loading-indicator" class="spinner-border spinner-border-sm" role="status" style="display: none;">
                <span class="visually-hidden">Loading...</span>
              </span>
            </button>
            <button type="button" class="btn btn-outline-secondary" id="clear-filters">
              <i class="ri-refresh-line me-1"></i>Xóa bộ lọc
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card">
    <div class="card-datatable table-responsive">
      <table class="datatables-purchase-orders table border-top">
        <thead>
          <tr>
            <th></th>
            <th>Mã phiếu</th>
            <th>Ngày tạo</th>
            <th>Kho hàng</th>
            <th>Nhà cung cấp</th>
            <th>Ngày dự kiến</th>
            <th>Trạng thái</th>
            <th>Tổng tiền</th>
            <th>Thao tác</th>
          </tr>
        </thead>
      </table>
    </div>
  </div>
@endsection
