@extends('layouts/layoutMaster')

@section('title', 'Thêm khu vực kho mới')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/warehouses/warehouse-areas-create.js')
@endsection

@section('content')
  <h4 class="mb-1">Thêm khu vực kho mới</h4>
  <p class="mb-6">Tạo khu vực mới trong kho hàng</p>

  <div class="row">
    <div class="col-md-12">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Thông tin khu vực kho</h5>
        </div>
        <div class="card-body">
          <form id="areaForm" class="row g-4" onsubmit="return false">
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <select id="warehouse_id" name="warehouse_id" class="form-select">
                  <option value="">Chọn kho hàng</option>
                  @foreach($warehouses as $warehouse)
                    <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
                  @endforeach
                </select>
                <label for="warehouse_id">Kho hàng <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="name" name="name" class="form-control" placeholder="Nhập tên khu vực" />
                <label for="name">Tên khu vực <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="code" name="code" class="form-control" placeholder="Nhập mã khu vực" />
                <label for="code">Mã khu vực <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-floating form-floating-outline">
                <textarea id="description" name="description" class="form-control h-px-100" placeholder="Nhập mô tả khu vực"></textarea>
                <label for="description">Mô tả khu vực</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked />
                <label class="form-check-label" for="is_active">Kích hoạt khu vực</label>
              </div>
            </div>
            <div class="col-12 d-flex justify-content-between">
              <a href="{{ route('warehouses.areas.list') }}" class="btn btn-label-secondary">Quay lại</a>
              <button type="submit" class="btn btn-primary">Tạo khu vực</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
