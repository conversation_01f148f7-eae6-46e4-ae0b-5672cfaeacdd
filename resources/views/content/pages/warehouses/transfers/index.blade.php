@extends('layouts/layoutMaster')

@section('title', 'Phiếu chuyển kho')

@section('vendor-style')
@vite([
'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-checkboxes-jquery/datatables.checkboxes.scss',
'resources/assets/vendor/libs/animate-css/animate.scss',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
'resources/assets/vendor/libs/select2/select2.scss',
'resources/assets/vendor/libs/flatpickr/flatpickr.scss',
])
@endsection

@section('vendor-script')
@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
'resources/assets/vendor/libs/select2/select2.js',
'resources/assets/vendor/libs/flatpickr/flatpickr.js',
'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'
])
@endsection

@section('page-style')
  @vite('resources/css/pages/warehouses/transfer-index.css')
@endsection

@section('page-script')
  @vite('resources/js/pages/warehouses/transfer-index.js')
@endsection

@section('content')
  <h4 class="mb-1">Quản lý phiếu chuyển kho</h4>
  <p class="mb-6">Quản lý danh sách phiếu chuyển kho giữa các kho hàng</p>

  <!-- Filter -->
  <div class="card mb-4">
    <div class="card-body">
      <div class="row">
        <div class="col-md-4 mb-3">
          <label for="source-warehouse-filter" class="form-label">Kho nguồn</label>
          <select id="source-warehouse-filter" class="form-select select2">
            <option value="">Tất cả kho nguồn</option>
            @foreach($warehouses as $warehouse)
              <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-4 mb-3">
          <label for="destination-warehouse-filter" class="form-label">Kho đích</label>
          <select id="destination-warehouse-filter" class="form-select select2">
            <option value="">Tất cả kho đích</option>
            @foreach($warehouses as $warehouse)
              <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-4 mb-3">
          <label for="status-filter" class="form-label">Trạng thái</label>
          <select id="status-filter" class="form-select select2">
            <option value="">Tất cả trạng thái</option>
            @foreach($statuses as $value => $label)
              <option value="{{ $value }}">{{ $label }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-4 mb-3">
          <label for="date-from" class="form-label">Từ ngày</label>
          <input type="text" id="date-from" class="form-control flatpickr-date" placeholder="DD/MM/YYYY">
        </div>
        <div class="col-md-4 mb-3">
          <label for="date-to" class="form-label">Đến ngày</label>
          <input type="text" id="date-to" class="form-control flatpickr-date" placeholder="DD/MM/YYYY">
        </div>
        <div class="col-md-4 d-flex align-items-end mb-3">
          <button id="filter-button" class="btn btn-primary me-2">Lọc</button>
          <button id="reset-filter" class="btn btn-label-secondary">Đặt lại</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Transfers List -->
  <div class="card">
    <div class="card-datatable table-responsive">
      <table class="datatables-transfers table border-top" data-datatable-url="{{ route('warehouses.transfers.datatable') }}">
        <thead>
        <tr>
          <th></th>
          <th>Mã phiếu</th>
          <th>Kho nguồn</th>
          <th>Kho đích</th>
          <th>Kho trung gian</th>
          <th>Trạng thái</th>
          <th>Ngày dự kiến</th>
          <th>Ngày tạo</th>
          <th>Thao tác</th>
        </tr>
        </thead>
      </table>
    </div>
  </div>
@endsection
