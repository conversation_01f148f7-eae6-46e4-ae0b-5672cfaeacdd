@extends('layouts/layoutMaster')

@section('title', 'Điều chỉnh tồn kho')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/warehouses/warehouse-inventory-adjust.js')
@endsection

@section('content')
  <h4 class="mb-1"><PERSON><PERSON><PERSON><PERSON> chỉnh tồn kho</h4>
  <p class="mb-6"><PERSON><PERSON><PERSON><PERSON> chỉnh số lượng tồn kho</p>

  <div class="row">
    <div class="col-md-12">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Thông tin điều chỉnh tồn kho</h5>
        </div>
        <div class="card-body">
          <form id="adjustInventoryForm" class="row g-4" onsubmit="return false">
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <select id="warehouse_id" name="warehouse_id" class="form-select">
                  <option value="">Chọn kho hàng</option>
                  @foreach($warehouses as $warehouse)
                    <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
                  @endforeach
                </select>
                <label for="warehouse_id">Kho hàng <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <select id="product_id" name="product_id" class="select2 form-select" data-placeholder="Chọn sản phẩm">
                  <option value="">Chọn sản phẩm</option>
                </select>
                <label for="product_id">Sản phẩm <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="number" id="current_quantity" class="form-control" readonly disabled />
                <label for="current_quantity">Số lượng hiện tại</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="number" id="new_quantity" name="new_quantity" class="form-control" placeholder="Nhập số lượng mới" min="0" step="0.01" />
                <label for="new_quantity">Số lượng mới <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-floating form-floating-outline">
                <textarea id="reason" name="reason" class="form-control h-px-100" placeholder="Nhập lý do điều chỉnh"></textarea>
                <label for="reason">Lý do điều chỉnh <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-12 d-flex justify-content-between">
              <a href="{{ route('warehouses.inventory.view') }}" class="btn btn-label-secondary">Quay lại</a>
              <button type="submit" class="btn btn-warning">Điều chỉnh tồn kho</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
