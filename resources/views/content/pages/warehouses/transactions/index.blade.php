@extends('layouts/layoutMaster')

@section('title', 'Phiếu nhập kho')

@section('page-head')
  <meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-checkboxes-jquery/datatables.checkboxes.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/js/ui-popover.js'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/warehouses/inventory-transaction-index.js')
  <style>
    #loading-indicator {
      width: 1.5rem;
      height: 1.5rem;
      vertical-align: middle;
    }
    .filter-buttons {
      display: inline-flex;
      align-items: center;
    }
    #clear-filters.active {
      background-color: #28c76f;
      border-color: #28c76f;
      color: #fff;
      transition: all 0.2s ease-in-out;
    }
    #clear-filters i {
      transition: all 0.2s ease-in-out;
    }
    @media (max-width: 576px) {
      #clear-filters, #apply-filters {
        width: 100%;
        margin-bottom: 0.5rem;
      }
      .filter-buttons {
        display: flex;
        flex-direction: column;
        width: 100%;
      }
    }
  </style>
@endsection

@section('content')
  <h4 class="mb-1">Quản lý phiếu nhập kho</h4>
  <p class="mb-6">Quản lý danh sách phiếu nhập kho trong hệ thống</p>

  <!-- Filter -->
  <div class="card mb-4">
    <div class="card-body">
      <div class="row">
        <div class="col-md-3 mb-3">
          <label for="warehouse-filter" class="form-label">Lọc theo kho hàng</label>
          <select id="warehouse-filter" class="form-select select2">
            <option value="">Tất cả kho hàng</option>
            @foreach($warehouses as $warehouse)
              <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-3 mb-3">
          <label for="product-filter" class="form-label">Lọc theo sản phẩm</label>
          <select id="product-filter" class="form-select select2-product">
            <option value="">Tất cả sản phẩm</option>
          </select>
        </div>
        <div class="col-md-3 mb-3">
          <label for="purchase-order-filter" class="form-label">Lọc theo phiếu nhập hàng</label>
          <select id="purchase-order-filter" class="form-select select2-purchase-order">
            <option value="">Tất cả phiếu nhập hàng</option>
          </select>
        </div>
        <div class="col-md-3 mb-3">
          <label for="date-range" class="form-label">Lọc theo ngày tạo</label>
          <div class="d-flex">
            <input type="text" id="date-from" class="form-control flatpickr-date me-2" placeholder="Từ ngày" />
            <input type="text" id="date-to" class="form-control flatpickr-date" placeholder="Đến ngày" />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 text-end">
          <div class="filter-buttons">
            <button id="clear-filters" class="btn btn-outline-secondary me-2">
              <i class="ri-refresh-line me-1"></i>Xóa bộ lọc
            </button>
            <button id="apply-filters" class="btn btn-primary">
              <i class="ri-filter-line me-1"></i>Lọc dữ liệu
            </button>
            <div id="loading-indicator" style="display: none;" class="spinner-border text-primary ms-2" role="status">
              <span class="visually-hidden">Đang tải...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Transactions List -->
  <div class="card">
    <div class="card-header border-bottom">
      <h5 class="card-title mb-3">Danh sách phiếu nhập kho</h5>
    </div>
    <div class="card-datatable table-responsive">
      <table class="datatables-transactions table border-top">
        <thead>
        <tr>
          <th></th>
          <th>Mã phiếu</th>
          <th>Ngày tạo</th>
          <th>Sản phẩm</th>
          <th>Kho hàng</th>
          <th>Số lượng</th>
          <th>Số lô</th>
          <th>Hạn sử dụng</th>
          <th>Phiếu nhập hàng</th>
          <th>Người tạo</th>
          <th>Thao tác</th>
        </tr>
        </thead>
      </table>
    </div>
  </div>
@endsection
