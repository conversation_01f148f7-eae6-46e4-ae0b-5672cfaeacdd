<div class="table-responsive">
  <table class="table table-bordered">
    <thead>
      <tr>
        <th><PERSON><PERSON> sản phẩm</th>
        <th>Tên sản phẩm</th>
        <th><PERSON><PERSON> hàng</th>
        <th><PERSON><PERSON> lô</th>
        <th><PERSON><PERSON> lượng</th>
        <th><PERSON><PERSON><PERSON> hết hạn</th>
        <th>Ngày còn lại</th>
      </tr>
    </thead>
    <tbody>
      @if(count($data) > 0)
        @foreach($data as $item)
          <tr>
            <td>{{ $item->product->code }}</td>
            <td>{{ $item->product->name }}</td>
            <td>{{ $item->warehouse->name }}</td>
            <td>{{ $item->batch_number ?? 'N/A' }}</td>
            <td>{{ $item->quantity }}</td>
            <td>{{ $item->expiry_date->format('d/m/Y') }}</td>
            <td>
              @php
                $daysLeft = $item->expiry_date->diffInDays(now());
                $color = $daysLeft <= 30 ? 'danger' : ($daysLeft <= 90 ? 'warning' : 'success');
              @endphp
              <span class="badge bg-label-{{ $color }}">{{ $daysLeft }} ngày</span>
            </td>
          </tr>
        @endforeach
      @else
        <tr>
          <td colspan="7" class="text-center">Không có dữ liệu</td>
        </tr>
      @endif
    </tbody>
  </table>
</div>
