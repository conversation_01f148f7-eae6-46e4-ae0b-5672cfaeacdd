@extends('layouts/layoutMaster')

@section('title', 'Phiếu nhận hàng')

@section('vendor-style')
  <link rel="stylesheet" href="{{asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')}}">
  <link rel="stylesheet" href="{{asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')}}">
  <link rel="stylesheet" href="{{asset('assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.css')}}">
  <link rel="stylesheet" href="{{asset('assets/vendor/libs/select2/select2.css')}}">
  <link rel="stylesheet" href="{{asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css')}}">
  <link rel="stylesheet" href="{{asset('assets/vendor/libs/flatpickr/flatpickr.css')}}">
@endsection

@section('vendor-script')
  <script src="{{asset('assets/vendor/libs/moment/moment.js')}}"></script>
  <script src="{{asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js')}}"></script>
  <script src="{{asset('assets/vendor/libs/select2/select2.js')}}"></script>
  <script src="{{asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js')}}"></script>
  <script src="{{asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js')}}"></script>
  <script src="{{asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js')}}"></script>
  <script src="{{asset('assets/vendor/libs/cleavejs/cleave.js')}}"></script>
  <script src="{{asset('assets/vendor/libs/cleavejs/cleave-phone.js')}}"></script>
  <script src="{{asset('assets/vendor/libs/flatpickr/flatpickr.js')}}"></script>
@endsection

@section('page-script')
  <script>
    $(function () {
      // Datepicker
      $('.flatpickr-date').flatpickr({
        dateFormat: 'd/m/Y',
        allowInput: true
      });

      // Select2
      $('.select2').select2();

      // DataTable
      let dt_table = $('.datatables-receipts');
      if (dt_table.length) {
        const dt = dt_table.DataTable({
          processing: true,
          serverSide: true,
          ajax: {
            url: '{{ route('warehouses.receipts.datatable') }}',
            type: 'POST',
            data: function (d) {
              d.warehouse_id = $('#warehouse-filter').val();
              d.status = $('#status-filter').val();
              d.date_from = $('#date-from').val();
              d.date_to = $('#date-to').val();
              d._token = '{{ csrf_token() }}';
            }
          },
          columns: [
            { data: '' },
            { data: 'code' },
            { data: 'transfer_code' },
            { data: 'warehouse_name' },
            { data: 'status' },
            { data: 'created_at' },
            { data: 'completed_at' },
            { data: 'actions' }
          ],
          columnDefs: [
            {
              className: 'control',
              orderable: false,
              searchable: false,
              responsivePriority: 2,
              targets: 0,
              render: function (data, type, full, meta) {
                return '';
              }
            },
            {
              targets: 1,
              render: function (data, type, full, meta) {
                return `<span class="fw-medium">${full.code}</span>`;
              }
            },
            {
              targets: 4,
              render: function (data, type, full, meta) {
                return `<span class="badge bg-label-${full.status_color}">${full.status_text}</span>`;
              }
            },
            {
              targets: -1,
              title: 'Thao tác',
              orderable: false,
              searchable: false,
              render: function (data, type, full, meta) {
                return full.actions;
              }
            }
          ],
          order: [[5, 'desc']],
          dom: '<"card-header d-flex flex-wrap py-0"<"me-5"f><"d-flex justify-content-center"<"dt-action-buttons text-end pt-3 pt-md-0"B>>>t<"row mx-2"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
          lengthMenu: [10, 25, 50, 75, 100],
          buttons: [
            {
              extend: 'collection',
              className: 'btn btn-label-primary dropdown-toggle me-2',
              text: '<i class="ri-file-list-line me-1"></i><span>Xuất</span>',
              buttons: [
                {
                  extend: 'print',
                  text: '<i class="ri-printer-line me-1"></i>In',
                  className: 'dropdown-item',
                  exportOptions: { columns: [1, 2, 3, 4, 5, 6] }
                },
                {
                  extend: 'csv',
                  text: '<i class="ri-file-text-line me-1"></i>CSV',
                  className: 'dropdown-item',
                  exportOptions: { columns: [1, 2, 3, 4, 5, 6] }
                },
                {
                  extend: 'excel',
                  text: '<i class="ri-file-excel-line me-1"></i>Excel',
                  className: 'dropdown-item',
                  exportOptions: { columns: [1, 2, 3, 4, 5, 6] }
                },
                {
                  extend: 'pdf',
                  text: '<i class="ri-file-pdf-line me-1"></i>PDF',
                  className: 'dropdown-item',
                  exportOptions: { columns: [1, 2, 3, 4, 5, 6] }
                }
              ]
            }
          ],
          language: {
            url: '{{ asset('assets/vendor/libs/datatables/i18n/vi.json') }}'
          },
          responsive: {
            details: {
              display: $.fn.dataTable.Responsive.display.modal({
                header: function (row) {
                  var data = row.data();
                  return 'Chi tiết phiếu nhận hàng ' + data.code;
                }
              }),
              type: 'column',
              renderer: function (api, rowIdx, columns) {
                var data = $.map(columns, function (col, i) {
                  return col.title !== '' // ? Do not show row in modal popup if title is blank (for check box)
                    ? '<tr data-dt-row="' +
                    col.rowIndex +
                    '" data-dt-column="' +
                    col.columnIndex +
                    '">' +
                    '<td>' +
                    col.title +
                    ':' +
                    '</td> ' +
                    '<td>' +
                    col.data +
                    '</td>' +
                    '</tr>'
                    : '';
                }).join('');

                return data ? $('<table class="table"/><tbody />').append(data) : false;
              }
            }
          }
        });

        // Filter form control to default size
        $('.dataTables_filter .form-control').removeClass('form-control-sm');
        $('.dataTables_length .form-select').removeClass('form-select-sm');

        // Filter button
        $('#filter-button').on('click', function () {
          dt.draw();
        });

        // Reset filter
        $('#reset-filter').on('click', function () {
          $('#warehouse-filter').val('').trigger('change');
          $('#status-filter').val('').trigger('change');
          $('#date-from').val('');
          $('#date-to').val('');
          dt.draw();
        });
      }
    });
  </script>
@endsection

@section('content')
  <h4 class="mb-1">Quản lý phiếu nhận hàng</h4>
  <p class="mb-6">Quản lý danh sách phiếu nhận hàng từ phiếu chuyển kho</p>

  <!-- Filter -->
  <div class="card mb-4">
    <div class="card-body">
      <div class="row">
        <div class="col-md-4 mb-3">
          <label for="warehouse-filter" class="form-label">Kho nhận</label>
          <select id="warehouse-filter" class="form-select select2">
            <option value="">Tất cả kho</option>
            @foreach($warehouses as $warehouse)
              <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-4 mb-3">
          <label for="status-filter" class="form-label">Trạng thái</label>
          <select id="status-filter" class="form-select select2">
            <option value="">Tất cả trạng thái</option>
            @foreach($statuses as $value => $label)
              <option value="{{ $value }}">{{ $label }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-4 mb-3">
          <label for="date-from" class="form-label">Từ ngày</label>
          <input type="text" id="date-from" class="form-control flatpickr-date" placeholder="DD/MM/YYYY">
        </div>
        <div class="col-md-4 mb-3">
          <label for="date-to" class="form-label">Đến ngày</label>
          <input type="text" id="date-to" class="form-control flatpickr-date" placeholder="DD/MM/YYYY">
        </div>
        <div class="col-md-8 d-flex align-items-end mb-3">
          <button id="filter-button" class="btn btn-primary me-2">Lọc</button>
          <button id="reset-filter" class="btn btn-label-secondary">Đặt lại</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Receipts List -->
  <div class="card">
    <div class="card-datatable table-responsive">
      <table class="datatables-receipts table border-top">
        <thead>
        <tr>
          <th></th>
          <th>Mã phiếu nhận</th>
          <th>Mã phiếu chuyển</th>
          <th>Kho nhận</th>
          <th>Trạng thái</th>
          <th>Ngày tạo</th>
          <th>Ngày nhận</th>
          <th>Thao tác</th>
        </tr>
        </thead>
      </table>
    </div>
  </div>
@endsection
