@extends('layouts/layoutMaster')

@section('title', 'Quản lý phiếu nhập kho')

@section('vendor-style')
@vite([
'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
'resources/assets/vendor/libs/select2/select2.scss',
'resources/assets/vendor/libs/flatpickr/flatpickr.scss',
'resources/assets/vendor/libs/@form-validation/form-validation.scss',
'resources/assets/vendor/libs/animate-css/animate.scss',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
'resources/assets/vendor/libs/toastr/toastr.scss',
'resources/css/pages/warehouses/good-receipt-show.css'
])
@endsection

@section('vendor-script')
@vite([
'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
'resources/assets/vendor/libs/select2/select2.js',
'resources/assets/vendor/libs/flatpickr/flatpickr.js',
'resources/assets/vendor/libs/@form-validation/popular.js',
'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
'resources/assets/vendor/libs/@form-validation/auto-focus.js',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
'resources/assets/js/ui-popover.js',
'resources/assets/vendor/libs/toastr/toastr.js'
])
@endsection

@section('page-script')
@vite('resources/js/pages/warehouses/good-receipt-index.js')
@endsection

@section('content')
<h4 class="mb-1">Quản lý phiếu nhập kho</h4>
<p class="mb-6">Quản lý danh sách phiếu nhập kho trong hệ thống</p>

<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Bộ lọc</h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <div class="form-floating form-floating-outline">
                    <select id="warehouse-filter" class="select2 form-select" data-placeholder="Tất cả kho hàng">
                        <option value="">Tất cả kho hàng</option>
                        @foreach($warehouses as $warehouse)
                        <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
                        @endforeach
                    </select>
                    <label for="warehouse-filter">Kho hàng</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-floating form-floating-outline">
                    <input type="text" id="purchase-order-filter" class="form-control" placeholder="Mã phiếu đặt hàng">
                    <label for="purchase-order-filter">Mã phiếu đặt hàng</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-floating form-floating-outline">
                    <select id="status-filter" class="select2 form-select" data-placeholder="Tất cả trạng thái">
                        <option value="">Tất cả trạng thái</option>
                        <option value="draft">Nháp</option>
                        <option value="pending">Chờ duyệt</option>
                        <option value="completed">Đã duyệt</option>
                        <option value="cancelled">Đã hủy</option>
                    </select>
                    <label for="status-filter">Trạng thái</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-floating form-floating-outline">
                    <input type="text" id="date-from" class="form-control flatpickr-input" placeholder="YYYY-MM-DD"/>
                    <label for="date-from">Từ ngày</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-floating form-floating-outline">
                    <input type="text" id="date-to" class="form-control flatpickr-input" placeholder="YYYY-MM-DD"/>
                    <label for="date-to">Đến ngày</label>
                </div>
            </div>
            <div class="col-md-3 d-flex align-items-center">
                <div class="filter-buttons">
                    <button type="button" class="btn btn-primary me-2" id="apply-filters">
                        <i class="ri-filter-2-line me-1"></i>Lọc
                        <span id="loading-indicator" class="spinner-border spinner-border-sm" role="status"
                              style="display: none;">
                <span class="visually-hidden">Loading...</span>
              </span>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="clear-filters">
                        <i class="ri-refresh-line me-1"></i>Xóa bộ lọc
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-datatable table-responsive">
        <table class="datatables-good-receipts table border-top">
            <thead>
            <tr>
                <th></th>
                <th>Mã phiếu</th>
                <th>Ngày tạo</th>
                <th>Kho hàng</th>
                <th>Phiếu đặt hàng</th>
                <th>Trạng thái</th>
                <th>Người tạo</th>
                <th>Thao tác</th>
            </tr>
            </thead>
        </table>
    </div>
</div>
@endsection
