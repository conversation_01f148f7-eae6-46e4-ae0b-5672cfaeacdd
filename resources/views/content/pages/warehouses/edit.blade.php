@extends('layouts/layoutMaster')

@section('title', 'Chỉnh sửa kho hàng')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/warehouses/warehouse-edit.js')
@endsection

@section('content')
  <h4 class="mb-1">Chỉnh sửa kho hàng</h4>
  <p class="mb-6">Cập nhật thông tin kho hàng</p>

  <div class="row">
    <div class="col-md-12">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Thông tin kho hàng</h5>
        </div>
        <div class="card-body">
          <form id="warehouseForm" class="row g-4" onsubmit="return false">
            <input type="hidden" name="id" value="{{ $warehouse->id }}" />
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="name" name="name" class="form-control" placeholder="Nhập tên kho hàng" value="{{ $warehouse->name }}" />
                <label for="name">Tên kho hàng <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="code" name="code" class="form-control" placeholder="Nhập mã kho hàng" value="{{ $warehouse->code }}" />
                <label for="code">Mã kho hàng <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-floating form-floating-outline">
                <textarea id="address" name="address" class="form-control h-px-100" placeholder="Nhập địa chỉ kho hàng">{{ $warehouse->address }}</textarea>
                <label for="address">Địa chỉ kho hàng</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="phone" name="phone" class="form-control" placeholder="Nhập số điện thoại" value="{{ $warehouse->phone }}" />
                <label for="phone">Số điện thoại</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="email" id="email" name="email" class="form-control" placeholder="Nhập email" value="{{ $warehouse->email }}" />
                <label for="email">Email</label>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-floating form-floating-outline">
                <textarea id="description" name="description" class="form-control h-px-100" placeholder="Nhập mô tả kho hàng">{{ $warehouse->description }}</textarea>
                <label for="description">Mô tả kho hàng</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {{ $warehouse->is_active ? 'checked' : '' }} />
                <label class="form-check-label" for="is_active">Kích hoạt kho hàng</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="is_default" name="is_default" {{ $warehouse->is_default ? 'checked' : '' }} />
                <label class="form-check-label" for="is_default">Đặt làm kho mặc định</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="is_transit" name="is_transit" {{ $warehouse->is_transit ? 'checked' : '' }} />
                <label class="form-check-label" for="is_transit">Đánh dấu là kho trung gian</label>
              </div>
            </div>
            <div class="col-12 d-flex justify-content-between">
              <a href="{{ route('warehouses.list') }}" class="btn btn-label-secondary">Quay lại</a>
              <button type="submit" class="btn btn-primary">Cập nhật kho hàng</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
