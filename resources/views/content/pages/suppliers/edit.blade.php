@extends('layouts/layoutMaster')

@section('title', 'Chỉnh sửa nhà cung cấp')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/suppliers/supplier-edit.js')
@endsection

@section('content')
  <h4 class="mb-1">Chỉnh sửa nhà cung cấp</h4>
  <p class="mb-6">Chỉnh sửa thông tin nhà cung cấp</p>

  <form id="supplierForm" class="row" onsubmit="return false">
    <input type="hidden" name="id" value="{{ $supplier->id }}">
    
    <div class="col-md-8">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Thông tin nhà cung cấp</h5>
        </div>
        <div class="card-body">
          <div class="row g-4">
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="name" name="name" class="form-control" placeholder="Nhập tên nhà cung cấp" value="{{ $supplier->name }}" />
                <label for="name">Tên nhà cung cấp <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="code" name="code" class="form-control" placeholder="Nhập mã nhà cung cấp" value="{{ $supplier->code }}" />
                <label for="code">Mã nhà cung cấp</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="contact_person" name="contact_person" class="form-control" placeholder="Nhập tên người liên hệ" value="{{ $supplier->contact_person }}" />
                <label for="contact_person">Người liên hệ</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="email" id="email" name="email" class="form-control" placeholder="Nhập email" value="{{ $supplier->email }}" />
                <label for="email">Email</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="phone" name="phone" class="form-control" placeholder="Nhập số điện thoại" value="{{ $supplier->phone }}" />
                <label for="phone">Điện thoại</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="tax_code" name="tax_code" class="form-control" placeholder="Nhập mã số thuế" value="{{ $supplier->tax_code }}" />
                <label for="tax_code">Mã số thuế</label>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-floating form-floating-outline">
                <textarea id="address" name="address" class="form-control h-px-100" placeholder="Nhập địa chỉ">{{ $supplier->address }}</textarea>
                <label for="address">Địa chỉ</label>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-floating form-floating-outline">
                <textarea id="notes" name="notes" class="form-control h-px-100" placeholder="Nhập ghi chú">{{ $supplier->notes }}</textarea>
                <label for="notes">Ghi chú</label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Trạng thái</h5>
        </div>
        <div class="card-body">
          <div class="form-check form-switch mb-4">
            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {{ $supplier->is_active ? 'checked' : '' }} />
            <label class="form-check-label" for="is_active">Đang hoạt động</label>
          </div>
        </div>
      </div>

      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Thao tác</h5>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-primary" id="submitBtn">
              <i class="ri-save-line me-1"></i>Lưu thay đổi
            </button>
            <a href="{{ route('suppliers.show', $supplier->id) }}" class="btn btn-label-secondary">
              <i class="ri-arrow-left-line me-1"></i>Quay lại
            </a>
          </div>
        </div>
      </div>
    </div>
  </form>
@endsection
