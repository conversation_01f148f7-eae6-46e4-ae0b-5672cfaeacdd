@extends('layouts/layoutMaster')

@section('title', 'Nhập sản phẩm từ file')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/assets/vendor/libs/dropzone/dropzone.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/vendor/libs/dropzone/dropzone.js'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/products/product-import-new.js')
@endsection

@section('page-style')
  <style>
    /* CSS cho bảng có header cố định khi cuộn */
    .table-responsive {
      position: relative;
      border: 1px solid #eee;
      border-radius: 0.375rem;
    }
    .sticky-top {
      position: sticky;
      top: 0;
      z-index: 10;
    }
    /* Đảm bảo các ô header có background để che phần nội dung khi cuộn */
    .sticky-top th {
      background-color: white;
      box-shadow: 0 1px 2px rgba(0,0,0,0.1);
      position: sticky;
      top: 0;
    }
    /* Thanh cuộn */
    .table-responsive::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    .table-responsive::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    .table-responsive::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 4px;
    }
    .table-responsive::-webkit-scrollbar-thumb:hover {
      background: #aaa;
    }
    /* Cố định cột checkbox và STT */
    #previewTable th:nth-child(1),
    #previewTable td:nth-child(1),
    #previewTable th:nth-child(2),
    #previewTable td:nth-child(2) {
      position: sticky;
      left: 0;
      background-color: white;
      z-index: 5;
    }
    #previewTable th:nth-child(2),
    #previewTable td:nth-child(2) {
      left: 50px;
    }
    /* Khi vừa cố định hàng và cột */
    #previewTable th:nth-child(1),
    #previewTable th:nth-child(2) {
      z-index: 15;
    }
  </style>
@endsection

@section('content')
  <h4 class="mb-1">Nhập sản phẩm từ file</h4>
  <p class="mb-6">Nhập danh sách sản phẩm từ file Excel hoặc CSV</p>

  <div class="row">
    <div class="col-md-12">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Tải lên file</h5>
        </div>
        <div class="card-body">
          <div class="row">
              <div class="col-md-12 mb-4">
                <div class="alert alert-info">
                  <h6 class="alert-heading mb-1"><i class="ri-information-line me-1"></i> Hướng dẫn nhập sản phẩm</h6>
                  <p class="mb-0">Vui lòng tải xuống mẫu file Excel và điền thông tin sản phẩm theo đúng định dạng.</p>
                  <p class="mb-0">Các trường bắt buộc: Mã sản phẩm, Tên sản phẩm.</p>
                </div>
              </div>

              <div class="col-md-12 mb-4">
                <div class="card">
                  <div class="card-body">
                    <form id="uploadForm" action="/admin/products/upload-import-file" method="POST" enctype="multipart/form-data">
                      @csrf
                      <div class="mb-3">
                        <label for="importFile" class="form-label">Chọn file Excel để import</label>
                        <input class="form-control" type="file" id="importFile" name="file" accept=".xlsx,.xls,.csv" required>
                        <div class="form-text">Chỉ chấp nhận file Excel (.xlsx, .xls) hoặc CSV (.csv). Kích thước tối đa: 10MB</div>
                      </div>
                      <button type="submit" class="btn btn-primary" id="uploadBtn">
                        <i class="ri-upload-2-line me-1"></i> Tải lên và kiểm tra
                      </button>
                    </form>
                  </div>
                </div>
              </div>

              <!-- Thông tin file đã upload -->
              <div class="col-md-12 mb-4 d-none" id="fileInfo">
                <div class="alert alert-success">
                  <h6 class="alert-heading mb-1"><i class="ri-check-line me-1"></i> File đã tải lên thành công</h6>
                  <p class="mb-0" id="fileName">Tên file: </p>
                  <p class="mb-0" id="fileSize">Kích thước: </p>
                  <p class="mb-0" id="uploadTime">Thời gian: </p>
                </div>
              </div>

              <!-- Kết quả phân tích dữ liệu -->
              <div class="col-md-12 mb-4 d-none" id="previewData">
                <div class="card">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                      <h5 class="card-title mb-0">Dữ liệu từ file Excel</h5>
                      <p class="text-muted mb-0" id="dataStats"></p>
                    </div>
                    <div>
                      <button type="button" class="btn btn-sm btn-primary" id="selectAllBtn">
                        <i class="ri-checkbox-multiple-line me-1"></i> Chọn tất cả
                      </button>
                      <button type="button" class="btn btn-sm btn-outline-primary" id="deselectAllBtn">
                        <i class="ri-checkbox-multiple-blank-line me-1"></i> Bỏ chọn tất cả
                      </button>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="alert alert-info mb-4">
                      <h6 class="alert-heading mb-1"><i class="ri-information-line me-1"></i> Thông tin</h6>
                      <p class="mb-0">Dữ liệu đã được phân tích từ file Excel. Vui lòng kiểm tra và chọn các dòng cần import.</p>
                      <p class="mb-0">Các dòng có lỗi sẽ được đánh dấu màu đỏ và không thể chọn.</p>
                    </div>

                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                      <table class="table table-bordered" id="previewTable">
                        <thead class="sticky-top bg-white">
                          <tr>
                            <th width="50px">
                              <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="checkAll">
                              </div>
                            </th>
                            <th width="50px">STT</th>
                            <th>Mã sản phẩm</th>
                            <th>Tên sản phẩm</th>
                            <th>Danh mục</th>
                            <th>Thương hiệu</th>
                            <th>Giá bán</th>
                            <th>Giá vốn</th>
                            <th>Trạng thái</th>
                            <th>Lỗi</th>
                          </tr>
                        </thead>
                        <tbody>
                          <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                        </tbody>
                      </table>
                    </div>

                    <div class="alert alert-warning mt-4 mb-0">
                      <h6 class="alert-heading mb-1"><i class="ri-alert-line me-1"></i> Lưu ý</h6>
                      <p class="mb-0">Chỉ các dòng được chọn mới được import vào hệ thống.</p>
                      <p class="mb-0">Nếu mã sản phẩm đã tồn tại và bạn đã chọn "Cập nhật sản phẩm đã tồn tại", sản phẩm sẽ được cập nhật.</p>
                    </div>
                  </div>
                  <div class="card-footer">
                    <button type="button" class="btn btn-primary" id="processImportBtn">
                      <i class="ri-save-line me-1"></i> Import dữ liệu đã chọn
                    </button>
                  </div>
                </div>
              </div>

              <div class="col-md-12 mb-4">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="update_existing" name="update_existing" />
                  <label class="form-check-label" for="update_existing">
                    Cập nhật sản phẩm đã tồn tại (dựa trên mã sản phẩm)
                  </label>
                </div>
              </div>

              <div class="col-md-12 d-flex justify-content-between">
                <div>
                  <a href="/admin/products/download-template" class="btn btn-outline-primary">
                    <i class="ri-download-line me-1"></i>Tải mẫu Excel
                  </a>
                </div>
                <div>
                  <a href="{{ route('products.list') }}" class="btn btn-label-secondary">Quay lại</a>
                </div>
              </div>
            </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Import Results -->
  <div class="row">
    <div class="col-md-12">
      <div class="card d-none" id="importResults">
        <div class="card-header">
          <h5 class="card-title mb-0">Kết quả nhập sản phẩm</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4 mb-3">
              <div class="card bg-label-success">
                <div class="card-body">
                  <h5 class="card-title">Thành công</h5>
                  <p class="card-text fs-3 fw-semibold" id="successCount">0</p>
                </div>
              </div>
            </div>
            <div class="col-md-4 mb-3">
              <div class="card bg-label-warning">
                <div class="card-body">
                  <h5 class="card-title">Cập nhật</h5>
                  <p class="card-text fs-3 fw-semibold" id="updatedCount">0</p>
                </div>
              </div>
            </div>
            <div class="col-md-4 mb-3">
              <div class="card bg-label-danger">
                <div class="card-body">
                  <h5 class="card-title">Lỗi</h5>
                  <p class="card-text fs-3 fw-semibold" id="errorCount">0</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Error Details -->
          <div class="row d-none" id="errorDetails">
            <div class="col-md-12">
              <div class="alert alert-danger">
                <h6 class="alert-heading mb-1">Danh sách lỗi</h6>
                <div id="errorList"></div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12 text-end">
              <a href="{{ route('products.list') }}" class="btn btn-primary">
                <i class="ri-list-check me-1"></i>Xem danh sách sản phẩm
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>



  <!-- Loading overlay -->
  <div class="position-fixed top-0 start-0 w-100 h-100 d-none" id="loadingOverlay" style="background: rgba(0,0,0,0.5); z-index: 9999;">
    <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
      <div class="spinner-border text-primary mb-2" role="status" style="width: 3rem; height: 3rem;">
        <span class="visually-hidden">Loading...</span>
      </div>
      <h5 class="mt-2" id="loadingMessage">Đang xử lý...</h5>
      <p class="mb-0" id="loadingDetail">Vui lòng đợi trong khi chúng tôi xử lý dữ liệu.</p>
    </div>
  </div>
@endsection
