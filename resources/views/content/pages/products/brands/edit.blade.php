@extends('layouts/layoutMaster')

@section('title', 'Chỉnh sửa thương hiệu sản phẩm')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/assets/vendor/libs/dropzone/dropzone.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/vendor/libs/dropzone/dropzone.js'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/products/brand-edit.js')
@endsection

@section('content')
  <h4 class="mb-1">Chỉnh sửa thương hiệu sản phẩm</h4>
  <p class="mb-6">Cập nhật thông tin thương hiệu sản phẩm</p>

  <div class="row">
    <div class="col-md-12">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Thông tin thương hiệu</h5>
        </div>
        <div class="card-body">
          <form id="brandForm" class="row g-4" enctype="multipart/form-data" onsubmit="return false">
            <input type="hidden" name="id" value="{{ $brand->id }}" />
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="name" name="name" class="form-control" placeholder="Nhập tên thương hiệu" value="{{ $brand->name }}" />
                <label for="name">Tên thương hiệu <span class="text-danger">*</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-check form-switch mt-4">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {{ $brand->is_active ? 'checked' : '' }} />
                <label class="form-check-label" for="is_active">Kích hoạt thương hiệu</label>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-floating form-floating-outline">
                <textarea id="description" name="description" class="form-control h-px-100" placeholder="Nhập mô tả thương hiệu">{{ $brand->description }}</textarea>
                <label for="description">Mô tả thương hiệu</label>
              </div>
            </div>
            <div class="col-md-12">
              <label class="form-label">Logo thương hiệu</label>
              @if($brand->logo)
                <div class="mb-3">
                  <img src="{{ asset('storage/' . $brand->logo) }}" alt="{{ $brand->name }}" class="img-fluid rounded" style="max-height: 100px;" />
                </div>
              @endif
              <div class="dropzone" id="brandLogoUpload">
                <div class="dz-message">
                  <h5>Kéo thả hình ảnh vào đây hoặc click để tải lên</h5>
                  <p class="mb-0">Chỉ chấp nhận file hình ảnh (JPG, PNG, GIF)</p>
                  <p class="mb-0">Kích thước tối đa: 2MB</p>
                </div>
              </div>
              <input type="hidden" name="logo" id="logo" />
            </div>
            <div class="col-12 d-flex justify-content-between">
              <a href="{{ route('brands.list') }}" class="btn btn-label-secondary">Quay lại</a>
              <button type="submit" class="btn btn-primary">Cập nhật thương hiệu</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
