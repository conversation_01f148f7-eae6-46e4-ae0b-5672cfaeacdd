@extends('layouts/layoutMaster')

@section('title', 'Quản lý thuộc t<PERSON>h sản phẩm')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-checkboxes-jquery/datatables.checkboxes.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/js/ui-popover.js'
  ])
@endsection

@section('content')
  <h4 class="mb-1">Quản lý thuộc tính sản phẩm</h4>
  <p class="mb-6">Thêm và quản lý thuộc tính cho sản phẩm</p>

  <div class="row">
    <div class="col-md-5">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Thông tin sản phẩm</h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label">Mã sản phẩm</label>
            <p class="form-control-static">{{ $product->code }}</p>
          </div>
          <div class="mb-3">
            <label class="form-label">Tên sản phẩm</label>
            <p class="form-control-static">{{ $product->name }}</p>
          </div>
          <div class="mb-3">
            <label class="form-label">Danh mục</label>
            <p class="form-control-static">{{ $product->category ? $product->category->name : 'N/A' }}</p>
          </div>
          <div class="mb-3">
            <label class="form-label">Thương hiệu</label>
            <p class="form-control-static">{{ $product->brand ? $product->brand->name : 'N/A' }}</p>
          </div>
        </div>
      </div>

      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Thêm thuộc tính mới</h5>
        </div>
        <div class="card-body">
          <form id="addAttributeForm">
            <div class="mb-3">
              <label class="form-label" for="attribute_id">Thuộc tính</label>
              <select id="attribute_id" name="attribute_id" class="form-select" required>
                <option value="">Chọn thuộc tính</option>
                @foreach($attributes as $attribute)
                  <option value="{{ $attribute->id }}" data-type="{{ $attribute->type }}">{{ $attribute->name }}</option>
                @endforeach
              </select>
            </div>

            <div id="attributeValueContainer" class="mb-3" style="display: none;">
              <!-- Attribute value input will be dynamically inserted here -->
            </div>

            <button type="submit" class="btn btn-primary">Thêm thuộc tính</button>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-7">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Thuộc tính hiện tại</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>Thuộc tính</th>
                  <th>Giá trị</th>
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody id="attributesTableBody">
                @foreach($product->productAttributes as $productAttribute)
                  <tr data-attribute-id="{{ $productAttribute->attribute_id }}">
                    <td>{{ $productAttribute->attribute->name }}</td>
                    <td>
                      @if($productAttribute->attribute->type === 'select')
                        {{ $productAttribute->attributeValue ? $productAttribute->attributeValue->value : 'N/A' }}
                      @else
                        {{ $productAttribute->text_value ?? 'N/A' }}
                      @endif
                    </td>
                    <td>
                      <button type="button" class="btn btn-sm btn-danger delete-attribute" data-attribute-id="{{ $productAttribute->attribute_id }}">
                        <i class="ri-delete-bin-line"></i>
                      </button>
                    </td>
                  </tr>
                @endforeach
              </tbody>
            </table>
          </div>

          @if($product->productAttributes->count() === 0)
            <div class="text-center py-4" id="noAttributesMessage">
              <div class="mb-2">
                <i class="ri-information-line ri-2x text-primary"></i>
              </div>
              <h6>Không có thuộc tính nào</h6>
              <p class="mb-0">Sản phẩm này chưa có thuộc tính nào</p>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>
@endsection

@section('page-script')
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const addAttributeForm = document.getElementById('addAttributeForm');
    const attributeSelect = document.getElementById('attribute_id');
    const attributeValueContainer = document.getElementById('attributeValueContainer');
    const attributesTableBody = document.getElementById('attributesTableBody');
    const noAttributesMessage = document.getElementById('noAttributesMessage');

    // Handle attribute selection change
    attributeSelect.addEventListener('change', function() {
      const selectedOption = this.options[this.selectedIndex];
      const attributeId = this.value;
      const attributeType = selectedOption.dataset.type;

      if (!attributeId) {
        attributeValueContainer.style.display = 'none';
        attributeValueContainer.innerHTML = '';
        return;
      }

      // Generate input based on attribute type
      let inputHtml = '';

      if (attributeType === 'select') {
        // Fetch attribute values via AJAX
        fetch(`/admin/attributes/${attributeId}/values`)
          .then(response => response.json())
          .then(data => {
            if (data.success && data.values.length > 0) {
              inputHtml = `
                <label class="form-label" for="attribute_value_id">Giá trị</label>
                <select id="attribute_value_id" name="attribute_value_id" class="form-select" required>
                  <option value="">Chọn giá trị</option>
                  ${data.values.map(value => `<option value="${value.id}">${value.value}</option>`).join('')}
                </select>
              `;
              attributeValueContainer.innerHTML = inputHtml;
              attributeValueContainer.style.display = 'block';
            } else {
              attributeValueContainer.innerHTML = '<div class="alert alert-warning">Không có giá trị nào cho thuộc tính này</div>';
              attributeValueContainer.style.display = 'block';
            }
          })
          .catch(error => {
            attributeValueContainer.innerHTML = '<div class="alert alert-danger">Lỗi khi tải giá trị thuộc tính</div>';
            attributeValueContainer.style.display = 'block';
          });
      } else if (attributeType === 'boolean') {
        inputHtml = `
          <label class="form-check-label">Giá trị</label>
          <div class="form-check mt-2">
            <input class="form-check-input" type="checkbox" id="text_value" name="text_value" value="1">
            <label class="form-check-label" for="text_value">Có</label>
          </div>
        `;
        attributeValueContainer.innerHTML = inputHtml;
        attributeValueContainer.style.display = 'block';
      } else if (attributeType === 'date') {
        inputHtml = `
          <label class="form-label" for="text_value">Giá trị</label>
          <input type="date" id="text_value" name="text_value" class="form-control" required>
        `;
        attributeValueContainer.innerHTML = inputHtml;
        attributeValueContainer.style.display = 'block';
      } else if (attributeType === 'number') {
        inputHtml = `
          <label class="form-label" for="text_value">Giá trị</label>
          <input type="number" id="text_value" name="text_value" class="form-control" required>
        `;
        attributeValueContainer.innerHTML = inputHtml;
        attributeValueContainer.style.display = 'block';
      } else {
        // Default to text
        inputHtml = `
          <label class="form-label" for="text_value">Giá trị</label>
          <input type="text" id="text_value" name="text_value" class="form-control" required>
        `;
        attributeValueContainer.innerHTML = inputHtml;
        attributeValueContainer.style.display = 'block';
      }
    });

    // Handle form submission
    addAttributeForm.addEventListener('submit', function(e) {
      e.preventDefault();

      const attributeId = attributeSelect.value;
      if (!attributeId) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi',
          text: 'Vui lòng chọn thuộc tính',
          customClass: {
            confirmButton: 'btn btn-primary'
          }
        });
        return;
      }

      // Check if attribute already exists
      const existingRow = attributesTableBody.querySelector(`tr[data-attribute-id="${attributeId}"]`);
      if (existingRow) {
        Swal.fire({
          icon: 'warning',
          title: 'Cảnh báo',
          text: 'Thuộc tính này đã tồn tại. Bạn có muốn cập nhật giá trị không?',
          showCancelButton: true,
          confirmButtonText: 'Cập nhật',
          cancelButtonText: 'Hủy',
          customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-outline-danger ms-1'
          }
        }).then((result) => {
          if (result.isConfirmed) {
            submitAttributeForm();
          }
        });
      } else {
        submitAttributeForm();
      }
    });

    // Function to submit the form
    function submitAttributeForm() {
      const formData = new FormData(addAttributeForm);

      // Add product ID
      const productId = '{{ $product->id }}';

      // Send AJAX request
      fetch(`/admin/products/add-attribute/${productId}`, {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          Swal.fire({
            icon: 'success',
            title: 'Thành công',
            text: data.message,
            customClass: {
              confirmButton: 'btn btn-primary'
            }
          }).then(() => {
            // Reload the page to show updated attributes
            window.location.reload();
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Lỗi',
            text: data.message,
            customClass: {
              confirmButton: 'btn btn-primary'
            }
          });
        }
      })
      .catch(error => {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi',
          text: 'Có lỗi xảy ra khi thêm thuộc tính',
          customClass: {
            confirmButton: 'btn btn-primary'
          }
        });
      });
    }

    // Handle delete attribute
    document.querySelectorAll('.delete-attribute').forEach(button => {
      button.addEventListener('click', function() {
        const attributeId = this.dataset.attributeId;
        const productId = '{{ $product->id }}';

        Swal.fire({
          icon: 'warning',
          title: 'Xác nhận',
          text: 'Bạn có chắc chắn muốn xóa thuộc tính này?',
          showCancelButton: true,
          confirmButtonText: 'Xóa',
          cancelButtonText: 'Hủy',
          customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-outline-secondary ms-1'
          }
        }).then((result) => {
          if (result.isConfirmed) {
            // Send AJAX request to delete attribute
            fetch(`/admin/products/${productId}/attributes/${attributeId}`, {
              method: 'DELETE',
              headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
              }
            })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                Swal.fire({
                  icon: 'success',
                  title: 'Thành công',
                  text: data.message,
                  customClass: {
                    confirmButton: 'btn btn-primary'
                  }
                }).then(() => {
                  // Remove the row from the table
                  const row = attributesTableBody.querySelector(`tr[data-attribute-id="${attributeId}"]`);
                  if (row) {
                    row.remove();
                  }

                  // Show "no attributes" message if no attributes left
                  if (attributesTableBody.children.length === 0) {
                    if (noAttributesMessage) {
                      noAttributesMessage.style.display = 'block';
                    }
                  }
                });
              } else {
                Swal.fire({
                  icon: 'error',
                  title: 'Lỗi',
                  text: data.message,
                  customClass: {
                    confirmButton: 'btn btn-primary'
                  }
                });
              }
            })
            .catch(error => {
              Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: 'Có lỗi xảy ra khi xóa thuộc tính',
                customClass: {
                  confirmButton: 'btn btn-primary'
                }
              });
            });
          }
        });
      });
    });
  });
</script>
@endsection
