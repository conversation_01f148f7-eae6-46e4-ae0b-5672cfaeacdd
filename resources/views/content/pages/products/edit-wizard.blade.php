@extends('layouts/layoutMaster')

@section('title', 'Chỉnh sửa sản phẩm')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/assets/vendor/libs/dropzone/dropzone.scss',
  'resources/assets/vendor/libs/bs-stepper/bs-stepper.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/vendor/libs/dropzone/dropzone.js',
  'resources/assets/vendor/libs/bs-stepper/bs-stepper.js'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/products/product-edit-wizard.js')
@endsection

@section('content')
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h4 class="mb-1">Chỉnh sửa sản phẩm</h4>
      <p class="mb-0">Cập nhật thông tin sản phẩm</p>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <!-- Product Wizard -->
      <div id="wizard-product" class="bs-stepper wizard-icons wizard-icons-example">
        <div class="bs-stepper-header m-auto border-0">
          <!-- Steps -->
          <div class="step" data-target="#basic-info">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-icon">
                <i class="ri-information-line ri-lg"></i>
              </span>
              <span class="bs-stepper-label">Thông tin cơ bản</span>
            </button>
          </div>
          <div class="line">
            <i class="ri-arrow-right-s-line"></i>
          </div>
          <div class="step" data-target="#pricing-info">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-icon">
                <i class="ri-money-dollar-circle-line ri-lg"></i>
              </span>
              <span class="bs-stepper-label">Thông tin giá</span>
            </button>
          </div>
          <div class="line">
            <i class="ri-arrow-right-s-line"></i>
          </div>
          <div class="step" data-target="#dimensions-info">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-icon">
                <i class="ri-ruler-2-line ri-lg"></i>
              </span>
              <span class="bs-stepper-label">Kích thước</span>
            </button>
          </div>
          <div class="line">
            <i class="ri-arrow-right-s-line"></i>
          </div>
          <div class="step" data-target="#inventory-info">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-icon">
                <i class="ri-store-2-line ri-lg"></i>
              </span>
              <span class="bs-stepper-label">Quản lý kho</span>
            </button>
          </div>
          <div class="line">
            <i class="ri-arrow-right-s-line"></i>
          </div>
          <div class="step" data-target="#media-info">
            <button type="button" class="step-trigger">
              <span class="bs-stepper-icon">
                <i class="ri-image-line ri-lg"></i>
              </span>
              <span class="bs-stepper-label">Hình ảnh & Thuộc tính</span>
            </button>
          </div>
        </div>

        <div class="bs-stepper-content border-top rounded-0">
          <form id="productForm" enctype="multipart/form-data" onSubmit="return false">
            <input type="hidden" name="id" value="{{ $product->id }}" />

            <!-- Basic Information -->
            <div id="basic-info" class="content">
              <div class="row">
                <div class="col-12">
                  <div class="card mb-4">
                    <div class="card-header">
                      <h5 class="card-title mb-0">Thông tin cơ bản</h5>
                    </div>
                    <div class="card-body">
                      <div class="row g-4">
                        <div class="col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input type="text" id="name" name="name" class="form-control" placeholder="Nhập tên sản phẩm" value="{{ $product->name }}" required />
                            <label for="name">Tên sản phẩm <span class="text-danger">*</span></label>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input type="text" id="code" name="code" class="form-control" placeholder="Nhập mã sản phẩm" value="{{ $product->code }}" required />
                            <label for="code">Mã sản phẩm <span class="text-danger">*</span></label>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="form-floating form-floating-outline">
                            <select id="category_id" name="category_id" class="select2 form-select" data-placeholder="Chọn danh mục" required>
                              <option value="">Chọn danh mục</option>
                              @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ $product->category_id == $category->id ? 'selected' : '' }}>{{ $category->name }}</option>
                              @endforeach
                            </select>
                            <label for="category_id">Danh mục <span class="text-danger">*</span></label>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="form-floating form-floating-outline">
                            <select id="brand_id" name="brand_id" class="select2 form-select" data-placeholder="Chọn thương hiệu" required>
                              <option value="">Chọn thương hiệu</option>
                              @foreach($brands as $brand)
                                <option value="{{ $brand->id }}" {{ $product->brand_id == $brand->id ? 'selected' : '' }}>{{ $brand->name }}</option>
                              @endforeach
                            </select>
                            <label for="brand_id">Thương hiệu <span class="text-danger">*</span></label>
                          </div>
                        </div>
                        <div class="col-md-12">
                          <div class="form-floating form-floating-outline">
                            <textarea id="description" name="description" class="form-control h-px-100" placeholder="Nhập mô tả sản phẩm">{{ $product->description }}</textarea>
                            <label for="description">Mô tả sản phẩm</label>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input type="text" id="unit" name="unit" class="form-control" placeholder="Nhập đơn vị tính" value="{{ $product->unit }}" />
                            <label for="unit">Đơn vị tính <span class="text-danger">*</span></label>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="form-floating form-floating-outline">
                            <input type="text" id="barcode" name="barcode" class="form-control" placeholder="Nhập mã vạch" value="{{ $product->barcode }}" />
                            <label for="barcode">Mã vạch</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="d-flex justify-content-between mt-4">
                <a href="{{ route('products.list') }}" class="btn btn-label-secondary">Quay lại</a>
                <button class="btn btn-primary btn-next">Tiếp theo</button>
              </div>
            </div>

            <!-- Pricing Information -->
            <div id="pricing-info" class="content">
              <div class="row">
                <div class="col-12">
                  <div class="card mb-4">
                    <div class="card-header">
                      <h5 class="card-title mb-0">Thông tin giá</h5>
                    </div>
                    <div class="card-body">
                      <div class="row g-4">
                        <div class="col-md-4">
                          <div class="form-floating form-floating-outline">
                            <input type="number" id="price" name="price" class="form-control" placeholder="Nhập giá bán" min="0" step="0.01" value="{{ $product->price }}" />
                            <label for="price">Giá bán <span class="text-danger">*</span></label>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="form-floating form-floating-outline">
                            <input type="number" id="cost" name="cost" class="form-control" placeholder="Nhập giá vốn" min="0" step="0.01" value="{{ $product->cost }}" />
                            <label for="cost">Giá vốn <span class="text-danger">*</span></label>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="form-floating form-floating-outline">
                            <select id="tax" name="tax" class="form-select">
                              <option value="0" {{ $product->tax == 0 ? 'selected' : '' }}>0%</option>
                              <option value="5" {{ $product->tax == 5 ? 'selected' : '' }}>5%</option>
                              <option value="8" {{ $product->tax == 8 ? 'selected' : '' }}>8%</option>
                              <option value="10" {{ $product->tax == 10 ? 'selected' : '' }}>10%</option>
                            </select>
                            <label for="tax">Thuế (%) <span class="text-danger">*</span></label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="d-flex justify-content-between mt-4">
                <button class="btn btn-label-secondary btn-prev">Quay lại</button>
                <button class="btn btn-primary btn-next">Tiếp theo</button>
              </div>
            </div>

            <!-- Dimensions Information -->
            <div id="dimensions-info" class="content">
              <div class="row">
                <div class="col-12">
                  <div class="card mb-4">
                    <div class="card-header">
                      <h5 class="card-title mb-0">Kích thước & Trọng lượng</h5>
                    </div>
                    <div class="card-body">
                      @php
                        // Extract dimensions values safely
                        $length = '';
                        $width = '';
                        $height = '';

                        if (is_array($product->dimensions)) {
                            $length = $product->dimensions['length'] ?? '';
                            $width = $product->dimensions['width'] ?? '';
                            $height = $product->dimensions['height'] ?? '';
                        } elseif (is_object($product->dimensions)) {
                            $length = $product->dimensions->length ?? '';
                            $width = $product->dimensions->width ?? '';
                            $height = $product->dimensions->height ?? '';
                        } elseif (is_string($product->dimensions)) {
                            $dims = json_decode($product->dimensions, true);
                            if (is_array($dims)) {
                                $length = $dims['length'] ?? '';
                                $width = $dims['width'] ?? '';
                                $height = $dims['height'] ?? '';
                            }
                        }
                      @endphp

                      <div class="row g-4">
                        <div class="col-md-3">
                          <div class="form-floating form-floating-outline">
                            <input type="number" id="weight" name="weight" class="form-control" placeholder="Nhập trọng lượng" min="0" step="0.01" value="{{ $product->weight }}" />
                            <label for="weight">Trọng lượng (kg) <span class="text-danger">*</span></label>
                          </div>
                        </div>
                        <div class="col-md-3">
                          <div class="form-floating form-floating-outline">
                            <input type="number" id="length" name="length" class="form-control" placeholder="Nhập chiều dài" min="0" step="0.01" value="{{ $length }}" />
                            <label for="length">Chiều dài (cm) <span class="text-danger">*</span></label>
                          </div>
                        </div>
                        <div class="col-md-3">
                          <div class="form-floating form-floating-outline">
                            <input type="number" id="width" name="width" class="form-control" placeholder="Nhập chiều rộng" min="0" step="0.01" value="{{ $width }}" />
                            <label for="width">Chiều rộng (cm) <span class="text-danger">*</span></label>
                          </div>
                        </div>
                        <div class="col-md-3">
                          <div class="form-floating form-floating-outline">
                            <input type="number" id="height" name="height" class="form-control" placeholder="Nhập chiều cao" min="0" step="0.01" value="{{ $height }}" />
                            <label for="height">Chiều cao (cm) <span class="text-danger">*</span></label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="d-flex justify-content-between mt-4">
                <button class="btn btn-label-secondary btn-prev">Quay lại</button>
                <button class="btn btn-primary btn-next">Tiếp theo</button>
              </div>
            </div>

            <!-- Inventory Tracking Information -->
            <div id="inventory-info" class="content">
              <div class="row">
                <div class="col-12">
                  <div class="card mb-4">
                    <div class="card-header">
                      <h5 class="card-title mb-0">Quản lý kho</h5>
                    </div>
                    <div class="card-body">
                      <div class="row g-4">
                        <div class="col-md-12">
                          <label class="form-label">Phương thức quản lý kho <span class="text-danger">*</span></label>
                          <div class="row">
                            <div class="col-md-4">
                              <div class="form-check custom-option custom-option-basic mb-3">
                                <label class="form-check-label custom-option-content" for="inventory_tracking_type_quantity">
                                  <input name="inventory_tracking_type" class="form-check-input" type="radio" value="quantity" id="inventory_tracking_type_quantity" {{ $product->inventory_tracking_type == 'quantity' ? 'checked' : '' }}>
                                  <span class="custom-option-header">
                                    <span class="h6 mb-0">Quản lý theo số lượng</span>
                                  </span>
                                  <span class="custom-option-body">
                                    <small>Sản phẩm được quản lý theo số lượng tổng, không theo dõi từng đơn vị riêng biệt</small>
                                  </span>
                                </label>
                              </div>
                            </div>
                            <div class="col-md-4">
                              <div class="form-check custom-option custom-option-basic mb-3">
                                <label class="form-check-label custom-option-content" for="inventory_tracking_type_batch">
                                  <input name="inventory_tracking_type" class="form-check-input" type="radio" value="batch" id="inventory_tracking_type_batch" {{ $product->inventory_tracking_type == 'batch' ? 'checked' : '' }}>
                                  <span class="custom-option-header">
                                    <span class="h6 mb-0">Quản lý theo lô</span>
                                  </span>
                                  <span class="custom-option-body">
                                    <small>Sản phẩm được quản lý theo lô, mỗi lô có số lô và ngày hết hạn</small>
                                  </span>
                                </label>
                              </div>
                            </div>
                            <div class="col-md-4">
                              <div class="form-check custom-option custom-option-basic mb-3">
                                <label class="form-check-label custom-option-content" for="inventory_tracking_type_serial">
                                  <input name="inventory_tracking_type" class="form-check-input" type="radio" value="serial" id="inventory_tracking_type_serial" {{ $product->inventory_tracking_type == 'serial' ? 'checked' : '' }}>
                                  <span class="custom-option-header">
                                    <span class="h6 mb-0">Quản lý theo serial/IMEI</span>
                                  </span>
                                  <span class="custom-option-body">
                                    <small>Mỗi đơn vị sản phẩm được quản lý riêng biệt với số serial/IMEI duy nhất</small>
                                  </span>
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="d-flex justify-content-between mt-4">
                <button class="btn btn-label-secondary btn-prev">Quay lại</button>
                <button class="btn btn-primary btn-next">Tiếp theo</button>
              </div>
            </div>

            <!-- Media & Attributes Information -->
            <div id="media-info" class="content">
              <div class="row">
                <div class="col-md-6">
                  <!-- Image Upload -->
                  <div class="card mb-4">
                    <div class="card-header">
                      <h5 class="card-title mb-0">Hình ảnh sản phẩm</h5>
                    </div>
                    <div class="card-body">
                      @if($product->image)
                        <div class="mb-3">
                          @php
                            $imagePath = $product->image;
                            // Kiểm tra nếu đường dẫn không bắt đầu bằng 'http' và không phải là đường dẫn tuyệt đối
                            if (!str_starts_with($imagePath, 'http') && !str_starts_with($imagePath, '/')) {
                                // Nếu đường dẫn bắt đầu bằng 'assets/', sử dụng asset() thay vì asset('storage/')
                                if (str_starts_with($imagePath, 'assets/')) {
                                    $imageUrl = asset($imagePath);
                                } else {
                                    $imageUrl = asset('storage/' . $imagePath);
                                }
                            } else {
                                $imageUrl = $imagePath;
                            }
                          @endphp
                          <img src="{{ $imageUrl }}" alt="{{ $product->name }}" class="img-fluid rounded" style="max-height: 200px;" data-product-image data-image-path="{{ $product->image }}" />
                        </div>
                      @endif
                      <div class="dropzone needsclick" id="productImageUpload">
                        <div class="dz-message needsclick">
                          <h5>Kéo thả hình ảnh vào đây hoặc click để tải lên</h5>
                          <p class="mb-0">Chỉ chấp nhận file hình ảnh (JPG, PNG, GIF)</p>
                          <p class="mb-0">Kích thước tối đa: 2MB</p>
                        </div>
                        <div class="fallback">
                          <input name="file" type="file" accept="image/*" />
                        </div>
                      </div>
                      <input type="hidden" name="image" id="image" />
                    </div>
                  </div>

                  <!-- Status -->
                  <div class="card mb-4">
                    <div class="card-header">
                      <h5 class="card-title mb-0">Trạng thái</h5>
                    </div>
                    <div class="card-body">
                      <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {{ $product->is_active ? 'checked' : '' }} />
                        <label class="form-check-label" for="is_active">Kích hoạt sản phẩm</label>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-md-6">
                  <!-- Product Attributes -->
                  <div class="card mb-4">
                    <div class="card-header">
                      <h5 class="card-title mb-0">Thuộc tính sản phẩm</h5>
                    </div>
                    <div class="card-body">
                      <div id="productAttributesContainer">
                        <div class="text-center py-4" id="loadingAttributes">
                          <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Đang tải...</span>
                          </div>
                          <p class="mt-2 mb-0">Đang tải thuộc tính...</p>
                        </div>
                        <div id="attributesList" class="row g-3" style="display: none;">
                        </div>
                        <div class="text-center py-4" id="noAttributesMessage" style="display: none;">
                          <div class="mb-2">
                            <i class="ri-information-line ri-2x text-primary"></i>
                          </div>
                          <h6>Không có thuộc tính nào</h6>
                          <p class="mb-0">Chưa có thuộc tính nào được tạo trong hệ thống</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="d-flex justify-content-between mt-4">
                <button class="btn btn-label-secondary btn-prev">Quay lại</button>
                <button type="submit" class="btn btn-success btn-submit">Cập nhật sản phẩm</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
