@extends('layouts/layoutMaster')

@section('title', 'Quản lý sản phẩm')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-checkboxes-jquery/datatables.checkboxes.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/js/ui-popover.js'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/products/product-index.js')
@endsection

@section('content')
  <h4 class="mb-1">Quản lý sản phẩm</h4>
  <p class="mb-6">Quản lý danh sách sản phẩm trong hệ thống</p>

  <!-- Filter -->
  <div class="card mb-4">
    <div class="card-body">
      <div class="row">
        <!-- Bộ lọc cơ bản -->
        <div class="col-md-4 mb-3">
          <label for="category-filter" class="form-label">Lọc theo danh mục</label>
          <select id="category-filter" class="form-select">
            <option value="">Tất cả danh mục</option>
            @foreach($categories as $category)
              <option value="{{ $category->id }}">{{ $category->name }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-4 mb-3">
          <label for="brand-filter" class="form-label">Lọc theo thương hiệu</label>
          <select id="brand-filter" class="form-select">
            <option value="">Tất cả thương hiệu</option>
            @foreach($brands as $brand)
              <option value="{{ $brand->id }}">{{ $brand->name }}</option>
            @endforeach
          </select>
        </div>
        <div class="col-md-4 mb-3">
          <label for="status-filter" class="form-label">Lọc theo trạng thái</label>
          <select id="status-filter" class="form-select">
            <option value="all">Tất cả trạng thái</option>
            <option value="active">Đang hoạt động</option>
            <option value="inactive">Ngừng hoạt động</option>
          </select>
        </div>
      </div>

      <!-- Bộ lọc mở rộng -->
      <div class="row mt-2" id="advanced-filters" style="display: none;">
        <!-- Mã sản phẩm -->
        <div class="col-md-4 mb-3">
          <label for="code-filter" class="form-label">Mã sản phẩm</label>
          <input type="text" id="code-filter" class="form-control" placeholder="Nhập mã sản phẩm">
        </div>

        <!-- Mã vạch -->
        <div class="col-md-4 mb-3">
          <label for="barcode-filter" class="form-label">Mã vạch</label>
          <input type="text" id="barcode-filter" class="form-control" placeholder="Nhập mã vạch">
        </div>

        <!-- Loại quản lý kho -->
        <div class="col-md-4 mb-3">
          <label for="inventory-type-filter" class="form-label">Loại quản lý kho</label>
          <select id="inventory-type-filter" class="form-select">
            <option value="">Tất cả</option>
            <option value="quantity">Theo số lượng</option>
            <option value="serial">Theo số serial</option>
            <option value="batch">Theo lô</option>
          </select>
        </div>

        <!-- Khoảng giá bán -->
        <div class="col-md-6 mb-3">
          <label class="form-label">Khoảng giá bán</label>
          <div class="d-flex gap-2">
            <input type="number" id="min-price-filter" class="form-control" placeholder="Giá tối thiểu">
            <span class="align-self-center">-</span>
            <input type="number" id="max-price-filter" class="form-control" placeholder="Giá tối đa">
          </div>
        </div>

        <!-- Khoảng giá vốn -->
        <div class="col-md-6 mb-3">
          <label class="form-label">Khoảng giá vốn</label>
          <div class="d-flex gap-2">
            <input type="number" id="min-cost-filter" class="form-control" placeholder="Giá vốn tối thiểu">
            <span class="align-self-center">-</span>
            <input type="number" id="max-cost-filter" class="form-control" placeholder="Giá vốn tối đa">
          </div>
        </div>
      </div>

      <!-- Nút hiển thị bộ lọc nâng cao -->
      <div class="row mt-2">
        <div class="col-12 text-center">
          <button type="button" id="toggle-advanced-filters" class="btn btn-sm btn-outline-primary">
            <span class="show-text">Hiển thị bộ lọc nâng cao</span>
            <span class="hide-text" style="display: none;">Ẩn bộ lọc nâng cao</span>
            <i class="ri-arrow-down-s-line show-icon"></i>
            <i class="ri-arrow-up-s-line hide-icon" style="display: none;"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Products List -->
  <div class="card">
    <div class="card-header border-bottom">
      <div class="d-flex justify-content-between align-items-center row">
        <div class="col-md-4 product_status"></div>
        <div class="col-md-8 text-end">
          <div class="btn-group">
            @can('products.export')
              <a href="{{ route('products.export_form') }}" class="btn btn-outline-primary">
                <i class="ri-file-download-line me-1"></i>Xuất Excel
              </a>
            @endcan
            @can('products.import')
              <a href="{{ route('products.import') }}" class="btn btn-outline-primary">
                <i class="ri-file-upload-line me-1"></i>Nhập Excel
              </a>
            @endcan
            @can('products.create')
              <a href="{{ route('products.create') }}" class="btn btn-primary">
                <i class="ri-add-line me-1"></i>Thêm sản phẩm
              </a>
            @endcan
          </div>
        </div>
      </div>
    </div>
    <div class="card-datatable table-responsive">
      <table class="datatables-products table border-top">
        <thead>
        <tr>
          <th></th>
          <th>Hình ảnh</th>
          <th>Mã sản phẩm</th>
          <th>Tên sản phẩm</th>
          <th>Danh mục</th>
          <th>Thương hiệu</th>
          <th>Giá bán</th>
          <th>Trạng thái</th>
          <th>Thao tác</th>
        </tr>
        </thead>
      </table>
    </div>
  </div>
@endsection
