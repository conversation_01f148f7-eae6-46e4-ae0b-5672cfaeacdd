<div class="tab-pane fade" id="form-tabs-asus" role="tabpanel">
  <form id="asusForm" onsubmit="return false">
    <h6>{{ __('menu.settings.asus') }}</h6>
    <div class="row g-6">
      <div class="col-md-12">
        <label class="switch">
          <input type="checkbox" class="switch-input" name="enableSettingsAsus" {{ $asus->asus_active ? 'checked' : '' }}/>
          <span class="switch-toggle-slider">
                      <span class="switch-on"></span>
                      <span class="switch-off"></span>
                      </span>
          <span class="switch-label">{{ __('common.enable') }} {{ __('menu.settings.asus') }}
                    <i class="ri-information-line"
                       data-bs-toggle="tooltip"
                       data-bs-placement="top"
                       title="{{ __('menu.settings.recaptcha_hint') }}"></i></span>
        </label>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="asusHost" class="form-control"
                 name="asusHost"
                 placeholder="ASUS Host" value="{{ $asus->asus_host }}"/>
          <label for="asusHost">ASUS Host</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="asusToken" class="form-control"
                 name="asusToken"
                 placeholder="Asus Token" value="{{ $asus->asus_token }}"/>
          <label for="asusToken">Asus Token</label>
        </div>
      </div>
    </div>
    <hr class="my-6 mx-n4" />
    <h6>{{ __('menu.settings.asus') }} Sandbox</h6>
    <div class="row g-6">
      <div class="col-md-12">
        <label class="switch">
          <input type="checkbox" class="switch-input" name="enableSettingsAsusSandbox" {{ $asus->asus_sandbox_active ? 'checked' : '' }}/>
          <span class="switch-toggle-slider">
                      <span class="switch-on"></span>
                      <span class="switch-off"></span>
                      </span>
          <span class="switch-label">{{ __('common.enable') }} {{ __('menu.settings.asus') }} Sandbox
                    <i class="ri-information-line"
                       data-bs-toggle="tooltip"
                       data-bs-placement="top"
                       title="{{ __('menu.settings.recaptcha_hint') }}"></i></span>
        </label>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="asusHostSanbox" class="form-control"
                 name="asusHostSanbox"
                 placeholder="ASUS Host Sanbox" value="{{ $asus->asus_sandbox_host }}"/>
          <label for="asusHostSanbox">ASUS Host Sanbox</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="asusTokenSandbox" class="form-control"
                 name="asusTokenSandbox"
                 placeholder="Asus Token Sandbox" value="{{ $asus->asus_sandbox_token }}"/>
          <label for="asusTokenSandbox">Asus Token Sandbox</label>
        </div>
      </div>
    </div>
    <div class="pt-6">
      <button type="submit" class="btn btn-primary me-4">Submit</button>
      <button type="reset" class="btn btn-outline-secondary">{{ __('common.reset') }}</button>
    </div>
  </form>
</div>