<div class="tab-pane fade" id="form-tabs-sap" role="tabpanel">
  <form id="sapForm" onsubmit="return false">
    <h6>{{ __('menu.settings.sap') }}</h6>
    <div class="row g-6">
      <div class="col-md-12">
        <label class="switch">
          <input type="checkbox" class="switch-input"
                 name="enableSettingsSap" {{ $sap->sap_active ? 'checked' : '' }} />
          <span class="switch-toggle-slider">
                      <span class="switch-on"></span>
                      <span class="switch-off"></span>
                      </span>
          <span class="switch-label">{{ __('common.enable') }} {{ __('menu.settings.sap') }}
                    <i class="ri-information-line"
                       data-bs-toggle="tooltip"
                       data-bs-placement="top"
                       title="{{ __('menu.settings.recaptcha_hint') }}"></i></span>
        </label>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="sapServer" class="form-control"
                 name="sapServer"
                 placeholder="SAP Server" value="{{ $sap->sap_server }}"/>
          <label for="sapServer">SAP Server</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="sapDB" class="form-control"
                 name="sapDB"
                 placeholder="SAP Database" value="{{ $sap->sap_database }}"/>
          <label for="sapDB">SAP Database</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="sapUser" class="form-control" name="sapUser"
                 placeholder="SAP User" value="{{ $sap->sap_username }}"/>
          <label for="sapUser">SAP User</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <div class="form-password-toggle">
            <div class="input-group input-group-merge">
              <div class="form-floating form-floating-outline">
                <input type="password" id="sapPassword"
                       class="form-control"
                       name="sapPassword"
                       placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                       aria-describedby="SAP Password" value="{{ $sap->sap_password }}"/>
                <label for="sapPassword">SAP Password</label>
              </div>
              <span class="input-group-text cursor-pointer"><i class="ri-eye-off-line"></i></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <hr class="my-6 mx-n4"/>
    <h6>{{ __('menu.settings.sap') }} Sandbox</h6>
    <div class="row g-6">
      <div class="col-md-12">
        <label class="switch">
          <input type="checkbox" class="switch-input"
                 name="enableSettingsSapSandbox" {{ $sap->sap_sandbox_active ? 'checked' : '' }}/>
          <span class="switch-toggle-slider">
                      <span class="switch-on"></span>
                      <span class="switch-off"></span>
                      </span>
          <span class="switch-label">{{ __('common.enable') }} {{ __('menu.settings.sap') }}
                    <i class="ri-information-line"
                       data-bs-toggle="tooltip"
                       data-bs-placement="top"
                       title="{{ __('menu.settings.recaptcha_hint') }}"></i></span>
        </label>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="sapServerSandbox" class="form-control"
                 name="sapServerSandbox"
                 placeholder="SAP Server" value="{{ $sap->sap_sandbox_server }}"/>
          <label for="sapServerSandbox">SAP Server</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="sapDBSandbox" class="form-control"
                 name="sapDBSandbox"
                 placeholder="SAP Database" value="{{ $sap->sap_sandbox_database }}"/>
          <label for="sapDBSandbox">SAP Database</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="sapUserSandbox" class="form-control" name="sapUserSandbox"
                 placeholder="SAP User" value="{{ $sap->sap_sandbox_username }}"/>
          <label for="sapUserSandbox">SAP User</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <div class="form-password-toggle">
            <div class="input-group input-group-merge">
              <div class="form-floating form-floating-outline">
                <input type="password" id="sapPasswordSandbox"
                       class="form-control"
                       name="sapPasswordSandbox"
                       placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                       aria-describedby="SAP Password Sandbox" value="{{ $sap->sap_sandbox_password }}"/>
                <label for="sapPasswordSandbox">SAP Password</label>
              </div>
              <span class="input-group-text cursor-pointer"><i class="ri-eye-off-line"></i></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pt-6">
      <button type="submit" class="btn btn-primary me-4">Submit</button>
      <button type="reset" class="btn btn-outline-secondary">{{ __('common.reset') }}</button>
    </div>
  </form>
</div>