<div class="tab-pane fade" id="form-tabs-notification" role="tabpanel">
  <form id="notificationForm" onsubmit="return false">
    <h6>{{ __('menu.settings.notifications_push') }}</h6>
    <div class="row g-6">
      <div class="col-md-12">
        <label class="switch">
          <input type="checkbox" class="switch-input" name="enableNotificationsPush" {{ $notification->push_notification ? "checked" : "" }} />
          <span class="switch-toggle-slider">
                      <span class="switch-on"></span>
                      <span class="switch-off"></span>
                      </span>
          <span class="switch-label">{{ __('common.enable') }} {{ __('menu.settings.notifications_push') }}
                    <i class="ri-information-line"
                       data-bs-toggle="tooltip"
                       data-bs-placement="top"
                       title="{{ __('menu.settings.recaptcha_hint') }}"></i></span>
        </label>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="pusherAppId" class="form-control" name="pusherAppId"
                 placeholder="Pusher App ID" value="{{ $notification->push_app_id }}" />
          <label for="pusherAppId">Pusher App ID</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="pusherAppKey" class="form-control" name="pusherAppKey"
                 placeholder="Pusher App Key" value="{{ $notification->push_api_key }}" />
          <label for="pusherAppKey">Pusher App Key</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="pusherAppSecret" class="form-control" name="pusherAppSecret"
                 placeholder="Pusher App Secret" value="{{ $notification->push_secret_key }}" />
          <label for="pusherAppSecret">Pusher App Secret</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="pusherCluster" class="form-control" name="pusherCluster"
                 placeholder="Pusher Cluster" value="{{ $notification->push_cluster }}" />
          <label for="pusherCluster">Pusher Cluster</label>
        </div>
      </div>
      <div class="col-md-6">
        <a href="javascript:void(0)" id="checkConfig" class="btn btn-primary me-4">{{ __('menu.settings.notifications.check_config') }}</a>
      </div>
    </div>
    <hr class="my-6 mx-n4" />
    <h6>{{ __('menu.settings.notifications_reverb') }}</h6>
    <div class="row g-6">
      <div class="col-md-12">
        <label class="switch">
          <input type="checkbox" class="switch-input" name="enableNotificationsReverb" {{ $notification->reverb_notification ? "checked" : "" }} />
          <span class="switch-toggle-slider">
                      <span class="switch-on"></span>
                      <span class="switch-off"></span>
                      </span>
          <span class="switch-label">{{ __('common.enable') }} {{ __('menu.settings.notifications_reverb') }}
                    <i class="ri-information-line"
                       data-bs-toggle="tooltip"
                       data-bs-placement="top"
                       title="{{ __('menu.settings.recaptcha_hint') }}"></i></span>
        </label>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="reverbAppId" class="form-control" name="reverbAppId"
                 placeholder="Reverb App ID" value="{{ $notification->reverb_app_id }}" />
          <label for="reverbAppId">Reverb App ID</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="reverbAppKey" class="form-control" name="reverbAppKey"
                 placeholder="Reverb App Key" value="{{ $notification->reverb_api_key }}" />
          <label for="reverbAppKey">Reverb App Key</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="reverbAppSecret" class="form-control" name="reverbAppSecret"
                 placeholder="Reverb App Secret" value="{{ $notification->reverb_secret_key }}" />
          <label for="reverbAppSecret">Reverb App Secret</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="reverbHost" class="form-control" name="reverbHost"
                 placeholder="reverbHost" value="{{ $notification->reverb_host }}" />
          <label for="reverbHost">Reverb Host</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="reverbPort" class="form-control" name="reverbPort"
                 placeholder="reverbPort" value="{{ $notification->reverb_port }}" />
          <label for="reverbPort">Reverb Port</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating form-floating-outline">
          <input type="text" id="reverbScheme" class="form-control" name="reverbScheme"
                 placeholder="reverbScheme" value="{{ $notification->reverb_scheme }}" />
          <label for="reverbScheme">Reverb Scheme</label>
        </div>
      </div>
      <div class="col-md-6">
        <a href="javascript:void(0)" id="checkConfig" class="btn btn-primary me-4">{{ __('menu.settings.notifications.check_config') }}</a>
      </div>
    </div>
    <hr class="my-6 mx-n4" />
    <h6>{{ __('menu.settings.notifications_slack') }}</h6>
    <div class="row g-6">
      <div class="col-md-12">
        <label class="switch">
          <input type="checkbox" class="switch-input" name="notifications_push" />
          <span class="switch-toggle-slider">
                      <span class="switch-on"></span>
                      <span class="switch-off"></span>
                      </span>
          <span class="switch-label">{{ __('common.enable') }} {{ __('menu.settings.notifications_slack') }}
                    <i class="ri-information-line"
                       data-bs-toggle="tooltip"
                       data-bs-placement="top"
                       title="{{ __('menu.settings.recaptcha_hint') }}"></i></span>
        </label>
      </div>
    </div>
    <div class="pt-6">
      <button type="submit" class="btn btn-primary me-4">Submit</button>
      <button type="reset" class="btn btn-outline-secondary">{{ __('common.reset') }}</button>
    </div>
  </form>
</div>