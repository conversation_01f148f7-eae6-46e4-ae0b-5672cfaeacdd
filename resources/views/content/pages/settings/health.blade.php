@extends('layouts/layoutMaster')

@section('title', __('menu.settings.health'))

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/assets/vendor/libs/spinkit/spinkit.scss',
  'resources/assets/vendor/libs/toastr/toastr.scss',
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/js/ui-popover.js',
  'resources/assets/vendor/libs/block-ui/block-ui.js',
  'resources/assets/vendor/libs/toastr/toastr.js'
  ])
@endsection

@section('page-script')
  @vite([
  'resources/js/pages/settings/health.js'
  ])
@endsection

@section('content')
  @if(count($checkResults?->storedCheckResults ?? []))
    <div class="row mb-12 g-6">
      @foreach ($checkResults->storedCheckResults as $result)
      <div class="col-md-6 col-xl-4">
        <div class="card {{ backgroundColor($result->status) }}">
          <div class="card-body {{ textColor($result->status) }}">
            <h5 class="card-title text-primary"><i class="{{ icon($result->status) }}"></i> {{ $result->label }}</h5>
            <p class="card-text">
              @if (!empty($result->notificationMessage))
                @if ($result->notificationMessage === 'Crashed')
                  {{ __('Failed to calculate. Your server configuration is preventing this feature from being calculated.') }}
                @else
                  {{ str_replace('The debug mode was expected to be `false`, but actually was `true`', __('Debug mode is enabled. If this is your production site, it is recommended to disable it.'), $result->notificationMessage) }}
                @endif
              @else
                {{ $result->shortSummary }}
              @endif
            </p>
          </div>
        </div>
      </div>
      @endforeach
    </div>
  @endif
  <div class="row mb-12 g-6">
    <div class="col-md-6 col-lg-4">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Clear Permission Cache</h5>
          <a href="{{ route('settings.cache.permission.clear') }}" class="btn btn-primary" id="clearPermissionCache">Clear Permission Cache</a>
        </div>
      </div>
    </div>
    <div class="col-md-6 col-lg-4">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Clear Cache</h5>
          <a href="{{ route('settings.cache.clear') }}" class="btn btn-primary" id="clearCache">Clear Cache</a>
        </div>
      </div>
    </div>
  </div>
  <div class="card">
    <div class="card-body">
      <div class="col-lg-12">
        <small class="text-light fw-medium">Server Details</small>
        <div class="demo-inline-spacing">
          <div class="table-responsive">
            <table class="table">
              <tbody class="table-border-bottom-0">
              @foreach($serverInfo as $info)
                <tr>
                  <td><i class="{{ $info['icon'] }} ri-22px text-info me-4"></i><span class="fw-medium">{{ __('common.'.$info['key']) }}</span>
                  @if($info['key'] == "version_php")
                      <span class="badge bg-label-info me-1">
                        <a href="javascript:void(0)">php info</a>
                      </span>
                  @endif
                  </td>
                  <td><span class="badge bg-label-primary me-1">{{ $info['value'] }}</span></td>
                </tr>
              @endforeach
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
@endsection