// Dropdowns
// *****************************************************************

// On hover outline
[data-trigger='hover'] {
  outline: 0;
}

.dropdown-menu {
  box-shadow: $dropdown-box-shadow;

  // Mega dropdown inside the dropdown menu
  .mega-dropdown > & {
    left: 0 !important;
    right: 0 !important;
  }

  // Badge within dropdown menu
  .badge[class^='float-'],
  .badge[class*=' float-'] {
    position: relative;
    top: 0.071em;
  }

  // Dark style
  @if $dark-style {
    .list-group-item {
      border-color: rgba-to-hex($dropdown-divider-bg, $dropdown-bg);
    }
  }

  // For RTL
  @include rtl-style {
    text-align: right;
  }
}

// Dropdown item line height
.dropdown-item {
  &.active .waves-ripple,
  &.disabled .waves-ripple {
    display: none;
  }
}

// Hidden dropdown toggle arrow
.dropdown-toggle.hide-arrow,
.dropdown-toggle-hide-arrow > .dropdown-toggle {
  &::before,
  &::after {
    display: none;
  }
}

// Dropdown caret icon

@if $enable-caret {
  // Dropdown arrow
  .dropdown-toggle::after {
    @include caret-down($caret-width);
  }
  // Dropend arrow
  .dropend .dropdown-toggle::after {
    @include caret-right($caret-width);
  }
  // Dropstart arrow
  .dropstart .dropdown-toggle::before {
    @include caret-left($caret-width);
  }
  // Dropup arrow
  .dropup .dropdown-toggle::after {
    @include caret-up($caret-width);
  }

  .dropstart .dropdown-toggle::before,
  .dropend .dropdown-toggle::after {
    vertical-align: $caret-vertical-align;
    margin-top: 0;
  }
  .dropdown-toggle.dropdown-toggle-split::after {
    margin-left: 0;
    margin-top: -0.2rem;
  }
  .dropdown-toggle::after {
    margin-top: -0.278rem;
    margin-left: 0.8em;
    width: 0.55em;
    height: 0.55em;
    border: 2px solid;
    border-top: 0;
    border-left: 0;
  }

  .dropup .dropdown-toggle::after {
    margin-top: 0.25rem;
    margin-left: 0.667em;
    width: 0.55em;
    height: 0.55em;
    border: 2px solid;
    border-bottom: 0;
    border-left: 0;
    transform: rotate(-45deg);
  }

  .dropend .dropdown-toggle::after {
    margin-top: 0;
    margin-left: 0.45em;
    width: 0.55em;
    height: 0.55em;
    border: 2px solid;
    border-top: 0;
    border-left: 0;
    transform: rotate(-45deg);
  }

  .dropstart .dropdown-toggle::before {
    margin-top: 0;
    margin-right: 0.55em;
    width: 0.55em;
    height: 0.55em;
    border: 2px solid;
    border-top: 0;
    border-right: 0;
    transform: rotate(45deg);
  }

  @include rtl-only {
    .dropdown-toggle:not(.dropdown-toggle-split)::after {
      margin-left: 0;
      margin-right: $caret-spacing;
    }
  }
}

@include rtl-only {
  // Dropdown menu alignment
  @each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-up($breakpoint) {
      $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

      .dropdown-menu#{$infix}-start {
        --bs-position: start;

        &[data-bs-popper] {
          left: auto;
          right: 0;
        }
      }

      .dropdown-menu#{$infix}-end {
        --bs-position: end;

        &[data-bs-popper] {
          left: 0;
          right: auto;
        }
      }
    }
  }
}
