// Alerts
// *******************************************************************************

// Alert mixins
@each $state, $value in $theme-colors {
  @if $state != primary and $state != light {
    @include template-alert-variant('.alert-#{$state}', $value);
    @include template-alert-outline-variant('.alert-outline-#{$state}', $value);
    @include template-alert-solid-variant('.alert-solid-#{$state}', $value);
  }
}

// Adjust close link position
.alert-dismissible {
  .btn-close {
    padding: $alert-padding-y * 1.5 $alert-padding-x;
  }
}

// Alert icon styles and line-height
.alert {
  line-height: 1.375rem;

  .alert-icon {
    color: $white;
    height: $alert-icon-size;
    width: $alert-icon-size;
    padding: $spacer * 0.75;
    margin-right: $spacer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &[class*='alert-solid-'] {
    .alert-icon {
      background-color: $white;
      box-shadow: $box-shadow-xs;
    }
  }
}

// RTL
// *******************************************************************************

@include rtl-only {
  .alert-dismissible {
    padding-left: $alert-dismissible-padding-r;
    padding-right: $alert-padding-x;
  }

  .alert-dismissible .btn-close {
    right: auto;
    left: 0;
  }
  .alert .alert-icon {
    margin-right: 0;
    margin-left: $spacer;
  }
}
