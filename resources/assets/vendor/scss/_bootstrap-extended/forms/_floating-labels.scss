// Floating Labels
// *******************************************************************************

.form-floating {
  > .form-control,
  > .form-control-plaintext,
  > .form-select {
    &:focus,
    &:not(:placeholder-shown) {
      ~ label {
        &:after {
          background-color: $card-bg !important;
        }
      }
    }
  }

  &.form-floating-outline {
    > .form-control,
    > .form-control-plaintext,
    > .form-select {
      &:disabled {
        background-color: $input-bg;
        border-color: $border-color;
        color: $text-muted;

        ~ .form-text {
          color: $form-floating-label-disabled-color;
        }
      }
    }

    > .form-control-plaintext {
      border: none;
    }
  }
}

// Floating label (Filled)
.form-floating:not(.form-floating-outline) {
  > .form-control,
  > .form-control-plaintext,
  > .form-select {
    background-color: $form-floating-input-bg;
    border: 0;
    border-bottom: 1px solid $body-color;
    @include border-bottom-start-radius(0);
    @include border-bottom-end-radius(0);
    padding-bottom: calc($form-floating-padding-y - $input-border-width);

    &.is-invalid {
      border-bottom-width: 2px;
    }

    &:hover {
      background-color: $form-floating-hover-bg;
      border-color: $headings-color;
    }

    &:focus,
    &:not(:placeholder-shown) {
      padding: $form-floating-input-padding-t $form-floating-padding-x $form-floating-input-padding-b $form-floating-padding-x;

      &::placeholder {
        color: $input-placeholder-color;
      }

      ~ label {
        &:after {
          background-color: transparent !important;
        }
      }
    }

    // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped
    &:-webkit-autofill {
      padding: $form-floating-input-padding-t $form-floating-padding-x calc($form-floating-input-padding-b - 1px) $form-floating-padding-x;
    }

    &:disabled {
      pointer-events: none;
      opacity: $form-floating-disabled-opacity;
    }
  }

  // Transform bottom bordered
  > .form-control:focus,
  > .form-select:focus {
    ~ .form-floating-focused {
      transform: scaleX(1);
    }
  }
}

// Floating label (Filled) border bottom
.form-floating-focused {
  position: relative;
  top: -1px;
  z-index: 9;
  display: block;
  width: 100%;
  height: $input-focus-border-width;
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
  transform: scaleX(0);
}

// Floating label (Outlined)
.form-floating.form-floating-outline {
  > .form-control,
  > .form-select {
    padding: calc($form-floating-padding-y - $input-border-width) calc($form-floating-padding-x - $input-border-width);

    &:focus {
      border-width: $input-focus-border-width;
    }

    &:focus,
    &:not(:placeholder-shown) {
      padding-top: $form-floating-padding-y;
      padding-bottom: $form-floating-padding-y;

      &::placeholder {
        color: $input-placeholder-color;
      }

      // Floating (outline) label position on focus
      ~ label {
        width: auto;
        height: auto;
        padding: $input-focus-border-width $form-floating-label-padding-x;
        margin-left: $form-floating-label-margin;
        margin-top: 0.125rem;
        transform: $form-floating-outline-label-transform;
        opacity: 1;
        font-size: $font-size-sm;

        &:after {
          content: '';
          position: absolute;
          width: 100%;
          inset-inline-start: 0;
          top: 0.35rem;
          z-index: -1;
        }
      }
    }

    .was-validated & {
      padding: calc($form-floating-padding-y - $input-border-width) calc($form-floating-padding-x - $input-focus-border-width);
    }

    // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped
    &:-webkit-autofill {
      padding-top: $form-floating-padding-y;
      padding-bottom: $form-floating-padding-y;

      ~ label {
        transform: $form-floating-outline-label-transform;
        opacity: 1;
      }
    }
  }

  // Form control padding on focus
  &:focus-within {
    > .form-control:first-child,
    > .form-select:first-child {
      padding: calc($form-floating-padding-y - 1px) calc($form-floating-padding-x - 2px);
    }
  }

  // Input group (not first-child) floating (outline) label position
  .input-group & {
    &:not(:first-child) {
      > .form-control:focus,
      > .form-control:not(:placeholder-shown),
      > .form-select {
        ~ label {
          padding: calc($input-focus-border-width * 0.5) $form-floating-label-padding-x !important;
          margin-left: calc($input-focus-border-width * -1);
          transform: $form-floating-outline-label-transform;
        }
      }
    }
  }
}

// File Upload : Floating label File and text alignment
.form-floating {
  .form-control {
    &::file-selector-button {
      padding: $form-floating-padding-y $form-floating-padding-x;
      margin: (-$form-floating-padding-y) (-$form-floating-padding-x);
      margin-inline-end: $form-floating-padding-x;
    }
  }

  > label {
    width: 100%;
    color: $text-muted;
    padding: 0.8125rem $form-floating-padding-x;
  }

  > .form-control-plaintext:not(:placeholder-shown) {
    padding-top: 2.1895rem;
  }

  > .form-control:focus,
  > .form-select {
    & ~ label:after {
      border-radius: 0;
    }
  }
}

// LTR

@include ltr-only {
  .form-floating {
    & ~ .form-text,
    .form-text {
      margin-left: $input-padding-x;
    }

    &:not(.form-floating-outline) {
      .form-select {
        background-position: right $form-select-padding-x center;
      }
    }
  }
}

// RTL
@include rtl-only {
  // Floating label (Filled)
  .form-floating:not(.form-floating-outline) {
    > label {
      right: 0;
      left: inherit;
      transform-origin: 100% 0;
    }

    .form-select {
      background-position: left $form-select-padding-x center;
    }

    > .form-control:focus,
    > .form-control:not(:placeholder-shown),
    > .form-select {
      ~ label {
        transform: $form-floating-label-transform-rtl;
      }
    }

    > .form-control:-webkit-autofill {
      ~ label {
        transform: $form-floating-label-transform-rtl;
      }
    }
  }

  // Floating label (Outlined)
  .form-floating.form-floating-outline {
    > label {
      right: 0;
      left: inherit;
      transform-origin: 100% 0;
    }

    // Floating (outline) label position
    > .form-control:focus,
    > .form-control:not(:placeholder-shown),
    > .form-select {
      ~ label {
        margin-right: $form-floating-label-margin;
        margin-left: 0px;
        transform: $form-floating-outline-label-transform-rtl;
      }
    }

    > .form-control:-webkit-autofill {
      ~ label {
        transform: $form-floating-outline-label-transform-rtl;
      }
    }
  }
  .input-group {
    .form-floating.form-floating-outline:not(:first-child) {
      > .form-control:focus,
      > .form-control:not(:placeholder-shown),
      > .form-select {
        ~ label {
          margin-right: calc($input-focus-border-width * -1);
          transform: $form-floating-outline-label-transform-rtl;
        }
      }
    }
  }
  .form-floating {
    .form-text,
    & ~ .form-text {
      margin-right: $input-padding-x;
    }
  }
}
