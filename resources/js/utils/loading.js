/**
 * Loading utility functions
 */

// Immediately execute to add functions to window object
(function () {
    // Show loading overlay similar to the site's main loading
    function showLoading() {
        // Create loading overlay if it doesn't exist
        if (!document.getElementById('ajax-loading')) {
            const loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'ajax-loading';
            loadingOverlay.className = 'loading';
            loadingOverlay.style.position = 'fixed';
            loadingOverlay.style.width = '100%';
            loadingOverlay.style.height = '100vh';
            loadingOverlay.style.zIndex = '9999';
            loadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
            loadingOverlay.style.display = 'flex';
            loadingOverlay.style.justifyContent = 'center';
            loadingOverlay.style.alignItems = 'center';
            loadingOverlay.style.top = '0';
            loadingOverlay.style.left = '0';

            // Check if dark mode is enabled
            const isDarkMode = document.documentElement.classList.contains('dark-style');

            if (isDarkMode) {
                loadingOverlay.style.backgroundColor = 'rgba(40, 42, 66, 0.7)';
                // Create sk-fold spinner for dark mode
                loadingOverlay.innerHTML = `
          <div class="sk-fold sk-primary">
            <div class="sk-fold-cube"></div>
            <div class="sk-fold-cube"></div>
            <div class="sk-fold-cube"></div>
            <div class="sk-fold-cube"></div>
          </div>
        `;
            } else {
                // Use loading image for light mode
                const img = document.createElement('img');
                img.src = '/assets/img/pages/loading-light.webp';
                img.alt = 'Loading';
                loadingOverlay.appendChild(img);
            }

            document.body.appendChild(loadingOverlay);
        } else {
            // Show existing loading overlay
            document.getElementById('ajax-loading').style.display = 'flex';
        }
    }

    // Hide loading overlay
    function hideLoading() {
        const loadingOverlay = document.getElementById('ajax-loading');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    // Export functions to window object
    window.showLoading = showLoading;
    window.hideLoading = hideLoading;
})();
