/**
 * Good Receipt View Batches
 */

'use strict';

// DOM elements
const viewBatchesButtons = document.querySelectorAll('.view-batches-btn');
const batchProductName = document.getElementById('batch-product-name');

// Global variables
let viewBatchesModal = null;
let goodReceiptId = null;
let baseUrl = window.location.origin;
let currentLocale = document.documentElement.lang || 'vi';

// Initialize components
document.addEventListener('DOMContentLoaded', function () {
  // Lấy dữ liệu từ thuộc tính data-* của container
  const container = document.getElementById('good-receipt-container');
  if (container) {
    goodReceiptId = parseInt(container.dataset.goodReceiptId);
  } else {
    // Fallback: Lấy ID từ URL nếu không có container
    goodReceiptId = parseInt(window.location.pathname.split('/').pop());
  }

  // Initialize Bootstrap Modal
  if (document.getElementById('viewBatchesModal')) {
    viewBatchesModal = new bootstrap.Modal(document.getElementById('viewBatchesModal'));
  }

  // Add event listeners to view batches buttons
  viewBatchesButtons.forEach(btn => {
    btn.addEventListener('click', function() {
      const itemId = this.dataset.id;
      const productName = this.dataset.productName;
      viewBatches(itemId, productName);
    });
  });
});

// View batches
function viewBatches(itemId, productName) {
  // Set product name in modal title
  if (batchProductName) {
    batchProductName.textContent = productName;
  }

  // Show loading row
  const loadingRow = document.getElementById('loadingBatchesRow');
  const noBatchesRow = document.getElementById('noBatchesRow');
  const batchesTable = document.getElementById('batchesTable');

  // Show loading row
  if (loadingRow) {
    loadingRow.style.display = '';
  }

  if (noBatchesRow) {
    noBatchesRow.style.display = 'none';
  }

  // Clear existing rows except loading and no batches rows
  if (batchesTable) {
    const rows = batchesTable.querySelectorAll('tbody tr:not(#loadingBatchesRow):not(#noBatchesRow)');
    rows.forEach(row => row.remove());
  }

  // Show modal
  viewBatchesModal.show();

  // Fetch batch data from server
  fetchBatchesFromServer(itemId);
}

// Fetch batches from server
function fetchBatchesFromServer(itemId) {
  showLoading();

  fetch(`${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/${goodReceiptId}/batches/${itemId}`)
    .then(response => {
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.json();
    })
    .then(data => {
      hideLoading();

      // Hide loading row
      const loadingRow = document.getElementById('loadingBatchesRow');
      if (loadingRow) {
        loadingRow.style.display = 'none';
      }

      // Check if there is batch data
      if (!data.batches || data.batches.length === 0) {
        const noBatchesRow = document.getElementById('noBatchesRow');
        if (noBatchesRow) {
          noBatchesRow.style.display = '';
        }
        return;
      }

      // Render batch list
      renderBatchList(data.batches);
    })
    .catch(error => {
      hideLoading();
      console.error('Error fetching batches:', error);

      // Hide loading row
      const loadingRow = document.getElementById('loadingBatchesRow');
      if (loadingRow) {
        loadingRow.style.display = 'none';
      }

      // Show error message
      const noBatchesRow = document.getElementById('noBatchesRow');
      if (noBatchesRow) {
        noBatchesRow.style.display = '';
        noBatchesRow.querySelector('td').textContent = 'Đã xảy ra lỗi khi tải dữ liệu batch';
      }
    });
}

// Render batch list
function renderBatchList(batches) {
  const batchesTable = document.getElementById('batchesTable');
  const tbody = batchesTable.querySelector('tbody');

  batches.forEach((batch, index) => {
    const row = document.createElement('tr');

    row.innerHTML = `
      <td>${index + 1}</td>
      <td>${batch.batch_number}</td>
      <td class="text-end">${formatNumber(batch.quantity)}</td>
      <td>${formatDate(batch.expiry_date)}</td>
    `;

    tbody.appendChild(row);
  });
}

// Format date for display
function formatDate(dateString) {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString; // Return original if invalid

    // Format as dd/mm/yyyy
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (e) {
    console.error('Error formatting date:', e);
    return dateString; // Return original on error
  }
}

// Format number for display
function formatNumber(number) {
  if (number === null || number === undefined) return '';

  try {
    // Format number with thousand separator
    // The regex /\\B(?=(\\d{3})+(?!\\d))/g works as follows:
    // \\B - Matches a position that is not a word boundary (i.e., between two word characters or two non-word characters)
    // (?=(\\d{3})+(?!\\d)) - Positive lookahead that matches positions where:
    //   (\\d{3})+ - One or more groups of exactly 3 digits
    //   (?!\\d) - Not followed by another digit
    // /g - Global flag to replace all matches, not just the first one
    // 
    // For example, in the number 1234567:
    // - It finds positions before 7, 4, and 1 (counting from right)
    // - It inserts a dot at each position: 1.234.567
    return number.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, '.');

    // Alternative modern approach using Intl.NumberFormat (as used in other files):
    // return new Intl.NumberFormat('vi-VN').format(number);
  } catch (e) {
    console.error('Error formatting number:', e);
    return number.toString(); // Return original on error
  }
}
