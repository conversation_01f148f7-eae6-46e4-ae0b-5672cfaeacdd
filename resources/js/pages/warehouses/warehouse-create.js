/**
 * Warehouse Management - Create
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
    (function () {
        const warehouseForm = document.getElementById('warehouseForm');

        // Form validation
        if (warehouseForm) {
            const fv = FormValidation.formValidation(warehouseForm, {
                fields: {
                    name: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập tên kho hàng'
                            },
                            stringLength: {
                                min: 3,
                                max: 255,
                                message: 'Tên kho hàng phải có từ 3 đến 255 ký tự'
                            }
                        }
                    },
                    code: {
                        validators: {
                            notEmpty: {
                                message: '<PERSON>ui lòng nhập mã kho hàng'
                            },
                            stringLength: {
                                min: 2,
                                max: 50,
                                message: 'Mã kho hàng phải có từ 2 đến 50 ký tự'
                            },
                            regexp: {
                                regexp: /^[a-zA-Z0-9-_]+$/,
                                message: '<PERSON><PERSON> kho hàng chỉ được chứa chữ cái, s<PERSON>, d<PERSON><PERSON> gạch ngang và gạch dưới'
                            }
                        }
                    },
                    phone: {
                        validators: {
                            stringLength: {
                                max: 20,
                                message: '<PERSON><PERSON> điện thoại không được vượt quá 20 ký tự'
                            },
                            regexp: {
                                regexp: /^[0-9+\-\s()]*$/,
                                message: 'Số điện thoại không hợp lệ'
                            }
                        }
                    },
                    email: {
                        validators: {
                            emailAddress: {
                                message: 'Email không hợp lệ'
                            }
                        }
                    }
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap5: new FormValidation.plugins.Bootstrap5({
                        eleValidClass: '',
                        rowSelector: '.col-md-6, .col-md-12'
                    }),
                    submitButton: new FormValidation.plugins.SubmitButton(),
                    autoFocus: new FormValidation.plugins.AutoFocus()
                }
            }).on('core.form.valid', function () {
                // Get form data
                const formData = {
                    name: warehouseForm.querySelector('[name="name"]').value,
                    code: warehouseForm.querySelector('[name="code"]').value,
                    address: warehouseForm.querySelector('[name="address"]').value,
                    phone: warehouseForm.querySelector('[name="phone"]').value,
                    email: warehouseForm.querySelector('[name="email"]').value,
                    description: warehouseForm.querySelector('[name="description"]').value,
                    is_active: warehouseForm.querySelector('[name="is_active"]').checked ? 1 : 0,
                    is_default: warehouseForm.querySelector('[name="is_default"]').checked ? 1 : 0,
                    is_transit: warehouseForm.querySelector('[name="is_transit"]').checked ? 1 : 0,
                    _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                };

                // Submit form data via AJAX
                $.ajax({
                    type: 'POST',
                    url: '/admin/warehouses/store',
                    data: formData,
                    success: function (response) {
                        if (response.code === 200) {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.text,
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function () {
                                window.location.href = '/admin/warehouses';
                            });
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.text,
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        }
                    },
                    error: function (error) {
                        let errorMessage = 'Có lỗi xảy ra khi tạo kho hàng. Vui lòng thử lại sau.';

                        if (error.responseJSON && error.responseJSON.errors) {
                            errorMessage = Object.values(error.responseJSON.errors).flat().join('<br>');
                        }

                        Swal.fire({
                            title: 'Lỗi!',
                            html: errorMessage,
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            });
        }
    })();
});
