/**
 * Warehouse Reports
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
    // Flatpickr initialization
    const flatpickrDate = document.querySelectorAll('.flatpickr-date');
    if (flatpickrDate.length) {
        flatpickrDate.forEach(function (flatpickrInstance) {
            flatpickrInstance.flatpickr({
                monthSelectorType: 'static',
                dateFormat: 'Y-m-d'
            });
        });
    }

    // Report type change handler
    const reportTypeSelect = document.getElementById('report-type');
    const dateRangeContainers = document.querySelectorAll('.date-range-container');

    if (reportTypeSelect) {
        reportTypeSelect.addEventListener('change', function () {
            const reportType = this.value;

            // Show/hide date range fields based on report type
            if (reportType === 'transactions' || reportType === 'expiry') {
                dateRangeContainers.forEach(container => {
                    container.classList.remove('d-none');
                });
            } else {
                dateRangeContainers.forEach(container => {
                    container.classList.add('d-none');
                });
            }
        });
    }

    // Export report button handler
    const exportButton = document.querySelector('a[href*="warehouses.reports.export"]');
    if (exportButton) {
        exportButton.addEventListener('click', function (e) {
            e.preventDefault();

            // Get form data
            const form = document.getElementById('reportFilterForm');
            const formData = new FormData(form);

            // Build query string
            let queryString = '';
            for (const [key, value] of formData.entries()) {
                if (value) {
                    queryString += (queryString ? '&' : '?') + key + '=' + encodeURIComponent(value);
                }
            }

            // Redirect to export URL with query parameters
            window.location.href = this.getAttribute('href').split('?')[0] + queryString;
        });
    }
});
