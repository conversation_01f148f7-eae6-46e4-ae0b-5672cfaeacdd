/**
 * Purchase Order Index
 */

'use strict';

// Datatable (jquery)
$(function () {
  let borderColor, bodyBg, headingColor;

  if (isDarkStyle) {
    borderColor = config.colors.borderColor;
    bodyBg = config.colors.bodyBg;
    headingColor = config.colors.headingColor;
  } else {
    borderColor = config.colors.borderColor;
    bodyBg = config.colors.bodyBg;
    headingColor = config.colors.headingColor;
  }

  // Datatable
  // --------------------------------------------------------------------
  const dt_purchase_orders = $('.datatables-purchase-orders');
  let dt_purchase_order; // Khai báo biến ở phạm vi ngoài để có thể truy cập từ bất kỳ đâu

  if (dt_purchase_orders.length) {
    dt_purchase_order = dt_purchase_orders.DataTable({
      ajax: {
        url: `${baseUrl}admin/warehouses/purchase-orders/datatable`,
        type: 'POST',
        data: function (d) {
          d.warehouse_id = $('#warehouse-filter').val();
          d.supplier_id = $('#supplier-filter').val();
          d.status = $('#status-filter').val();
          d.date_from = $('#date-from').val();
          d.date_to = $('#date-to').val();
        }
      },
      columns: [
        { data: '' },
        { data: 'code' },
        { data: 'created_at' },
        { data: 'warehouse.name' },
        { data: 'supplier.name' },
        { data: 'expected_date' },
        { data: 'status' },
        { data: 'total_amount' },
        { data: 'actions' }
      ],
      columnDefs: [
        {
          className: 'control',
          orderable: false,
          searchable: false,
          responsivePriority: 2,
          targets: 0,
          render: function (data, type, full, meta) {
            return '';
          }
        },
        {
          targets: 1,
          render: function (data, type, full, meta) {
            return `<a href="${baseUrl}admin/warehouses/purchase-orders/${full.id}" class="fw-medium">${full.code}</a>`;
          }
        },
        {
          targets: 6,
          render: function (data, type, full, meta) {
            const statusObj = {
              draft: {
                title: 'Nháp',
                class: 'bg-label-secondary'
              },
              pending: {
                title: 'Chờ duyệt',
                class: 'bg-label-warning'
              },
              approved: {
                title: 'Đã duyệt',
                class: 'bg-label-info'
              },
              rejected: {
                title: 'Từ chối',
                class: 'bg-label-danger'
              },
              partially_received: {
                title: 'Nhập một phần',
                class: 'bg-label-primary'
              },
              received: {
                title: 'Đã nhập kho',
                class: 'bg-label-success'
              },
              cancelled: {
                title: 'Đã hủy',
                class: 'bg-label-dark'
              }
            };
            return `<span class="badge ${statusObj[full.status].class}">${statusObj[full.status].title}</span>`;
          }
        },
        {
          targets: -1,
          title: 'Thao tác',
          orderable: false,
          searchable: false,
          render: function (data, type, full, meta) {
            let actions = `
              <div class="d-inline-block">
                <a href="${baseUrl}admin/warehouses/purchase-orders/${full.id}" class="btn btn-sm btn-icon">
                  <i class="ri-eye-line"></i>
                </a>
              </div>
            `;

            // Thêm nút tạo phiếu nhập hàng nếu phiếu đặt hàng đã được duyệt
            if (full.status === 'approved') {
              actions += `
                <div class="d-inline-block ms-1">
                  <a href="${baseUrl}admin/warehouses/purchase-orders/${full.id}/create-good-receipt" class="btn btn-sm btn-icon btn-info" data-bs-toggle="tooltip" data-bs-placement="top" title="Tạo phiếu nhập hàng">
                    <i class="ri-file-list-3-line"></i>
                  </a>
                </div>
              `;
            }

            return actions;
          }
        }
      ],
      order: [[1, 'desc']],
      dom: '<"card-header d-flex flex-wrap py-3"<"me-5"f><"d-flex justify-content-center justify-content-md-end"B>><"row"<"col-sm-12"tr>><"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
      buttons: [
        {
          extend: 'collection',
          className: 'btn btn-label-primary dropdown-toggle',
          text: '<i class="ri-download-line me-1"></i>Xuất',
          buttons: [
            {
              extend: 'excel',
              text: '<i class="ri-file-excel-line me-1"></i>Excel',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7]
              }
            },
            {
              extend: 'pdf',
              text: '<i class="ri-file-pdf-line me-1"></i>PDF',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7]
              }
            },
            {
              extend: 'print',
              text: '<i class="ri-printer-line me-1"></i>In',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7]
              }
            }
          ]
        }
      ],
      responsive: {
        details: {
          display: $.fn.dataTable.Responsive.display.modal({
            header: function (row) {
              const data = row.data();
              return 'Chi tiết phiếu đặt hàng';
            }
          }),
          type: 'column',
          renderer: function (api, rowIdx, columns) {
            const data = $.map(columns, function (col, i) {
              return col.title !== '' // ? Do not show row in modal popup if title is blank (for check box)
                ? `<tr data-dt-row="${col.rowIndex}" data-dt-column="${col.columnIndex}">
                    <td>${col.title}:</td>
                    <td>${col.data}</td>
                  </tr>`
                : '';
            }).join('');

            return data ? $('<table class="table"/><tbody />').append(data) : false;
          }
        }
      },
      language: {
        url: '/assets/vendor/libs/datatables/i18n/vi.json'
      }
    });
  }

  // Filter form control to default size
  // ? setTimeout used for multilingual table initialization
  setTimeout(() => {
    $('.dataTables_filter .form-control').removeClass('form-control-sm');
    $('.dataTables_length .form-select').removeClass('form-select-sm');
  }, 300);

  // Select2
  // --------------------------------------------------------------------
  const select2 = $('.select2');
  if (select2.length) {
    select2.each(function () {
      const $this = $(this);
      select2Focus($this);
      $this.wrap('<div class="position-relative"></div>').select2({
        placeholder: $this.data('placeholder'),
        dropdownParent: $this.parent()
      });
    });
  }

  // Flatpickr
  // --------------------------------------------------------------------
  const flatpickrDate = document.querySelectorAll('.flatpickr-input');
  if (flatpickrDate) {
    flatpickrDate.forEach(function (flatpickrElement) {
      flatpickrElement.flatpickr({
        monthSelectorType: 'static',
        altInput: true,
        altFormat: 'd/m/Y',
        dateFormat: 'Y-m-d'
      });
    });
  }

  // Filter button
  // --------------------------------------------------------------------
  $('#apply-filters').on('click', function () {
    $('#loading-indicator').show();
    dt_purchase_order.ajax.reload();
    $('#loading-indicator').hide();
    $('#clear-filters').addClass('active');
  });

  // Clear filter button
  // --------------------------------------------------------------------
  $('#clear-filters').on('click', function () {
    $('#warehouse-filter').val('').trigger('change');
    $('#supplier-filter').val('').trigger('change');
    $('#status-filter').val('').trigger('change');
    $('#date-from').val('').trigger('change');
    $('#date-to').val('').trigger('change');
    dt_purchase_order.ajax.reload();
    $(this).removeClass('active');
  });

  // Add new button
  // --------------------------------------------------------------------
  $('#add-new-btn').on('click', function () {
    window.location.href = `${baseUrl}admin/warehouses/purchase-orders/create`;
  });
});
