/**
 * Good Receipt Import Handling
 */

'use strict';

// DOM elements
const importItemButtons = document.querySelectorAll('.import-item-btn');
const saveQuantityBtn = document.getElementById('saveQuantityBtn');
const saveBatchBtn = document.getElementById('saveBatchBtn');
const saveSerialBtn = document.getElementById('saveSerialBtn');
const addBatchBtn = document.getElementById('addBatchBtn');
const addSerialBtn = document.getElementById('addSerialBtn');
const batchNumberInput = document.getElementById('batch_number_input');
const serialNumberInput = document.getElementById('serial_number');
const serialFileInput = document.getElementById('serial_file');
const downloadTemplateBtn = document.getElementById('downloadTemplateBtn');

// Global variables
let importQuantityModal = null;
let importBatchModal = null;
let importSerialModal = null;
let currentItemId = null;
let currentProductId = null;
let currentTrackingType = null;
let currentQuantity = null;
let scannedSerials = {};
let scannedBatches = {};
let goodReceiptId = null;
let serialCount = 0;
let batchCount = 0;
let baseUrl = window.location.origin;
let currentLocale = document.documentElement.lang || 'vi';

// Initialize components
document.addEventListener('DOMContentLoaded', function () {
  // Event delegation for view-batches links
  document.addEventListener('click', function(e) {
    if (e.target && e.target.classList.contains('view-batches')) {
      const itemId = e.target.dataset.itemId;
      showBatchesModal(itemId);
    }
  });
  // Lấy dữ liệu từ thuộc tính data-* của container
  const container = document.getElementById('good-receipt-container');
  if (container) {
    goodReceiptId = parseInt(container.dataset.goodReceiptId);
  } else {
    // Fallback: Lấy ID từ URL nếu không có container
    goodReceiptId = parseInt(window.location.pathname.split('/').pop());
  }

  // Initialize Bootstrap Modals
  if (document.getElementById('importQuantityModal')) {
    importQuantityModal = new bootstrap.Modal(document.getElementById('importQuantityModal'));
  }

  if (document.getElementById('importBatchModal')) {
    importBatchModal = new bootstrap.Modal(document.getElementById('importBatchModal'));
  }

  if (document.getElementById('importSerialModal')) {
    importSerialModal = new bootstrap.Modal(document.getElementById('importSerialModal'));
  }

  // Initialize Select2 - Sử dụng setTimeout để đảm bảo Select2 đã được tải
  setTimeout(() => {
    if (document.getElementById('quantity_warehouse_area')) {
      const quantityWarehouseArea = document.getElementById('quantity_warehouse_area');
      if (typeof $(quantityWarehouseArea).select2 === 'function') {
        $(quantityWarehouseArea).select2({
          dropdownParent: $('#importQuantityModal')
        });
      }
    }

    if (document.getElementById('batch_warehouse_area')) {
      const batchWarehouseArea = document.getElementById('batch_warehouse_area');
      if (typeof $(batchWarehouseArea).select2 === 'function') {
        $(batchWarehouseArea).select2({
          dropdownParent: $('#importBatchModal')
        });
      }
    }

    if (document.getElementById('serial_warehouse_area')) {
      const serialWarehouseArea = document.getElementById('serial_warehouse_area');
      if (typeof $(serialWarehouseArea).select2 === 'function') {
        $(serialWarehouseArea).select2({
          dropdownParent: $('#importSerialModal')
        });
      }
    }
  }, 500);

  // Event listeners
  importItemButtons.forEach(btn => {
    btn.addEventListener('click', function() {
      const row = this.closest('tr');
      currentItemId = row.dataset.itemId;
      currentProductId = row.dataset.productId;
      currentTrackingType = row.dataset.trackingType;
      currentQuantity = parseFloat(row.dataset.quantity);

      // Hiển thị modal tương ứng với loại sản phẩm
      showImportModal();
    });
  });

  if (saveQuantityBtn) {
    saveQuantityBtn.addEventListener('click', saveQuantityImport);
  }

  if (saveBatchBtn) {
    saveBatchBtn.addEventListener('click', saveBatchImport);
  }

  if (saveSerialBtn) {
    saveSerialBtn.addEventListener('click', saveSerialImport);
  }

  if (addBatchBtn) {
    addBatchBtn.addEventListener('click', addBatch);
  }

  if (addSerialBtn) {
    addSerialBtn.addEventListener('click', addSerial);
  }

  if (batchNumberInput) {
    // Xử lý khi nhấn Enter
    batchNumberInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        addBatch();
      }
    });
  }

  if (serialNumberInput) {
    // Xử lý khi nhấn Enter
    serialNumberInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        addSerial();
      }
    });

    // Xử lý tự động submit sau khi nhập
    let inputTimeout;
    serialNumberInput.addEventListener('input', function() {
      // Xóa timeout cũ nếu có
      if (inputTimeout) {
        clearTimeout(inputTimeout);
      }

      // Tạo timeout mới
      inputTimeout = setTimeout(() => {
        const serialNumber = this.value.trim();
        // Kiểm tra nếu đã nhập đủ IMEI (thường là 10-15 ký tự)
        if (serialNumber && serialNumber.length >= 10) {
          addSerial();
        }
      }, 500); // Đợi 500ms sau khi người dùng ngừng nhập
    });
  }

  if (serialFileInput) {
    serialFileInput.addEventListener('change', handleSerialFileUpload);
  }

  if (downloadTemplateBtn) {
    downloadTemplateBtn.addEventListener('click', downloadSerialTemplate);
  }

  // Load saved data from localStorage
  loadSavedData();
});

// Show import modal based on product tracking type
function showImportModal() {
  const productName = document.querySelector(`tr[data-item-id="${currentItemId}"] td:nth-child(3) .fw-medium`).textContent;
  const row = document.querySelector(`tr[data-item-id="${currentItemId}"]`);
  const isImported = row.dataset.isImported === 'true';

  // Kiểm tra xem sản phẩm đã được nhập hay chưa
  if (isImported) {
    // Nếu đã nhập, hiển thị thông báo
    Swal.fire({
      title: 'Thông báo',
      text: 'Sản phẩm này đã được nhập vào kho. Bạn có muốn nhập lại không?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Nhập lại',
      cancelButtonText: 'Hủy',
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-outline-secondary ms-1'
      },
      buttonsStyling: false
    }).then((result) => {
      if (result.isConfirmed) {
        showImportModalByType(productName);
      }
    });
  } else {
    showImportModalByType(productName);
  }
}

// Show import modal by product tracking type
function showImportModalByType(productName) {
  switch (currentTrackingType) {
    case 'quantity':
      // Hiển thị modal nhập số lượng
      document.getElementById('quantity_item_id').value = currentItemId;
      document.getElementById('quantity_product_id').value = currentProductId;
      document.getElementById('quantity_product_info').querySelector('.fw-medium').textContent = productName;
      document.getElementById('quantity_required').textContent = currentQuantity;
      document.getElementById('quantity_value').value = currentQuantity;

      // Tìm dữ liệu đã lưu trong localStorage
      const storageKey = `good_receipt_${goodReceiptId}_import`;
      const savedData = JSON.parse(localStorage.getItem(storageKey) || '{}');
      const savedQuantityData = savedData[currentItemId];

      // Nếu có dữ liệu đã lưu, hiển thị lên form
      if (savedQuantityData && savedQuantityData.type === 'quantity') {
        document.getElementById('quantity_value').value = savedQuantityData.quantity;
        if (savedQuantityData.warehouseAreaId) {
          document.getElementById('quantity_warehouse_area').value = savedQuantityData.warehouseAreaId;
          // Cập nhật select2 nếu có
          if (typeof $(document.getElementById('quantity_warehouse_area')).select2 === 'function') {
            $(document.getElementById('quantity_warehouse_area')).trigger('change');
          }
        }
      }

      importQuantityModal.show();
      break;
    case 'batch':
      // Hiển thị modal nhập lô
      document.getElementById('batch_item_id').value = currentItemId;
      document.getElementById('batch_product_id').value = currentProductId;
      document.getElementById('batch_product_info').querySelector('.fw-medium').textContent = productName;
      document.getElementById('batch_required').textContent = currentQuantity;
      document.getElementById('batch_quantity').value = currentQuantity;
      document.getElementById('batch_number_input').value = '';

      // Set default expiry date to 1 year from now
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      document.getElementById('batch_expiry_date').value = oneYearFromNow.toISOString().split('T')[0];

      // Khởi tạo danh sách lô nếu chưa có
      if (!scannedBatches[currentItemId]) {
        scannedBatches[currentItemId] = [];
      }

      // Cập nhật số lượng đã nhập
      updateBatchCount();

      // Hiển thị danh sách lô đã nhập
      renderBatchList();

      importBatchModal.show();
      break;
    case 'serial':
      // Hiển thị modal nhập IMEI/Serial
      document.getElementById('serial_item_id').value = currentItemId;
      document.getElementById('serial_product_id').value = currentProductId;
      document.getElementById('serial_product_info').querySelector('.fw-medium').textContent = productName;
      document.getElementById('serial_required').textContent = currentQuantity;

      // Khởi tạo danh sách serial nếu chưa có
      if (!scannedSerials[currentItemId]) {
        scannedSerials[currentItemId] = [];
      }

      // Cập nhật số lượng đã quét
      updateSerialCount();

      // Hiển thị danh sách serial đã quét
      renderSerialList();

      importSerialModal.show();
      break;
    default:
      Swal.fire({
        title: 'Lỗi',
        text: 'Không xác định được loại sản phẩm',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
  }
}

// Save quantity import
function saveQuantityImport() {
  const form = document.getElementById('importQuantityForm');

  // Validate form
  if (!form.checkValidity()) {
    form.classList.add('was-validated');
    return;
  }

  const itemId = document.getElementById('quantity_item_id').value;
  const quantity = parseFloat(document.getElementById('quantity_value').value);
  const warehouseAreaId = document.getElementById('quantity_warehouse_area').value;

  // Validate quantity
  if (quantity <= 0) {
    document.getElementById('quantity_value').classList.add('is-invalid');
    return;
  }

  if (quantity !== currentQuantity) {
    Swal.fire({
      title: 'Cảnh báo',
      text: `Số lượng nhập (${quantity}) khác với số lượng trong phiếu đặt hàng (${currentQuantity}). Bạn có chắc chắn muốn tiếp tục?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Tiếp tục',
      cancelButtonText: 'Hủy',
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-outline-secondary ms-1'
      },
      buttonsStyling: false
    }).then((result) => {
      if (result.isConfirmed) {
        processQuantityImport(itemId, quantity, warehouseAreaId);
      }
    });
  } else {
    processQuantityImport(itemId, quantity, warehouseAreaId);
  }
}

// Process quantity import
function processQuantityImport(itemId, quantity, warehouseAreaId) {
  // Lưu dữ liệu vào localStorage
  const importData = {
    type: 'quantity',
    itemId: itemId,
    quantity: quantity,
    warehouseAreaId: warehouseAreaId
  };

  saveToLocalStorage(itemId, importData);

  // Cập nhật trạng thái dòng sản phẩm
  updateItemStatus(itemId, true);

  // Đóng modal
  importQuantityModal.hide();

  // Hiển thị thông báo thành công
  Swal.fire({
    title: 'Thành công',
    text: 'Đã nhập hàng thành công',
    icon: 'success',
    customClass: {
      confirmButton: 'btn btn-success'
    },
    buttonsStyling: false
  });
}

// Add batch
function addBatch() {
  const batchNumber = document.getElementById('batch_number_input').value.trim();
  const expiryDate = document.getElementById('batch_expiry_date').value;

  if (!batchNumber) {
    return;
  }

  if (!expiryDate) {
    Swal.fire({
      title: 'Lỗi',
      text: 'Vui lòng chọn ngày hết hạn',
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  // Kiểm tra trùng lặp
  const existingBatch = scannedBatches[currentItemId].find(batch => batch.batchNumber === batchNumber);
  if (existingBatch) {
    Swal.fire({
      title: 'Lỗi',
      text: 'Số lô này đã được thêm vào danh sách',
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  // Lấy số lượng cho batch
  const batchQuantity = document.getElementById('batch_item_quantity').value ? parseFloat(document.getElementById('batch_item_quantity').value) : 0;

  // Kiểm tra số lượng batch phải lớn hơn 0
  if (batchQuantity <= 0) {
    Swal.fire({
      title: 'Lỗi',
      text: 'Số lượng cho lô này phải lớn hơn 0',
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  // Kiểm tra tổng số lượng các batch không vượt quá số lượng đặt hàng
  const totalBatchQuantity = calculateTotalBatchQuantity(currentItemId) + batchQuantity;
  if (totalBatchQuantity > currentQuantity) {
    Swal.fire({
      title: 'Lỗi',
      text: `Tổng số lượng các lô (${totalBatchQuantity}) vượt quá số lượng trong phiếu đặt hàng (${currentQuantity})`,
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  // Thêm vào danh sách
  scannedBatches[currentItemId].push({
    batchNumber: batchNumber,
    expiryDate: expiryDate,
    quantity: batchQuantity,
    manufacturingDate: document.getElementById('manufacturing_date').value || null
  });

  // Cập nhật hiển thị
  updateBatchCount();
  renderBatchList();

  // Xóa input và focus lại
  document.getElementById('batch_number_input').value = '';
  setTimeout(() => {
    document.getElementById('batch_number_input').focus();
  }, 100);
}

// Update batch count
function updateBatchCount() {
  batchCount = scannedBatches[currentItemId].length;
  document.getElementById('batchCount').textContent = batchCount;
  document.getElementById('batch_scanned').textContent = batchCount;
}

// Calculate total batch quantity
function calculateTotalBatchQuantity(itemId) {
  if (!scannedBatches[itemId] || scannedBatches[itemId].length === 0) {
    return 0;
  }

  return scannedBatches[itemId].reduce((total, batch) => {
    return total + (parseFloat(batch.quantity) || 0);
  }, 0);
}

// Render batch list
function renderBatchList() {
  const batchTable = document.getElementById('batchTable');
  const tbody = batchTable.querySelector('tbody');
  const noBatchRow = document.getElementById('noBatchRow');

  // Clear existing rows except noBatchRow
  const rows = tbody.querySelectorAll('tr:not(#noBatchRow)');
  rows.forEach(row => row.remove());

  if (scannedBatches[currentItemId].length === 0) {
    noBatchRow.style.display = '';
    return;
  }

  noBatchRow.style.display = 'none';

  // Tính tổng số lượng đã nhập
  const totalBatchQuantity = calculateTotalBatchQuantity(currentItemId);

  // Cập nhật hiển thị tổng số lượng và số lượng còn lại
  const batchTotalQuantityElement = document.getElementById('batch_total_quantity');
  const batchRemainingQuantityElement = document.getElementById('batch_remaining_quantity');

  if (batchTotalQuantityElement) {
    batchTotalQuantityElement.textContent = totalBatchQuantity;
  }

  if (batchRemainingQuantityElement) {
    const remainingQuantity = currentQuantity - totalBatchQuantity;
    batchRemainingQuantityElement.textContent = remainingQuantity;

    // Thêm class để hiển thị màu sắc dựa trên số lượng còn lại
    if (remainingQuantity < 0) {
      batchRemainingQuantityElement.classList.remove('text-success', 'text-warning');
      batchRemainingQuantityElement.classList.add('text-danger');
    } else if (remainingQuantity === 0) {
      batchRemainingQuantityElement.classList.remove('text-danger', 'text-warning');
      batchRemainingQuantityElement.classList.add('text-success');
    } else {
      batchRemainingQuantityElement.classList.remove('text-success', 'text-danger');
      batchRemainingQuantityElement.classList.add('text-warning');
    }
  }

  scannedBatches[currentItemId].forEach((batch, index) => {
    const row = document.createElement('tr');
    const formattedDate = batch.expiryDate ? new Date(batch.expiryDate).toLocaleDateString('vi-VN') : '';
    const formattedMfgDate = batch.manufacturingDate ? new Date(batch.manufacturingDate).toLocaleDateString('vi-VN') : '';
    const quantity = batch.quantity || 0;

    row.innerHTML = `
      <td>${index + 1}</td>
      <td>${batch.batchNumber}</td>
      <td class="text-end">${quantity}</td>
      <td>${formattedMfgDate}</td>
      <td>${formattedDate}</td>
      <td>
        <button type="button" class="btn btn-sm btn-danger remove-batch-btn" data-index="${index}">
          <i class="ri-delete-bin-line"></i>
        </button>
      </td>
    `;

    tbody.appendChild(row);
  });

  // Add event listeners to remove buttons
  document.querySelectorAll('.remove-batch-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      const index = parseInt(this.dataset.index);
      removeBatch(index);
    });
  });
}

// Remove batch
function removeBatch(index) {
  scannedBatches[currentItemId].splice(index, 1);
  updateBatchCount();
  renderBatchList();
}

// Save batch import
function saveBatchImport() {
  const form = document.getElementById('importBatchForm');

  // Validate form
  if (!form.checkValidity()) {
    form.classList.add('was-validated');
    return;
  }

  const itemId = document.getElementById('batch_item_id').value;
  const quantity = parseFloat(document.getElementById('batch_quantity').value);
  const warehouseAreaId = document.getElementById('batch_warehouse_area').value;

  // Validate quantity
  if (quantity <= 0) {
    document.getElementById('batch_quantity').classList.add('is-invalid');
    return;
  }

  // Validate batch list
  if (scannedBatches[currentItemId].length === 0) {
    Swal.fire({
      title: 'Lỗi',
      text: 'Vui lòng nhập ít nhất một số lô',
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  // Kiểm tra tổng số lượng các batch
  const totalBatchQuantity = calculateTotalBatchQuantity(currentItemId);

  // Kiểm tra xem có batch nào có số lượng <= 0 không
  const invalidBatches = scannedBatches[currentItemId].filter(batch => parseFloat(batch.quantity) <= 0);
  if (invalidBatches.length > 0) {
    Swal.fire({
      title: 'Lỗi',
      text: 'Tất cả các lô phải có số lượng lớn hơn 0',
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  // Kiểm tra tổng số lượng batch có khớp với số lượng nhập không
  if (totalBatchQuantity !== quantity) {
    Swal.fire({
      title: 'Lỗi',
      text: `Tổng số lượng các lô (${totalBatchQuantity}) không khớp với số lượng nhập (${quantity})`,
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  if (quantity !== currentQuantity) {
    Swal.fire({
      title: 'Cảnh báo',
      text: `Số lượng nhập (${quantity}) khác với số lượng trong phiếu đặt hàng (${currentQuantity}). Bạn có chắc chắn muốn tiếp tục?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Tiếp tục',
      cancelButtonText: 'Hủy',
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-outline-secondary ms-1'
      },
      buttonsStyling: false
    }).then((result) => {
      if (result.isConfirmed) {
        processBatchImport(itemId, quantity, warehouseAreaId);
      }
    });
  } else {
    processBatchImport(itemId, quantity, warehouseAreaId);
  }
}

// Process batch import
function processBatchImport(itemId, quantity, warehouseAreaId) {
  // Lưu dữ liệu vào localStorage
  const importData = {
    type: 'batch',
    itemId: itemId,
    batches: scannedBatches[itemId],
    quantity: quantity,
    warehouseAreaId: warehouseAreaId
  };

  saveToLocalStorage(itemId, importData);

  // Cập nhật trạng thái dòng sản phẩm
  updateItemStatus(itemId, true);

  // Đóng modal
  importBatchModal.hide();

  // Hiển thị thông báo thành công
  Swal.fire({
    title: 'Thành công',
    text: 'Đã nhập hàng thành công',
    icon: 'success',
    customClass: {
      confirmButton: 'btn btn-success'
    },
    buttonsStyling: false
  });
}

// Add serial
function addSerial() {
  const serialNumber = document.getElementById('serial_number').value.trim();

  if (!serialNumber) {
    return;
  }

  // Kiểm tra trùng lặp
  if (scannedSerials[currentItemId].includes(serialNumber)) {
    Swal.fire({
      title: 'Lỗi',
      text: 'IMEI/Serial này đã được quét',
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  // Kiểm tra số lượng IMEI đã quét có vượt quá số lượng trong phiếu đặt hàng không
  if (scannedSerials[currentItemId].length >= currentQuantity) {
    Swal.fire({
      title: 'Lỗi',
      text: `Số lượng IMEI đã đạt giới hạn (${currentQuantity}). Không thể thêm IMEI mới.`,
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  // Kiểm tra IMEI đã tồn tại trong hệ thống
  checkSerialExists(serialNumber).then(exists => {
    if (exists) {
      Swal.fire({
        title: 'Lỗi',
        text: 'IMEI/Serial này đã tồn tại trong hệ thống',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
    } else {
      // Thêm vào danh sách
      scannedSerials[currentItemId].push(serialNumber);

      // Cập nhật hiển thị
      updateSerialCount();
      renderSerialList();

      // Xóa input và focus lại
      document.getElementById('serial_number').value = '';
      setTimeout(() => {
        document.getElementById('serial_number').focus();
      }, 100);
    }
  });
}

// Check if serial exists
function checkSerialExists(serialNumber) {
  return new Promise((resolve, reject) => {
    showLoading();

    fetch(`${baseUrl}/${currentLocale}/admin/warehouses/check-serial-exists`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        'Accept': 'application/json'
      },
      body: JSON.stringify({ serial_number: serialNumber })
    })
      .then(response => response.json())
      .then(data => {
        hideLoading();
        resolve(data.exists);
      })
      .catch(error => {
        console.error('Error:', error);
        hideLoading();
        resolve(false); // Assume it doesn't exist in case of error
      });
  });
}

// Update serial count
function updateSerialCount() {
  serialCount = scannedSerials[currentItemId].length;
  document.getElementById('serialCount').textContent = serialCount;
  document.getElementById('serial_scanned').textContent = serialCount;
}

// Render serial list
function renderSerialList() {
  const serialTable = document.getElementById('serialTable');
  const tbody = serialTable.querySelector('tbody');
  const noSerialRow = document.getElementById('noSerialRow');

  // Clear existing rows except noSerialRow
  const rows = tbody.querySelectorAll('tr:not(#noSerialRow)');
  rows.forEach(row => row.remove());

  if (scannedSerials[currentItemId].length === 0) {
    noSerialRow.style.display = '';
    return;
  }

  noSerialRow.style.display = 'none';

  scannedSerials[currentItemId].forEach((serial, index) => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${index + 1}</td>
      <td>${serial}</td>
      <td>
        <button type="button" class="btn btn-sm btn-danger remove-serial-btn" data-index="${index}">
          <i class="ri-delete-bin-line"></i>
        </button>
      </td>
    `;

    tbody.appendChild(row);
  });

  // Add event listeners to remove buttons
  document.querySelectorAll('.remove-serial-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      const index = parseInt(this.dataset.index);
      removeSerial(index);
    });
  });
}

// Remove serial
function removeSerial(index) {
  scannedSerials[currentItemId].splice(index, 1);
  updateSerialCount();
  renderSerialList();
}

// Handle serial file upload
function handleSerialFileUpload(e) {
  const file = e.target.files[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = function(e) {
    try {
      showLoading();

      // Kiểm tra xem XLSX đã được tải chưa
      if (typeof XLSX === 'undefined') {
        hideLoading();
        Swal.fire({
          title: 'Lỗi',
          text: 'Thư viện XLSX chưa được tải. Vui lòng làm mới trang và thử lại.',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
        return;
      }

      // Parse Excel file
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });

      // Get first sheet
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];

      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      // Skip header row and process data
      const serials = [];
      const remainingSlots = currentQuantity - scannedSerials[currentItemId].length;

      // Kiểm tra xem còn bao nhiêu slot trống cho IMEI
      if (remainingSlots <= 0) {
        hideLoading();
        Swal.fire({
          title: 'Lỗi',
          text: `Số lượng IMEI đã đạt giới hạn (${currentQuantity}). Không thể thêm IMEI mới.`,
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
        return;
      }

      // Chỉ lấy số lượng IMEI còn thiếu
      let count = 0;
      for (let i = 1; i < jsonData.length && count < remainingSlots; i++) {
        const row = jsonData[i];
        if (row.length > 0 && row[0]) {
          const serial = row[0].toString().trim();
          if (serial && !scannedSerials[currentItemId].includes(serial)) {
            serials.push(serial);
            count++;
          }
        }
      }

      // Check if serials exist in system
      if (serials.length > 0) {
        checkSerialsExist(serials).then(existingSerials => {
          if (existingSerials.length > 0) {
            Swal.fire({
              title: 'Lỗi',
              html: `Các IMEI/Serial sau đã tồn tại trong hệ thống:<br><br>${existingSerials.join('<br>')}`,
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-primary'
              },
              buttonsStyling: false
            });
          }

          // Add valid serials
          const validSerials = serials.filter(serial => !existingSerials.includes(serial));
          if (validSerials.length > 0) {
            scannedSerials[currentItemId] = [...scannedSerials[currentItemId], ...validSerials];
            updateSerialCount();
            renderSerialList();

            Swal.fire({
              title: 'Thành công',
              text: `Đã nhập ${validSerials.length} IMEI/Serial từ file`,
              icon: 'success',
              customClass: {
                confirmButton: 'btn btn-success'
              },
              buttonsStyling: false
            });
          }

          hideLoading();
        });
      } else {
        hideLoading();
        Swal.fire({
          title: 'Thông báo',
          text: 'Không có IMEI/Serial mới để nhập',
          icon: 'info',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
      }
    } catch (error) {
      hideLoading();
      console.error('Error:', error);
      Swal.fire({
        title: 'Lỗi',
        text: 'Không thể đọc file Excel. Vui lòng kiểm tra định dạng file.',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
    }
  };

  reader.readAsArrayBuffer(file);
}

// Check if multiple serials exist
function checkSerialsExist(serials) {
  return new Promise((resolve, reject) => {
    fetch(`${baseUrl}/${currentLocale}/admin/warehouses/check-serials-exist`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        'Accept': 'application/json'
      },
      body: JSON.stringify({ serials: serials })
    })
      .then(response => response.json())
      .then(data => {
        resolve(data.existing_serials || []);
      })
      .catch(error => {
        console.error('Error:', error);
        resolve([]); // Assume none exist in case of error
      });
  });
}

// Download serial template
function downloadSerialTemplate(e) {
  e.preventDefault();

  // Kiểm tra xem XLSX đã được tải chưa
  if (typeof XLSX === 'undefined') {
    Swal.fire({
      title: 'Lỗi',
      text: 'Thư viện XLSX chưa được tải. Vui lòng làm mới trang và thử lại.',
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  // Create workbook
  const wb = XLSX.utils.book_new();

  // Create worksheet with header
  const ws = XLSX.utils.aoa_to_sheet([['IMEI/Serial']]);

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(wb, ws, 'IMEI Template');

  // Generate Excel file
  XLSX.writeFile(wb, 'imei_template.xlsx');
}

// Save serial import
function saveSerialImport() {
  const warehouseAreaId = document.getElementById('serial_warehouse_area').value;

  // Validate warehouse area
  if (!warehouseAreaId) {
    document.getElementById('serial_warehouse_area').classList.add('is-invalid');
    return;
  }

  // Validate serial count
  if (scannedSerials[currentItemId].length === 0) {
    Swal.fire({
      title: 'Lỗi',
      text: 'Vui lòng nhập ít nhất một IMEI/Serial',
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  if (scannedSerials[currentItemId].length > currentQuantity) {
    Swal.fire({
      title: 'Lỗi',
      text: `Số lượng IMEI/Serial (${scannedSerials[currentItemId].length}) vượt quá số lượng trong phiếu đặt hàng (${currentQuantity}). Vui lòng xóa bớt IMEI.`,
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  } else if (scannedSerials[currentItemId].length < currentQuantity) {
    Swal.fire({
      title: 'Cảnh báo',
      text: `Số lượng IMEI/Serial (${scannedSerials[currentItemId].length}) ít hơn số lượng trong phiếu đặt hàng (${currentQuantity}). Bạn có chắc chắn muốn tiếp tục?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Tiếp tục',
      cancelButtonText: 'Hủy',
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-outline-secondary ms-1'
      },
      buttonsStyling: false
    }).then((result) => {
      if (result.isConfirmed) {
        processSerialImport(currentItemId, warehouseAreaId);
      }
    });
  } else {
    processSerialImport(currentItemId, warehouseAreaId);
  }
}

// Process serial import
function processSerialImport(itemId, warehouseAreaId) {
  // Lưu dữ liệu vào localStorage
  const importData = {
    type: 'serial',
    itemId: itemId,
    serials: scannedSerials[itemId],
    warehouseAreaId: warehouseAreaId
  };

  saveToLocalStorage(itemId, importData);

  // Cập nhật trạng thái dòng sản phẩm
  updateItemStatus(itemId, true);

  // Đóng modal
  importSerialModal.hide();

  // Hiển thị thông báo thành công
  Swal.fire({
    title: 'Thành công',
    text: 'Đã nhập hàng thành công',
    icon: 'success',
    customClass: {
      confirmButton: 'btn btn-success'
    },
    buttonsStyling: false
  });
}

// Save to localStorage
function saveToLocalStorage(itemId, data) {
  const storageKey = `good_receipt_${goodReceiptId}_import`;
  let savedData = JSON.parse(localStorage.getItem(storageKey) || '{}');

  savedData[itemId] = data;

  localStorage.setItem(storageKey, JSON.stringify(savedData));
}

// Load saved data from localStorage and server
function loadSavedData() {
  const storageKey = `good_receipt_${goodReceiptId}_import`;
  const savedData = JSON.parse(localStorage.getItem(storageKey) || '{}');

  // Khởi tạo scannedSerials và scannedBatches
  scannedSerials = {};
  scannedBatches = {};

  // Cập nhật trạng thái các dòng sản phẩm từ localStorage
  for (const itemId in savedData) {
    const data = savedData[itemId];

    // Khởi tạo danh sách serial nếu cần
    if (data.type === 'serial') {
      scannedSerials[itemId] = data.serials || [];
    }

    // Khởi tạo danh sách batch nếu cần
    if (data.type === 'batch') {
      scannedBatches[itemId] = data.batches || [];
    }

    // Cập nhật trạng thái dòng
    updateItemStatus(itemId, true);
  }

  // Tải dữ liệu đã lưu từ server
  loadSavedImportData();
}

// Load saved import data from server
function loadSavedImportData() {
  if (!goodReceiptId) return;

  fetch(`${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/${goodReceiptId}/get-import-data`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const importData = data.data || {};

        // Xử lý dữ liệu IMEI/Serial
        if (importData.serials && importData.serials.length > 0) {
          importData.serials.forEach(item => {
            if (item.item_id && item.serials && item.serials.length > 0) {
              // Khởi tạo mảng nếu chưa có
              if (!scannedSerials[item.item_id]) {
                scannedSerials[item.item_id] = [];
              }

              // Thêm các serial chưa có vào mảng
              item.serials.forEach(serial => {
                if (!scannedSerials[item.item_id].includes(serial)) {
                  scannedSerials[item.item_id].push(serial);
                }
              });

              // Cập nhật trạng thái dòng
              updateItemStatus(item.item_id, true);
            }
          });
        }

        // Xử lý dữ liệu Batch
        if (importData.batches && importData.batches.length > 0) {
          importData.batches.forEach(item => {
            if (item.item_id && item.batches && item.batches.length > 0) {
              // Khởi tạo mảng nếu chưa có
              if (!scannedBatches[item.item_id]) {
                scannedBatches[item.item_id] = [];
              }

              // Thêm các batch chưa có vào mảng
              item.batches.forEach(batch => {
                // Kiểm tra xem batch đã tồn tại chưa
                const existingBatch = scannedBatches[item.item_id].find(b => b.batchNumber === batch.batch_number);
                if (!existingBatch) {
                  scannedBatches[item.item_id].push({
                    batchNumber: batch.batch_number,
                    expiryDate: batch.expiry_date,
                    quantity: batch.quantity,
                    manufacturingDate: batch.manufacturing_date || null
                  });
                }
              });

              // Cập nhật trạng thái dòng
              updateItemStatus(item.item_id, true);
            }
          });
        }
      }
    })
    .catch(error => {
      console.error('Error loading saved import data:', error);
    });
}

// Update item status and warehouse area
function updateItemStatus(itemId, imported) {
  const row = document.querySelector(`tr[data-item-id="${itemId}"]`);
  if (!row) return;

  const statusBadge = row.querySelector('.item-status');
  if (!statusBadge) return;

  // Lấy loại sản phẩm từ data attribute
  const trackingType = row.dataset.trackingType;

  if (imported) {
    // Cập nhật trạng thái
    statusBadge.textContent = 'Đã nhập';
    statusBadge.classList.remove('bg-label-secondary');
    statusBadge.classList.add('bg-label-success');
    statusBadge.title = 'Sản phẩm đã được nhập vào kho';

    // Ẩn nút nhập hàng nếu có
    const importButton = row.querySelector('.import-item-btn');
    if (importButton) {
      importButton.style.display = 'none';
    }

    // Cập nhật thông tin khu vực kho
    const storageKey = `good_receipt_${goodReceiptId}_import`;
    const savedData = JSON.parse(localStorage.getItem(storageKey) || '{}');
    const itemData = savedData[itemId];

    if (itemData && itemData.warehouseAreaId) {
      // Lấy tên khu vực kho từ select box
      let warehouseAreaName = '';
      let selectElement;

      switch (itemData.type) {
        case 'quantity':
          selectElement = document.getElementById('quantity_warehouse_area');
          break;
        case 'batch':
          selectElement = document.getElementById('batch_warehouse_area');
          break;
        case 'serial':
          selectElement = document.getElementById('serial_warehouse_area');
          break;
      }

      if (selectElement) {
        const selectedOption = selectElement.options[selectElement.selectedIndex];
        if (selectedOption) {
          warehouseAreaName = selectedOption.text;
        }
      }

      // Nếu không lấy được từ select box, thử lấy từ tất cả các select box
      if (!warehouseAreaName) {
        const allSelects = [
          document.getElementById('quantity_warehouse_area'),
          document.getElementById('batch_warehouse_area'),
          document.getElementById('serial_warehouse_area')
        ];

        for (const select of allSelects) {
          if (!select) continue;

          for (const option of select.options) {
            if (option.value == itemData.warehouseAreaId) {
              warehouseAreaName = option.text;
              break;
            }
          }

          if (warehouseAreaName) break;
        }
      }

      // Cập nhật hiển thị khu vực kho trong bảng
      const areaCell = row.querySelector('td:nth-child(10)');
      if (areaCell) {
        let html = '';

        // Chỉ hiển thị thông tin khu vực kho
        if (warehouseAreaName) {
          html += `<span title="${warehouseAreaName}">${warehouseAreaName}</span>`;
        } else {
          html += `<span>N/A</span>`;
        }

        // Log thông tin để debug
        console.log('Cập nhật thông tin khu vực kho:', {
          itemId,
          warehouseAreaName,
          html
        });

        areaCell.innerHTML = html;
      }

      // Ẩn nút nhập hàng nếu đã nhập
      const importBtn = row.querySelector('.import-item-btn');
      if (importBtn) {
        // Ẩn nút nhập hàng cho tất cả các loại sản phẩm đã nhập
        importBtn.style.display = 'none';
      }
    }
  } else {
    statusBadge.textContent = 'Chưa nhập';
    statusBadge.classList.remove('bg-label-success');
    statusBadge.classList.add('bg-label-secondary');

    // Hiện lại nút nhập hàng
    const importBtn = row.querySelector('.import-item-btn');
    if (importBtn) {
      importBtn.style.display = '';
    }
  }
}

// Show loading
function showLoading() {
  if (typeof window.showLoading === 'function') {
    window.showLoading();
  } else {
    // Fallback if global showLoading is not available
    Swal.fire({
      title: 'Đang xử lý...',
      html: 'Vui lòng chờ trong giây lát',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });
  }
}

// Hide loading
function hideLoading() {
  if (typeof window.hideLoading === 'function') {
    window.hideLoading();
  } else {
    // Fallback if global hideLoading is not available
    Swal.close();
  }
}

// Show batches modal
function showBatchesModal(itemId) {
  const storageKey = `good_receipt_${goodReceiptId}_import`;
  const savedData = JSON.parse(localStorage.getItem(storageKey) || '{}');
  const itemData = savedData[itemId];

  if (!itemData || !itemData.batches || itemData.batches.length === 0) {
    Swal.fire({
      title: 'Thông báo',
      text: 'Không có thông tin lô nào được lưu trữ',
      icon: 'info',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  // Tạo nội dung HTML cho modal
  let html = `
    <div class="table-responsive">
      <table class="table table-bordered">
        <thead>
          <tr>
            <th style="width: 5%">#</th>
            <th style="width: 55%">Số lô</th>
            <th style="width: 40%">Ngày hết hạn</th>
          </tr>
        </thead>
        <tbody>
  `;

  itemData.batches.forEach((batch, index) => {
    const formattedDate = formatDate(batch.expiryDate);
    html += `
      <tr>
        <td>${index + 1}</td>
        <td>${batch.batchNumber}</td>
        <td>${formattedDate}</td>
      </tr>
    `;
  });

  html += `
        </tbody>
      </table>
    </div>
  `;

  // Hiển thị modal
  Swal.fire({
    title: 'Danh sách số lô',
    html: html,
    width: '600px',
    customClass: {
      confirmButton: 'btn btn-primary'
    },
    buttonsStyling: false
  });
}

// Format date for display
function formatDate(dateString) {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString; // Return original if invalid

    // Format as dd/mm/yyyy
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (e) {
    console.error('Error formatting date:', e);
    return dateString; // Return original on error
  }
}
