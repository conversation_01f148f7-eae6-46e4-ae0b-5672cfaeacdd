/**
 * Warehouse Inventory - Add
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
    (function () {
        const addInventoryForm = document.getElementById('addInventoryForm');
        const warehouseSelect = document.getElementById('warehouse_id');
        const productSelect = document.getElementById('product_id');
        const quantityInput = document.getElementById('quantity');
        const batchNumberInput = document.getElementById('batch_number');
        const expiryDateInput = document.getElementById('expiry_date');
        const notesInput = document.getElementById('notes');

        // Initialize Select2
        if (productSelect) {
            const $productSelect = $(productSelect);
            select2Focus($productSelect);
            $productSelect.wrap('<div class="position-relative"></div>').select2({
                placeholder: 'Chọn sản phẩm',
                dropdownParent: $productSelect.parent()
            });
        }

        // Initialize Flatpickr
        if (expiryDateInput) {
            flatpickr(expiryDateInput, {
                monthSelectorType: 'static',
                dateFormat: 'Y-m-d'
            });
        }

        // Form validation
        if (addInventoryForm) {
            const fv = FormValidation.formValidation(addInventoryForm, {
                fields: {
                    warehouse_id: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng chọn kho hàng'
                            }
                        }
                    },
                    product_id: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng chọn sản phẩm'
                            }
                        }
                    },
                    quantity: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập số lượng'
                            },
                            numeric: {
                                message: 'Số lượng phải là số'
                            },
                            greaterThan: {
                                message: 'Số lượng phải lớn hơn 0',
                                min: 0.01
                            }
                        }
                    },
                    batch_number: {
                        validators: {
                            stringLength: {
                                max: 50,
                                message: 'Số lô không được vượt quá 50 ký tự'
                            }
                        }
                    },
                    notes: {
                        validators: {
                            stringLength: {
                                max: 500,
                                message: 'Ghi chú không được vượt quá 500 ký tự'
                            }
                        }
                    }
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap5: new FormValidation.plugins.Bootstrap5({
                        eleValidClass: '',
                        rowSelector: '.col-md-6, .col-md-12'
                    }),
                    submitButton: new FormValidation.plugins.SubmitButton(),
                    autoFocus: new FormValidation.plugins.AutoFocus()
                }
            }).on('core.form.valid', function () {
                // Get form data
                const formData = {
                    warehouse_id: warehouseSelect.value,
                    product_id: productSelect.value,
                    quantity: quantityInput.value,
                    batch_number: batchNumberInput.value,
                    expiry_date: expiryDateInput.value,
                    notes: notesInput.value,
                    _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                };

                // Submit form data via AJAX
                $.ajax({
                    type: 'POST',
                    url: '/admin/warehouses/inventory/add',
                    data: formData,
                    success: function (response) {
                        if (response.code === 200) {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.text,
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function () {
                                window.location.href = '/admin/warehouses/inventory';
                            });
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.text,
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        }
                    },
                    error: function (error) {
                        let errorMessage = 'Có lỗi xảy ra khi nhập kho. Vui lòng thử lại sau.';

                        if (error.responseJSON && error.responseJSON.errors) {
                            errorMessage = Object.values(error.responseJSON.errors).flat().join('<br>');
                        }

                        Swal.fire({
                            title: 'Lỗi!',
                            html: errorMessage,
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            });
        }
    })();
});
