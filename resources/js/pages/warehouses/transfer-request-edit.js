/**
 * Chỉnh sửa yêu cầu chuyển hàng
 */

'use strict';

// DOM Elements
const transferRequestForm = document.getElementById('transferRequestForm');
const sourceWarehouseSelect = document.getElementById('source_warehouse_id');
const destinationWarehouseSelect = document.getElementById('destination_warehouse_id');
const prioritySelect = document.getElementById('priority');
const requestedDateInput = document.getElementById('requested_date');
const expectedDateInput = document.getElementById('expected_date');
const reasonTextarea = document.getElementById('reason');
const notesTextarea = document.getElementById('notes');
const productsTable = document.getElementById('productsTable');
const noProductsRow = document.getElementById('noProductsRow');
const addProductBtn = document.getElementById('addProductBtn');
const addProductModal = document.getElementById('addProductModal');
const addProductForm = document.getElementById('addProductForm');
const productSelect = document.getElementById('product_id');
const quantityInput = document.getElementById('quantity');
const productNotesInput = document.getElementById('product_notes');
const saveProductBtn = document.getElementById('saveProductBtn');
const submitBtn = document.getElementById('submitBtn');
const editIndexInput = document.getElementById('edit_index');

// Variables
let products = [];
let flatpickrRequestedDate;
let flatpickrExpectedDate;
let productSelectInstance;
let availableQuantity = 0;

// DOM ready function
document.addEventListener('DOMContentLoaded', function () {
  // Initialize Select2
  initSelect2();

  // Initialize Flatpickr
  initFlatpickr();

  // Initialize products from server data
  if (window.initialProducts && Array.isArray(window.initialProducts)) {
    products = window.initialProducts;
    renderProductsTable();
  }

  // Event listeners
  addProductBtn.addEventListener('click', function () {
    openAddProductModal();
  });

  saveProductBtn.addEventListener('click', function () {
    addProductToTable();
  });

  submitBtn.addEventListener('click', function () {
    submitForm();
  });

  // Source warehouse change event
  sourceWarehouseSelect.addEventListener('change', function () {
    // Clear product select when source warehouse changes
    if (productSelectInstance) {
      productSelectInstance.val(null).trigger('change');
    }
  });

  /**
   * Initialize Select2
   */
  function initSelect2() {
    // Basic Select2
    $('.select2').each(function () {
      const $this = $(this);
      $this.wrap('<div class="position-relative"></div>');
      $this.select2({
        placeholder: $this.data('placeholder'),
        dropdownParent: $this.parent(),
        width: '100%'
      });
      // Thêm class form-floating-select2 cho form-floating chứa select2
      if ($this.closest('.form-floating').length) {
        select2Focus($this);
      }
    });

    // Ajax Select2 for products
    $('.select2-ajax').each(function () {
      const $this = $(this);
      $this.wrap('<div class="position-relative"></div>');
      productSelectInstance = $this.select2({
        placeholder: $this.data('placeholder'),
        dropdownParent: $this.parent(),
        ajax: {
          url: function() {
            // Lấy giá trị hiện tại của kho nguồn
            const currentWarehouseId = sourceWarehouseSelect.value || 0;

            // Lấy locale từ URL hiện tại
            const locale = window.location.pathname.split('/')[1];

            // Trả về URL đầy đủ với locale
            return '/' + locale + '/admin/warehouses/transfer-requests/warehouse/' + currentWarehouseId + '/products';
          },
          dataType: 'json',
          delay: 250,
          data: function (params) {
            return {
              q: params.term,
              page: params.page
            };
          },
          transport: function(params, success, failure) {
            // Kiểm tra xem đã chọn kho nguồn chưa
            if (!sourceWarehouseSelect.value) {
              // Hiển thị thông báo lỗi
              const data = {
                results: [{
                  id: 'error',
                  text: 'Vui lòng chọn kho nguồn trước',
                  disabled: true
                }]
              };
              success(data);
              return;
            }
            // Nếu đã chọn kho nguồn, tiếp tục gọi API
            $.ajax(params).then(success).fail(failure);
          },
          processResults: function (data, params) {
            params.page = params.page || 1;

            return {
              results: data.results || data.data,
              pagination: {
                more: data.pagination ? data.pagination.more : (params.page * 10) < data.total
              }
            };
          },
          error: function(xhr, status, error) {
            console.error('Select2 AJAX error:', status, error);
            if (xhr.status === 401) {
              console.error('Lỗi xác thực (401 Unauthorized). Vui lòng đăng nhập lại.');
            }
            return { results: [] };
          },
          cache: true
        },
        minimumInputLength: 2,
        templateResult: formatProduct,
        templateSelection: formatProductSelection
      });
      // Thêm class form-floating-select2 cho form-floating chứa select2
      if ($this.closest('.form-floating').length) {
        select2Focus($this);
      }
    });

    // Format product in dropdown
    function formatProduct(product) {
      if (product.loading) return product.text;

      const markup = `
        <div class="d-flex align-items-center">
          <div class="ms-2">
            <div>${product.name}</div>
            <div class="small text-muted">Mã: ${product.code} | Tồn kho: ${product.available_quantity}</div>
          </div>
        </div>
      `;

      return $(markup);
    }

    // Format selected product
    function formatProductSelection(product) {
      // Store available quantity for validation
      if (product.available_quantity !== undefined) {
        availableQuantity = product.available_quantity;
      }

      return product.name || product.text;
    }
  }

  /**
   * Initialize Flatpickr
   */
  function initFlatpickr() {
    // Requested date
    if (requestedDateInput) {
      flatpickrRequestedDate = flatpickr(requestedDateInput, {
        dateFormat: 'Y-m-d', // Định dạng năm-tháng-ngày cho server
        altInput: true, // Sử dụng input phụ để hiển thị định dạng khác
        altFormat: 'd/m/Y', // Định dạng hiển thị cho người dùng
        allowInput: true
      });
    }

    // Expected date
    if (expectedDateInput) {
      flatpickrExpectedDate = flatpickr(expectedDateInput, {
        dateFormat: 'Y-m-d', // Định dạng năm-tháng-ngày cho server
        altInput: true, // Sử dụng input phụ để hiển thị định dạng khác
        altFormat: 'd/m/Y', // Định dạng hiển thị cho người dùng
        allowInput: true
      });
    }
  }

  /**
   * Open add product modal
   */
  function openAddProductModal() {
    // Reset form
    addProductForm.reset();
    $(productSelect).val('').trigger('change');
    editIndexInput.value = '';

    // Show modal
    const modal = new bootstrap.Modal(addProductModal);
    modal.show();
  }

  /**
   * Add product to table
   */
  function addProductToTable() {
    // Get values from form
    const productId = productSelect.value;
    const productText = $(productSelect).find('option:selected').text();
    const quantity = quantityInput.value;
    const notes = productNotesInput.value;
    const editIndex = editIndexInput.value;

    // Validate
    if (!productId) {
      Swal.fire({
        title: 'Lỗi',
        text: 'Vui lòng chọn sản phẩm',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
      return;
    }

    if (!quantity || parseFloat(quantity) <= 0) {
      Swal.fire({
        title: 'Lỗi',
        text: 'Vui lòng nhập số lượng hợp lệ',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
      return;
    }

    // Check if quantity exceeds available quantity
    if (parseFloat(quantity) > availableQuantity) {
      Swal.fire({
        title: 'Cảnh báo',
        text: `Số lượng yêu cầu (${quantity}) vượt quá số lượng tồn kho (${availableQuantity})`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Tiếp tục',
        cancelButtonText: 'Hủy',
        customClass: {
          confirmButton: 'btn btn-primary',
          cancelButton: 'btn btn-label-secondary'
        },
        buttonsStyling: false
      }).then((result) => {
        if (result.isConfirmed) {
          saveProduct(productId, productText, quantity, notes, editIndex);
        }
      });
    } else {
      saveProduct(productId, productText, quantity, notes, editIndex);
    }
  }

  /**
   * Save product to array and update table
   */
  function saveProduct(productId, productText, quantity, notes, editIndex) {
    // Create product object
    const product = {
        product_id: productId,
        product_name: productText,
        quantity: quantity,
        notes: notes,
        available_quantity: availableQuantity // Lưu thông tin số lượng tồn kho
    };

    // Add or update product
    if (editIndex !== '') {
      products[parseInt(editIndex)] = product;
    } else {
      // Check if product already exists
      // Chỉ kiểm tra sản phẩm đã tồn tại khi danh sách sản phẩm không rỗng
      if (products.length > 0) {
        const existingIndex = products.findIndex(p => p.product_id === productId);
        if (existingIndex !== -1) {
          Swal.fire({
            title: 'Cảnh báo',
            text: 'Sản phẩm này đã tồn tại trong danh sách. Bạn có muốn cập nhật số lượng?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Cập nhật',
            cancelButtonText: 'Hủy',
            customClass: {
              confirmButton: 'btn btn-primary',
              cancelButton: 'btn btn-label-secondary'
            },
            buttonsStyling: false
          }).then((result) => {
            if (result.isConfirmed) {
              products[existingIndex] = product;
              renderProductsTable();
              bootstrap.Modal.getInstance(addProductModal).hide();
            } else {
              // Đóng modal khi người dùng nhấn nút Hủy
              bootstrap.Modal.getInstance(addProductModal).hide();
            }
          });
          return;
        }
      }

      products.push(product);
    }

    // Update table
    renderProductsTable();

    // Hide modal
    bootstrap.Modal.getInstance(addProductModal).hide();
  }

  /**
   * Render products table
   */
  function renderProductsTable() {
    // Clear table
    const tbody = productsTable.querySelector('tbody');
    tbody.innerHTML = '';

    // Show no products row if empty
    if (products.length === 0) {
      tbody.appendChild(noProductsRow);
      return;
    }

    // Add products to table
    products.forEach((product, index) => {
        const row = document.createElement('tr');

        row.innerHTML = `
        <td>${product.product_name}</td>
        <td>${product.quantity} / ${product.available_quantity}</td>
        <td>${product.notes || '-'}</td>
        <td>
          <div class="d-flex gap-1">
            <button type="button" class="btn btn-sm btn-icon btn-label-primary edit-product" data-index="${index}">
              <i class="ri-pencil-line"></i>
            </button>
            <button type="button" class="btn btn-sm btn-icon btn-label-danger delete-product" data-index="${index}">
              <i class="ri-delete-bin-line"></i>
            </button>
          </div>
        </td>`;

        tbody.appendChild(row);
    });

    // Add event listeners to edit and delete buttons
    document.querySelectorAll('.edit-product').forEach(button => {
        button.addEventListener('click', function() {
            const index = this.getAttribute('data-index');
            editProduct(index);
        });
    });

    document.querySelectorAll('.delete-product').forEach(button => {
        button.addEventListener('click', function() {
            const index = this.getAttribute('data-index');
            deleteProduct(index);
        });
    });
  }

  /**
   * Edit product
   */
  function editProduct(index) {
    const product = products[index];

    // Reset form
    addProductForm.reset();
    $(productSelect).val('').trigger('change');
    editIndexInput.value = '';

    // Set values if editing
    if (product) {
        $(productSelect).append(new Option(product.product_name, product.product_id, true, true)).trigger('change');
        quantityInput.value = product.quantity;
        productNotesInput.value = product.notes || '';
        editIndexInput.value = index;
    }

    // Update available quantity
    availableQuantity = product.available_quantity;

    // Show modal
    const modal = new bootstrap.Modal(addProductModal);
    modal.show();
  }

  /**
   * Delete product
   */
  function deleteProduct(index) {
    Swal.fire({
      title: 'Xác nhận xóa',
      text: 'Bạn có chắc chắn muốn xóa sản phẩm này?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then((result) => {
      if (result.isConfirmed) {
        products.splice(index, 1);
        renderProductsTable();
      }
    });
  }

  /**
   * Submit form
   */
  function submitForm() {
    // Validate form data
    const formData = new FormData(transferRequestForm);
    const data = {
        source_warehouse_id: formData.get('source_warehouse_id'),
        destination_warehouse_id: formData.get('destination_warehouse_id'),
        priority: formData.get('priority'),
        requested_date: formData.get('requested_date'),
        expected_date: formData.get('expected_date'),
        reason: formData.get('reason'),
        notes: formData.get('notes'),
        items: products.map(product => ({
            product_id: product.product_id,
            quantity: product.quantity,
            notes: product.notes
        }))
    };

    // Validate required fields
    if (!data.source_warehouse_id) {
      Swal.fire({
        title: 'Lỗi',
        text: 'Vui lòng chọn kho nguồn',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
      return;
    }

    if (!data.destination_warehouse_id) {
      Swal.fire({
        title: 'Lỗi',
        text: 'Vui lòng chọn kho đích',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
      return;
    }

    if (data.source_warehouse_id === data.destination_warehouse_id) {
      Swal.fire({
        title: 'Lỗi',
        text: 'Kho nguồn và kho đích không được trùng nhau',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
      return;
    }

    if (!data.priority) {
      Swal.fire({
        title: 'Lỗi',
        text: 'Vui lòng chọn mức độ ưu tiên',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
      return;
    }

    if (!data.requested_date) {
      Swal.fire({
        title: 'Lỗi',
        text: 'Vui lòng chọn ngày yêu cầu',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
      return;
    }

    if (data.items.length === 0) {
      Swal.fire({
        title: 'Lỗi',
        text: 'Vui lòng thêm ít nhất một sản phẩm',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
      return;
    }

    // Lấy ID từ URL
    const getIdFromUrl = () => {
      const pathParts = window.location.pathname.split('/');
      for (let i = 0; i < pathParts.length; i++) {
        if (pathParts[i] === 'edit' && i > 0) {
          return pathParts[i-1];
        }
      }
      return null;
    };

    const id = getIdFromUrl();

    // Sử dụng baseUrl đã được định nghĩa
    const updateUrl = `${baseUrl}admin/warehouses/transfer-requests/${id}/update`;

    // Debug - hiển thị thông tin request
    console.log('Sending request to:', updateUrl);
    console.log('Form data:', data);

    // Submit form - sử dụng URL tuyệt đối với locale
    fetch(updateUrl, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      },
      body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
      if (result.code === 200) {
        Swal.fire({
          title: 'Thành công',
          text: result.text,
          icon: 'success',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        }).then(() => {
          if (result.redirect) {
            window.location.href = result.redirect;
          }
        });
      } else {
        Swal.fire({
          title: 'Lỗi',
          text: result.text,
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
      }
    })
    .catch(error => {
      console.error('Error:', error);
      Swal.fire({
        title: 'Lỗi',
        text: 'Đã xảy ra lỗi khi gửi yêu cầu',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
    });
  }
});
