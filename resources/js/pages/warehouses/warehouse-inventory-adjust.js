/**
 * Warehouse Inventory - Adjust
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
    (function () {
        const adjustInventoryForm = document.getElementById('adjustInventoryForm');
        const warehouseSelect = document.getElementById('warehouse_id');
        const productSelect = document.getElementById('product_id');
        const currentQuantityInput = document.getElementById('current_quantity');
        const newQuantityInput = document.getElementById('new_quantity');
        const reasonInput = document.getElementById('reason');

        // Initialize Select2
        if (productSelect) {
            const $productSelect = $(productSelect);
            select2Focus($productSelect);
            $productSelect.wrap('<div class="position-relative"></div>').select2({
                placeholder: 'Chọn sản phẩm',
                dropdownParent: $productSelect.parent()
            });
        }

        // Warehouse change event
        if (warehouseSelect) {
            warehouseSelect.addEventListener('change', function () {
                if (this.value) {
                    // Load products in the selected warehouse
                    $.ajax({
                        type: 'GET',
                        url: '/admin/warehouses/inventory/products',
                        data: {
                            warehouse_id: this.value
                        },
                        success: function (response) {
                            // Clear previous options
                            productSelect.innerHTML = '<option value="">Chọn sản phẩm</option>';

                            // Add new options
                            response.forEach(function (product) {
                                const option = document.createElement('option');
                                option.value = product.id;
                                option.textContent = product.text;
                                option.dataset.availableQuantity = product.available_quantity;
                                productSelect.appendChild(option);
                            });

                            // Refresh Select2
                            $(productSelect).trigger('change');
                        }
                    });
                } else {
                    // Clear product select
                    productSelect.innerHTML = '<option value="">Chọn sản phẩm</option>';
                    $(productSelect).trigger('change');

                    // Reset current quantity
                    currentQuantityInput.value = '';
                    newQuantityInput.value = '';
                }
            });
        }

        // Product change event
        if (productSelect) {
            productSelect.addEventListener('change', function () {
                if (this.value && warehouseSelect.value) {
                    // Get product details
                    $.ajax({
                        type: 'GET',
                        url: '/admin/warehouses/inventory/product-details',
                        data: {
                            warehouse_id: warehouseSelect.value,
                            product_id: this.value
                        },
                        success: function (response) {
                            if (response.success) {
                                // Set current quantity
                                currentQuantityInput.value = response.data.quantity;
                                newQuantityInput.value = response.data.quantity;
                                newQuantityInput.min = 0;
                            } else {
                                // Product not found in warehouse
                                currentQuantityInput.value = '0';
                                newQuantityInput.value = '0';
                                newQuantityInput.min = 0;
                            }
                        }
                    });
                } else {
                    // Reset current quantity
                    currentQuantityInput.value = '';
                    newQuantityInput.value = '';
                }
            });
        }

        // Form validation
        if (adjustInventoryForm) {
            const fv = FormValidation.formValidation(adjustInventoryForm, {
                fields: {
                    warehouse_id: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng chọn kho hàng'
                            }
                        }
                    },
                    product_id: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng chọn sản phẩm'
                            }
                        }
                    },
                    new_quantity: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập số lượng mới'
                            },
                            numeric: {
                                message: 'Số lượng phải là số'
                            },
                            greaterThan: {
                                message: 'Số lượng phải lớn hơn hoặc bằng 0',
                                min: 0
                            }
                        }
                    },
                    reason: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập lý do điều chỉnh'
                            },
                            stringLength: {
                                max: 500,
                                message: 'Lý do không được vượt quá 500 ký tự'
                            }
                        }
                    }
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap5: new FormValidation.plugins.Bootstrap5({
                        eleValidClass: '',
                        rowSelector: '.col-md-6, .col-md-12'
                    }),
                    submitButton: new FormValidation.plugins.SubmitButton(),
                    autoFocus: new FormValidation.plugins.AutoFocus()
                }
            }).on('core.form.valid', function () {
                // Get form data
                const formData = {
                    warehouse_id: warehouseSelect.value,
                    product_id: productSelect.value,
                    new_quantity: newQuantityInput.value,
                    reason: reasonInput.value,
                    _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                };

                // Submit form data via AJAX
                $.ajax({
                    type: 'POST',
                    url: '/admin/warehouses/inventory/adjust',
                    data: formData,
                    success: function (response) {
                        if (response.code === 200) {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.text,
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function () {
                                window.location.href = '/admin/warehouses/inventory';
                            });
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.text,
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        }
                    },
                    error: function (error) {
                        let errorMessage = 'Có lỗi xảy ra khi điều chỉnh tồn kho. Vui lòng thử lại sau.';

                        if (error.responseJSON && error.responseJSON.errors) {
                            errorMessage = Object.values(error.responseJSON.errors).flat().join('<br>');
                        }

                        Swal.fire({
                            title: 'Lỗi!',
                            html: errorMessage,
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            });
        }
    })();
});
