/**
 * Good Receipt Invoice Handling (Legacy)
 *
 * Note: This file is kept for backward compatibility.
 * New functionality is implemented in good-receipt-invoices.js
 */

'use strict';

// DOM elements
const updateInvoiceBtn = document.getElementById('updateInvoiceBtn');
const updateInvoiceBtn2 = document.getElementById('updateInvoiceBtn2');
const addInvoiceBtn = document.getElementById('addInvoiceBtn');

// Global variables
let baseUrl = window.location.origin;
let currentLocale = document.documentElement.lang || 'vi';
let goodReceiptId = null;

// Initialize components
document.addEventListener('DOMContentLoaded', function () {
  // Lấy dữ liệu từ thuộc tính data-* của container
  const container = document.getElementById('good-receipt-container');
  if (container) {
    goodReceiptId = parseInt(container.dataset.goodReceiptId);
  } else {
    // Fallback: Lấy ID từ URL nếu không có container
    const urlParts = window.location.pathname.split('/');
    const idIndex = urlParts.indexOf('good-receipts') + 1;
    if (idIndex > 0 && idIndex < urlParts.length) {
      goodReceiptId = parseInt(urlParts[idIndex]);
    }
  }

  // Event listeners for legacy buttons - redirect to new functionality
  if (updateInvoiceBtn) {
    updateInvoiceBtn.addEventListener('click', redirectToNewInvoiceUI);
  }

  if (updateInvoiceBtn2) {
    updateInvoiceBtn2.addEventListener('click', redirectToNewInvoiceUI);
  }
});

// Redirect to new invoice UI
function redirectToNewInvoiceUI() {
  if (!goodReceiptId) return;

  // Trigger click on the new add invoice button
  if (addInvoiceBtn) {
    addInvoiceBtn.click();
  } else {
    // Fallback: Show message
    Swal.fire({
      title: 'Thông báo',
      text: 'Chức năng nhập hóa đơn đã được cập nhật. Vui lòng sử dụng nút "Thêm hóa đơn" ở phần "Danh sách hóa đơn".',
      icon: 'info',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
  }
}
