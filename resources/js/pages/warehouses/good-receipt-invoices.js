/**
 * Good Receipt Invoices
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
  // DOM Elements
  const invoicesTable = document.getElementById('invoicesTable');
  const addInvoiceBtn = document.getElementById('addInvoiceBtn');
  const addInvoiceModal = document.getElementById('addInvoiceModal');
  const invoiceForm = document.getElementById('invoiceForm');
  const saveInvoiceBtn = document.getElementById('saveInvoiceBtn');
  const loadingInvoicesRow = document.getElementById('loadingInvoicesRow');
  const noInvoicesRow = document.getElementById('noInvoicesRow');
  const viewInvoiceAttachmentsModal = document.getElementById('viewInvoiceAttachmentsModal');
  const invoiceAttachmentsTable = document.getElementById('invoiceAttachmentsTable');

  // Variables
  let goodReceiptId = null;
  let goodReceiptStatus = null;
  let invoiceModal = null;
  let viewAttachmentsModal = null;
  let currentInvoiceId = null;
  let currentLocale = document.documentElement.lang || 'vi';
  let baseUrl = window.location.origin;

  // Constants for status
  const STATUS_DRAFT = 'DRAFT';
  const STATUS_PENDING = 'PENDING';
  const STATUS_APPROVED = 'APPROVED';
  const STATUS_COMPLETED = 'COMPLETED';
  const STATUS_CANCELLED = 'CANCELLED';

  // Initialize
  function init() {
    // Try to get good receipt ID and status from container
    const container = document.getElementById('good-receipt-container');
    if (container) {
      goodReceiptId = container.dataset.goodReceiptId;
      goodReceiptStatus = container.dataset.goodReceiptStatus;
    } else {
      // Fallback: Get ID from URL
      const urlParts = window.location.pathname.split('/');
      const idIndex = urlParts.indexOf('good-receipts') + 1;
      if (idIndex > 0 && idIndex < urlParts.length) {
        goodReceiptId = parseInt(urlParts[idIndex]);
      }
    }

    // Initialize modals and add event listeners if buttons exist
    if (addInvoiceBtn) {
      invoiceModal = new bootstrap.Modal(addInvoiceModal);
      addInvoiceBtn.addEventListener('click', openAddInvoiceModal);
      saveInvoiceBtn.addEventListener('click', saveInvoice);
    }

    // Initialize view attachments modal if available
    if (viewInvoiceAttachmentsModal) {
      viewAttachmentsModal = new bootstrap.Modal(viewInvoiceAttachmentsModal);
    }

    // Load invoices if table exists and we have a good receipt ID
    if (invoicesTable && goodReceiptId) {
      loadInvoices();
    }
  }

  // Load invoices
  function loadInvoices() {
    if (!goodReceiptId || !invoicesTable) return;

    showLoading();

    fetch(`${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/${goodReceiptId}/invoices`)
      .then(response => response.json())
      .then(data => {
        hideLoading();

        if (data.success) {
          renderInvoices(data.data);
        } else {
          console.error('Error loading invoices:', data.message);
          showNoInvoices();
        }
      })
      .catch(error => {
        hideLoading();
        console.error('Error loading invoices:', error);
        showNoInvoices();
      });
  }

  // Render invoices
  function renderInvoices(invoices) {
    if (!invoicesTable) return;

    // Hide loading row
    loadingInvoicesRow.style.display = 'none';

    // Show no invoices row if no invoices
    if (!invoices || invoices.length === 0) {
      showNoInvoices();
      return;
    }

    // Hide no invoices row
    noInvoicesRow.style.display = 'none';

    // Clear existing rows except loading and no invoices rows
    const rows = invoicesTable.querySelectorAll('tbody tr:not(#loadingInvoicesRow):not(#noInvoicesRow)');
    rows.forEach(row => row.remove());

    // Add invoice rows
    invoices.forEach((invoice, index) => {
      const row = document.createElement('tr');
      row.id = `invoice-${invoice.id}`;

      row.innerHTML = `
        <td>${index + 1}</td>
        <td>
          <span class="fw-medium">${invoice.invoice_number}</span>
          ${invoice.invoice_series ? `<br><small class="text-muted">Seri: ${invoice.invoice_series}</small>` : ''}
          ${invoice.invoice_template ? `<br><small class="text-muted">Mẫu: ${invoice.invoice_template}</small>` : ''}
        </td>
        <td>${formatDate(invoice.invoice_date)}</td>
        <td>
          <button type="button" class="btn btn-sm btn-label-primary view-attachments-btn" data-id="${invoice.id}" data-bs-toggle="tooltip" data-bs-placement="top" title="Xem file đính kèm">
            <i class="ri-attachment-2"></i> ${invoice.attachments_count || 0} file
          </button>
        </td>
        <td>
          <div class="d-flex">
            ${(goodReceiptStatus === STATUS_DRAFT || goodReceiptStatus === STATUS_PENDING) ? `
              <button type="button" class="btn btn-sm btn-icon btn-label-primary me-1 edit-invoice-btn" data-id="${invoice.id}" data-bs-toggle="tooltip" data-bs-placement="top" title="Sửa">
                <i class="ri-edit-line"></i>
              </button>
              <button type="button" class="btn btn-sm btn-icon btn-label-danger delete-invoice-btn" data-id="${invoice.id}" data-bs-toggle="tooltip" data-bs-placement="top" title="Xóa">
                <i class="ri-delete-bin-line"></i>
              </button>
            ` : `
              <button type="button" class="btn btn-sm btn-icon btn-label-primary view-invoice-btn" data-id="${invoice.id}" data-bs-toggle="tooltip" data-bs-placement="top" title="Xem chi tiết">
                <i class="ri-eye-line"></i>
              </button>
            `}
          </div>
        </td>
      `;

      invoicesTable.querySelector('tbody').appendChild(row);

      // Add event listeners
      const viewAttachmentsBtn = row.querySelector('.view-attachments-btn');
      if (viewAttachmentsBtn) {
        viewAttachmentsBtn.addEventListener('click', () => viewInvoiceAttachments(invoice.id));
      }

      // Add event listeners for edit/delete buttons (only in DRAFT or PENDING state)
      if (goodReceiptStatus === STATUS_DRAFT || goodReceiptStatus === STATUS_PENDING) {
        const editBtn = row.querySelector('.edit-invoice-btn');
        if (editBtn) {
          editBtn.addEventListener('click', () => editInvoice(invoice.id));
        }

        const deleteBtn = row.querySelector('.delete-invoice-btn');
        if (deleteBtn) {
          deleteBtn.addEventListener('click', () => deleteInvoice(invoice.id));
        }
      } else {
        // Add event listener for view button (in other states)
        const viewBtn = row.querySelector('.view-invoice-btn');
        if (viewBtn) {
          viewBtn.addEventListener('click', () => viewInvoice(invoice.id));
        }
      }
    });

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }

  // Show no invoices message
  function showNoInvoices() {
    if (loadingInvoicesRow) loadingInvoicesRow.style.display = 'none';
    if (noInvoicesRow) noInvoicesRow.style.display = '';
  }

  // Open add invoice modal
  function openAddInvoiceModal() {
    // Reset form
    invoiceForm.reset();
    document.getElementById('invoice_id').value = '';

    // Set current date as default
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('invoice_date').value = today;

    // Set modal title
    addInvoiceModal.querySelector('.modal-title').textContent = 'Thêm hóa đơn mới';

    // Show modal
    invoiceModal.show();
  }

  // Edit invoice
  function editInvoice(invoiceId) {
    if (!invoiceId) return;

    showLoading();

    fetch(`${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/invoices/${invoiceId}`)
      .then(response => response.json())
      .then(data => {
        hideLoading();

        if (data.success) {
          // Fill form with invoice data
          const invoice = data.data;
          document.getElementById('invoice_id').value = invoice.id;
          document.getElementById('invoice_number').value = invoice.invoice_number;
          document.getElementById('invoice_series').value = invoice.invoice_series || '';
          document.getElementById('invoice_template').value = invoice.invoice_template || '';
          document.getElementById('invoice_date').value = formatDateForInput(invoice.invoice_date);

          document.getElementById('invoice_notes').value = invoice.notes || '';

          // Set modal title
          addInvoiceModal.querySelector('.modal-title').textContent = 'Sửa hóa đơn';

          // Show modal
          invoiceModal.show();
        } else {
          Swal.fire({
            title: 'Lỗi',
            text: data.message,
            icon: 'error',
            confirmButtonText: 'Đóng'
          });
        }
      })
      .catch(error => {
        hideLoading();
        console.error('Error loading invoice:', error);
        Swal.fire({
          title: 'Lỗi',
          text: 'Có lỗi xảy ra khi tải thông tin hóa đơn',
          icon: 'error',
          confirmButtonText: 'Đóng'
        });
      });
  }

  // Save invoice
  function saveInvoice() {
    if (!goodReceiptId) return;

    // Validate form
    if (!invoiceForm.checkValidity()) {
      invoiceForm.reportValidity();
      return;
    }

    const formData = new FormData(invoiceForm);
    formData.append('good_receipt_id', goodReceiptId);

    // Get invoice ID
    const invoiceId = document.getElementById('invoice_id').value;
    const isEdit = invoiceId !== '';

    // Set URL based on whether we're creating or updating
    const url = isEdit
      ? `${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/invoices/${invoiceId}`
      : `${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/${goodReceiptId}/invoices`;

    showLoading();

    fetch(url, {
      method: isEdit ? 'PUT' : 'POST',
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      },
      body: formData
    })
      .then(response => response.json())
      .then(data => {
        hideLoading();

        if (data.success) {
          // Close modal
          invoiceModal.hide();

          // Show success message
          Swal.fire({
            title: 'Thành công',
            text: data.message,
            icon: 'success',
            customClass: {
              confirmButton: 'btn btn-success'
            },
            buttonsStyling: false
          });

          // Reload invoices
          loadInvoices();
        } else {
          Swal.fire({
            title: 'Lỗi',
            text: data.message,
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
          });
        }
      })
      .catch(error => {
        hideLoading();
        console.error('Error saving invoice:', error);
        Swal.fire({
          title: 'Lỗi',
          text: error,
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
      });
  }

  // Delete invoice
  function deleteInvoice(invoiceId) {
    if (!invoiceId) return;

    Swal.fire({
      title: 'Xác nhận xóa',
      text: 'Bạn có chắc chắn muốn xóa hóa đơn này?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-secondary ms-1'
      },
      buttonsStyling: false
    }).then(result => {
      if (result.isConfirmed) {
        showLoading();

        fetch(`${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/invoices/${invoiceId}`, {
          method: 'DELETE',
          headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          }
        })
          .then(response => response.json())
          .then(data => {
            hideLoading();

            if (data.success) {
              Swal.fire({
                title: 'Thành công',
                text: data.message,
                icon: 'success',
                confirmButtonText: 'Đóng'
              });

              // Reload invoices
              loadInvoices();
            } else {
              Swal.fire({
                title: 'Lỗi',
                text: data.message,
                icon: 'error',
                confirmButtonText: 'Đóng'
              });
            }
          })
          .catch(error => {
            hideLoading();
            console.error('Error deleting invoice:', error);
            Swal.fire({
              title: 'Lỗi',
              text: 'Có lỗi xảy ra khi xóa hóa đơn',
              icon: 'error',
              confirmButtonText: 'Đóng'
            });
          });
      }
    });
  }

  // View invoice attachments
  function viewInvoiceAttachments(invoiceId) {
    if (!invoiceId) return;

    currentInvoiceId = invoiceId;

    // Show loading
    const loadingRow = document.getElementById('loadingInvoiceAttachmentsRow');
    const noAttachmentsRow = document.getElementById('noInvoiceAttachmentsRow');

    if (loadingRow) loadingRow.style.display = '';
    if (noAttachmentsRow) noAttachmentsRow.style.display = 'none';

    // Clear existing rows except loading and no attachments rows
    const rows = invoiceAttachmentsTable.querySelectorAll('tbody tr:not(#loadingInvoiceAttachmentsRow):not(#noInvoiceAttachmentsRow)');
    rows.forEach(row => row.remove());

    // Show modal
    viewAttachmentsModal.show();

    // Load attachments
    fetch(`${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/invoices/${invoiceId}/attachments`)
      .then(response => response.json())
      .then(data => {
        if (loadingRow) loadingRow.style.display = 'none';

        if (data.success) {
          renderInvoiceAttachments(data.data);
        } else {
          console.error('Error loading invoice attachments:', data.message);
          if (noAttachmentsRow) noAttachmentsRow.style.display = '';
        }
      })
      .catch(error => {
        if (loadingRow) loadingRow.style.display = 'none';
        console.error('Error loading invoice attachments:', error);
        if (noAttachmentsRow) noAttachmentsRow.style.display = '';
      });
  }

  // Render invoice attachments
  function renderInvoiceAttachments(attachments) {
    if (!invoiceAttachmentsTable) return;

    // Show no attachments row if no attachments
    if (!attachments || attachments.length === 0) {
      document.getElementById('noInvoiceAttachmentsRow').style.display = '';
      return;
    }

    // Hide no attachments row
    document.getElementById('noInvoiceAttachmentsRow').style.display = 'none';

    // Add attachment rows
    attachments.forEach((attachment, index) => {
      const row = document.createElement('tr');
      row.id = `invoice-attachment-${attachment.id}`;

      const fileUrl = `/storage/${attachment.file_path}`;
      const fileExtension = attachment.file_name.split('.').pop().toLowerCase();
      const fileIcon = getFileIcon(fileExtension);

      row.innerHTML = `
        <td>${index + 1}</td>
        <td>
          <a href="${fileUrl}" target="_blank" class="d-flex align-items-center">
            <i class="${fileIcon} me-2 fs-5"></i>
            <span>${attachment.file_name}</span>
          </a>
        </td>
        <td>${getFileType(fileExtension)}</td>
        <td>${attachment.file_size_human}</td>
        <td>${formatDate(attachment.created_at)}</td>
        <td>
          <div class="d-flex">
            <a href="${fileUrl}" target="_blank" class="btn btn-sm btn-icon btn-label-primary me-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Xem">
              <i class="ri-eye-line"></i>
            </a>
            <a href="${fileUrl}" download="${attachment.file_name}" class="btn btn-sm btn-icon btn-label-success me-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Tải xuống">
              <i class="ri-download-line"></i>
            </a>
            ${(goodReceiptStatus === STATUS_DRAFT || goodReceiptStatus === STATUS_PENDING) ? `
            <button type="button" class="btn btn-sm btn-icon btn-label-danger delete-attachment-btn" data-id="${attachment.id}" data-bs-toggle="tooltip" data-bs-placement="top" title="Xóa">
              <i class="ri-delete-bin-line"></i>
            </button>
            ` : ''}
          </div>
        </td>
      `;

      invoiceAttachmentsTable.querySelector('tbody').appendChild(row);

      // Add event listener to delete button (only in DRAFT or PENDING state)
      if (goodReceiptStatus === STATUS_DRAFT || goodReceiptStatus === STATUS_PENDING) {
        const deleteBtn = row.querySelector('.delete-attachment-btn');
        if (deleteBtn) {
          deleteBtn.addEventListener('click', () => deleteInvoiceAttachment(attachment.id));
        }
      }
    });

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }

  // Delete invoice attachment
  function deleteInvoiceAttachment(attachmentId) {
    if (!attachmentId || !currentInvoiceId) return;

    Swal.fire({
      title: 'Xác nhận xóa',
      text: 'Bạn có chắc chắn muốn xóa file đính kèm này?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-secondary ms-1'
      },
      buttonsStyling: false
    }).then(result => {
      if (result.isConfirmed) {
        showLoading();

        fetch(`${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/invoices/${currentInvoiceId}/attachments/${attachmentId}`, {
          method: 'DELETE',
          headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          }
        })
          .then(response => response.json())
          .then(data => {
            hideLoading();

            if (data.success) {
              Swal.fire({
                title: 'Thành công',
                text: data.message,
                icon: 'success',
                confirmButtonText: 'Đóng'
              });

              // Reload attachments
              viewInvoiceAttachments(currentInvoiceId);

              // Reload invoices to update attachment count
              loadInvoices();
            } else {
              Swal.fire({
                title: 'Lỗi',
                text: data.message,
                icon: 'error',
                confirmButtonText: 'Đóng'
              });
            }
          })
          .catch(error => {
            hideLoading();
            console.error('Error deleting attachment:', error);
            Swal.fire({
              title: 'Lỗi',
              text: 'Có lỗi xảy ra khi xóa file đính kèm',
              icon: 'error',
              confirmButtonText: 'Đóng'
            });
          });
      }
    });
  }

  // Helper functions
  function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit', year: 'numeric' });
  }

  function formatDateForInput(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  }

  function formatCurrency(amount) {
    if (amount === null || amount === undefined) return '0 đ';
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
  }

  function getFileIcon(extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return 'ri-file-pdf-line';
      case 'doc':
      case 'docx':
        return 'ri-file-word-line';
      case 'xls':
      case 'xlsx':
        return 'ri-file-excel-line';
      case 'ppt':
      case 'pptx':
        return 'ri-file-ppt-line';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return 'ri-image-line';
      case 'zip':
      case 'rar':
      case '7z':
        return 'ri-file-zip-line';
      case 'txt':
        return 'ri-file-text-line';
      default:
        return 'ri-file-line';
    }
  }

  function getFileType(extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return 'PDF';
      case 'doc':
      case 'docx':
        return 'Word';
      case 'xls':
      case 'xlsx':
        return 'Excel';
      case 'ppt':
      case 'pptx':
        return 'PowerPoint';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return 'Hình ảnh';
      case 'zip':
      case 'rar':
      case '7z':
        return 'Nén';
      case 'txt':
        return 'Văn bản';
      default:
        return 'Khác';
    }
  }

  function showLoading() {
    if (typeof window.showLoading === 'function') {
      window.showLoading();
    }
  }

  function hideLoading() {
    if (typeof window.hideLoading === 'function') {
      window.hideLoading();
    }
  }

  // View invoice details (read-only)
  function viewInvoice(invoiceId) {
    if (!invoiceId) return;

    showLoading();

    fetch(`${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/invoices/${invoiceId}`)
      .then(response => response.json())
      .then(data => {
        hideLoading();

        if (data.success) {
          // Show invoice details in a modal or alert
          const invoice = data.data;
          Swal.fire({
            title: 'Chi tiết hóa đơn',
            html: `
              <div class="text-start">
                <p><strong>Số hóa đơn:</strong> ${invoice.invoice_number}</p>
                ${invoice.invoice_series ? `<p><strong>Số seri:</strong> ${invoice.invoice_series}</p>` : ''}
                ${invoice.invoice_template ? `<p><strong>Mẫu số:</strong> ${invoice.invoice_template}</p>` : ''}
                <p><strong>Ngày hóa đơn:</strong> ${formatDate(invoice.invoice_date)}</p>
                <p><strong>Ngày tạo:</strong> ${formatDate(invoice.created_at)}</p>
                ${invoice.notes ? `<p><strong>Ghi chú:</strong> ${invoice.notes}</p>` : ''}
              </div>
            `,
            icon: 'info',
            customClass: {
              confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
          });
        } else {
          Swal.fire({
            title: 'Lỗi',
            text: data.message,
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
          });
        }
      })
      .catch(error => {
        hideLoading();
        console.error('Error loading invoice:', error);
        Swal.fire({
          title: 'Lỗi',
          text: 'Có lỗi xảy ra khi tải thông tin hóa đơn',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
      });
  }

  // Initialize
  init();
});
