/**
 * Warehouse Inventory - Transfer
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
    (function () {
        const transferInventoryForm = document.getElementById('transferInventoryForm');
        const sourceWarehouseSelect = document.getElementById('source_warehouse_id');
        const destinationWarehouseSelect = document.getElementById('destination_warehouse_id');
        const productSelect = document.getElementById('product_id');
        const quantityInput = document.getElementById('quantity');
        const notesInput = document.getElementById('notes');
        const availableQuantityInfo = document.getElementById('available-quantity-info');
        const availableQuantitySpan = document.getElementById('available-quantity');

        // Initialize Select2
        if (productSelect) {
            const $productSelect = $(productSelect);
            select2Focus($productSelect);
            $productSelect.wrap('<div class="position-relative"></div>').select2({
                placeholder: 'Chọn sản phẩm',
                dropdownParent: $productSelect.parent()
            });
        }

        // Source warehouse change event
        if (sourceWarehouseSelect) {
            sourceWarehouseSelect.addEventListener('change', function () {
                if (this.value) {
                    // Load products in the selected warehouse
                    $.ajax({
                        type: 'GET',
                        url: `${baseUrl}admin/warehouses/inventory/products`,
                        data: {
                            warehouse_id: this.value
                        },
                        success: function (response) {
                            // Clear previous options
                            productSelect.innerHTML = '<option value="">Chọn sản phẩm</option>';

                            // Add new options
                            response.forEach(function (product) {
                                const option = document.createElement('option');
                                option.value = product.id;
                                option.textContent = product.text;
                                option.dataset.availableQuantity = product.available_quantity;
                                productSelect.appendChild(option);
                            });

                            // Refresh Select2
                            $(productSelect).trigger('change');
                        }
                    });

                    // Update destination warehouse options
                    updateDestinationWarehouseOptions();
                } else {
                    // Clear product select
                    productSelect.innerHTML = '<option value="">Chọn sản phẩm</option>';
                    $(productSelect).trigger('change');

                    // Hide available quantity info
                    availableQuantityInfo.classList.add('d-none');

                    // Reset destination warehouse options
                    updateDestinationWarehouseOptions();
                }
            });
        }

        // Destination warehouse change event
        if (destinationWarehouseSelect) {
            destinationWarehouseSelect.addEventListener('change', function () {
                // Validate source and destination warehouses are different
                if (this.value && this.value === sourceWarehouseSelect.value) {
                    Swal.fire({
                        title: 'Lỗi!',
                        text: 'Kho nguồn và kho đích không được trùng nhau',
                        icon: 'error',
                        customClass: {
                            confirmButton: 'btn btn-primary'
                        }
                    });
                    this.value = '';
                }
            });
        }

        // Product change event
        if (productSelect) {
            productSelect.addEventListener('change', function () {
                if (this.value) {
                    const selectedOption = this.options[this.selectedIndex];
                    const availableQuantity = selectedOption.dataset.availableQuantity;

                    // Show available quantity info
                    availableQuantitySpan.textContent = availableQuantity;
                    availableQuantityInfo.classList.remove('d-none');

                    // Set max quantity
                    quantityInput.max = availableQuantity;
                } else {
                    // Hide available quantity info
                    availableQuantityInfo.classList.add('d-none');
                }
            });
        }

        // Update destination warehouse options
        function updateDestinationWarehouseOptions() {
            const sourceWarehouseId = sourceWarehouseSelect.value;

            // Enable all options
            Array.from(destinationWarehouseSelect.options).forEach(function (option) {
                option.disabled = false;
            });

            // Disable the source warehouse option in destination select
            if (sourceWarehouseId) {
                const option = destinationWarehouseSelect.querySelector(`option[value="${sourceWarehouseId}"]`);
                if (option) {
                    option.disabled = true;
                }

                // If currently selected destination is the same as source, reset it
                if (destinationWarehouseSelect.value === sourceWarehouseId) {
                    destinationWarehouseSelect.value = '';
                }
            }
        }

        // Form validation
        if (transferInventoryForm) {
            const fv = FormValidation.formValidation(transferInventoryForm, {
                fields: {
                    source_warehouse_id: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng chọn kho nguồn'
                            }
                        }
                    },
                    destination_warehouse_id: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng chọn kho đích'
                            },
                            different: {
                                compare: function () {
                                    return sourceWarehouseSelect.value;
                                },
                                message: 'Kho đích phải khác kho nguồn'
                            }
                        }
                    },
                    product_id: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng chọn sản phẩm'
                            }
                        }
                    },
                    quantity: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập số lượng'
                            },
                            numeric: {
                                message: 'Số lượng phải là số'
                            },
                            greaterThan: {
                                message: 'Số lượng phải lớn hơn 0',
                                min: 0.01
                            },
                            callback: {
                                message: 'Số lượng không được vượt quá số lượng khả dụng',
                                callback: function (input) {
                                    const value = parseFloat(input.value);
                                    const availableQuantity = parseFloat(availableQuantitySpan.textContent);
                                    return value <= availableQuantity;
                                }
                            }
                        }
                    },
                    notes: {
                        validators: {
                            stringLength: {
                                max: 500,
                                message: 'Ghi chú không được vượt quá 500 ký tự'
                            }
                        }
                    }
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap5: new FormValidation.plugins.Bootstrap5({
                        eleValidClass: '',
                        rowSelector: '.col-md-6, .col-md-12'
                    }),
                    submitButton: new FormValidation.plugins.SubmitButton(),
                    autoFocus: new FormValidation.plugins.AutoFocus()
                }
            }).on('core.form.valid', function () {
                // Get form data
                const formData = {
                    source_warehouse_id: sourceWarehouseSelect.value,
                    destination_warehouse_id: destinationWarehouseSelect.value,
                    product_id: productSelect.value,
                    quantity: quantityInput.value,
                    notes: notesInput.value,
                    _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                };

                // Submit form data via AJAX
                $.ajax({
                    type: 'POST',
                    url: `${baseUrl}admin/warehouses/inventory/transfer`,
                    data: formData,
                    success: function (response) {
                        if (response.code === 200) {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.text,
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function () {
                                window.location.href = `${baseUrl}admin/warehouses/inventory`;
                            });
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.text,
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        }
                    },
                    error: function (error) {
                        let errorMessage = 'Có lỗi xảy ra khi chuyển kho. Vui lòng thử lại sau.';

                        if (error.responseJSON && error.responseJSON.errors) {
                            errorMessage = Object.values(error.responseJSON.errors).flat().join('<br>');
                        }

                        Swal.fire({
                            title: 'Lỗi!',
                            html: errorMessage,
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            });
        }
    })();
});
