/**
 * Chi tiết yêu cầu chuyển hàng
 */

'use strict';

// DOM ready function
document.addEventListener('DOMContentLoaded', function () {
  // Initialize tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });

  // Submit button
  const submitBtn = document.getElementById('submitBtn');
  if (submitBtn) {
    submitBtn.addEventListener('click', function () {
      Swal.fire({
        title: 'Xác nhận',
        text: 'Bạn có chắc chắn muốn gửi yêu cầu này để duyệt?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'G<PERSON>i duyệt',
        cancelButtonText: 'Hủy',
        customClass: {
          confirmButton: 'btn btn-primary me-3',
          cancelButton: 'btn btn-label-secondary'
        },
        buttonsStyling: false
      }).then(function (result) {
        if (result.isConfirmed) {
          showLoading();
          // Gửi yêu cầu duyệt - sử dụng URL tuyệt đối
          fetch(window.location.origin + `/admin/warehouses/transfer-requests/${window.requestId}/submit`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
              'X-Requested-With': 'XMLHttpRequest'
            }
          })
            .then(response => response.json())
            .then(data => {
              hideLoading();
              if (data.code === 200) {
                Swal.fire({
                  icon: 'success',
                  title: 'Thành công',
                  text: data.text,
                  customClass: {
                    confirmButton: 'btn btn-success'
                  },
                  buttonsStyling: false
                }).then(() => {
                  if (data.redirect) {
                    window.location.href = data.redirect;
                  } else {
                    window.location.reload();
                  }
                });
              } else {
                Swal.fire({
                  title: 'Lỗi',
                  text: data.text,
                  icon: 'error',
                  customClass: {
                    confirmButton: 'btn btn-primary'
                  },
                  buttonsStyling: false
                });
              }
            })
            .catch(error => {
              hideLoading();
              console.error('Error:', error);
              Swal.fire({
                title: 'Lỗi',
                text: 'Đã xảy ra lỗi khi gửi yêu cầu. Vui lòng thử lại sau.',
                icon: 'error',
                customClass: {
                  confirmButton: 'btn btn-primary'
                },
                buttonsStyling: false
              });
            });
        }
      });
    });
  }

  // Approve button and modal
  const approveBtn = document.getElementById('approveBtn');
  const approveModal = new bootstrap.Modal(document.getElementById('approveModal'));
  const confirmApproveBtn = document.getElementById('confirmApproveBtn');

  if (approveBtn && confirmApproveBtn) {
    approveBtn.addEventListener('click', function () {
      approveModal.show();
    });

    confirmApproveBtn.addEventListener('click', function (e) {
      // Ngăn chặn hành vi mặc định
      e.preventDefault();
      const form = document.getElementById('approveForm');
      const formData = new FormData(form);
      const data = {};

      // Debug - hiển thị dữ liệu form trước khi xử lý
      console.log('Raw form data:');
      for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
      }

      // Convert FormData to JSON - cách đơn giản hơn
      const items = [];
      const itemIds = new Set();

      // Tìm tất cả các item id trước
      formData.forEach((value, key) => {
        if (key.includes('items[') && key.includes('][id]')) {
          itemIds.add(value);
        }
      });

      // Tạo các item dựa trên id
      itemIds.forEach(id => {
        const item = { id: id };
        items.push(item);
      });

      // Thêm các trường khác vào item
      formData.forEach((value, key) => {
        if (key.includes('items[') && key.includes('][approved_quantity]')) {
          const idMatch = key.match(/items\[(\d+)\]/);
          if (idMatch && idMatch[1]) {
            const index = parseInt(idMatch[1]);
            const itemId = formData.get(`items[${index}][id]`);
            const item = items.find(i => i.id === itemId);
            if (item) {
              item.approved_quantity = value;
            }
          }
        } else if (!key.includes('[')) {
          // Các trường không phải mảng
          data[key] = value;
        }
      });

      // Gán mảng items vào data
      data.items = items;

      // Kiểm tra và xử lý dữ liệu items
      if (data.items && data.items.length > 0) {
        // Chuyển đổi approved_quantity từ string sang number
        data.items.forEach(item => {
          if (item.approved_quantity) {
            item.approved_quantity = parseInt(item.approved_quantity, 10);
          } else {
            // Nếu không có approved_quantity, gán giá trị mặc định là 0
            item.approved_quantity = 0;
          }
        });

        // Log dữ liệu items sau khi xử lý
        console.log('Processed items:', data.items);
      } else {
        // Nếu không có items, hiển thị thông báo lỗi
        Swal.fire({
          title: 'Lỗi',
          text: 'Không tìm thấy thông tin sản phẩm để duyệt',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
        hideLoading();
        return;
      }

      // Debug - hiển thị dữ liệu gửi đi
      console.log('Form data to be sent:', data);

      // Kiểm tra xem có items không
      if (!data.items || data.items.length === 0) {
        Swal.fire({
          title: 'Lỗi',
          text: 'Không có sản phẩm nào để duyệt',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
        return;
      }

      showLoading();
      // Gửi yêu cầu duyệt - sử dụng URL tuyệt đối
      fetch(window.location.origin + `/admin/warehouses/transfer-requests/${window.requestId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(data)
      })
        .then(async response => {
          console.log('Response status:', response.status);
          console.log('Response headers:', response.headers);

          // Kiểm tra nếu response không thành công
          if (!response.ok) {
            console.error('Response not OK:', response.status, response.statusText);
            try {
              const errorData = await response.json();
              console.error('Error data:', errorData);
              throw { status: response.status, data: errorData };
            } catch (e) {
              console.error('Error parsing error response:', e);
              throw { status: response.status, data: { text: 'Lỗi không xác định' } };
            }
          }

          const responseData = await response.json();
          console.log('Response data:', responseData);
          return responseData;
        })
        .then(data => {
          hideLoading();
          approveModal.hide();
          if (data.code === 200) {
            Swal.fire({
              icon: 'success',
              title: 'Thành công',
              text: data.text,
              customClass: {
                confirmButton: 'btn btn-success'
              },
              buttonsStyling: false
            }).then(() => {
              if (data.redirect) {
                window.location.href = data.redirect;
              } else {
                window.location.reload();
              }
            });
          } else {
            Swal.fire({
              title: 'Lỗi',
              text: data.text,
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-primary'
              },
              buttonsStyling: false
            });
          }
        })
        .catch(error => {
          hideLoading();
          approveModal.hide();
          console.error('Error:', error);

          let errorMessage = 'Đã xảy ra lỗi khi gửi yêu cầu. Vui lòng thử lại sau.';

          // Nếu có thông tin lỗi chi tiết
          if (error.data && error.data.errors) {
            const errorDetails = Object.values(error.data.errors).flat();
            if (errorDetails.length > 0) {
              errorMessage = errorDetails.join('\n');
            } else if (error.data.text) {
              errorMessage = error.data.text;
            }
          } else if (error.data && error.data.text) {
            errorMessage = error.data.text;
          }

          Swal.fire({
            title: 'Lỗi',
            text: errorMessage,
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
          });
        });
    });
  }

  // Reject button and modal
  const rejectBtn = document.getElementById('rejectBtn');
  const rejectModal = new bootstrap.Modal(document.getElementById('rejectModal'));
  const confirmRejectBtn = document.getElementById('confirmRejectBtn');

  if (rejectBtn && confirmRejectBtn) {
    rejectBtn.addEventListener('click', function () {
      rejectModal.show();
    });

    confirmRejectBtn.addEventListener('click', function (e) {
      // Ngăn chặn hành vi mặc định
      e.preventDefault();
      const rejectionReason = document.getElementById('rejection_reason').value;

      if (!rejectionReason.trim()) {
        Swal.fire({
          title: 'Lỗi',
          text: 'Vui lòng nhập lý do từ chối',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
        return;
      }

      showLoading();
      // Gửi yêu cầu từ chối - sử dụng URL tuyệt đối
      fetch(window.location.origin + `/admin/warehouses/transfer-requests/${window.requestId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ reason: rejectionReason })
      })
        .then(response => response.json())
        .then(data => {
          hideLoading();
          rejectModal.hide();
          if (data.code === 200) {
            Swal.fire({
              icon: 'success',
              title: 'Thành công',
              text: data.text,
              customClass: {
                confirmButton: 'btn btn-success'
              },
              buttonsStyling: false
            }).then(() => {
              if (data.redirect) {
                window.location.href = data.redirect;
              } else {
                window.location.reload();
              }
            });
          } else {
            Swal.fire({
              title: 'Lỗi',
              text: data.text,
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-primary'
              },
              buttonsStyling: false
            });
          }
        })
        .catch(error => {
          hideLoading();
          rejectModal.hide();
          console.error('Error:', error);
          Swal.fire({
            title: 'Lỗi',
            text: 'Đã xảy ra lỗi khi gửi yêu cầu. Vui lòng thử lại sau.',
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
          });
        });
    });
  }

  // Cancel button and modal
  const cancelBtn = document.getElementById('cancelBtn');
  const cancelModal = new bootstrap.Modal(document.getElementById('cancelModal'));
  const confirmCancelBtn = document.getElementById('confirmCancelBtn');

  if (cancelBtn && confirmCancelBtn) {
    cancelBtn.addEventListener('click', function () {
      cancelModal.show();
    });

    confirmCancelBtn.addEventListener('click', function (e) {
      // Ngăn chặn hành vi mặc định
      e.preventDefault();
      const cancelReason = document.getElementById('cancel_reason').value;

      showLoading();
      // Gửi yêu cầu hủy - sử dụng URL tuyệt đối
      fetch(window.location.origin + `/admin/warehouses/transfer-requests/${window.requestId}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ cancel_reason: cancelReason })
      })
        .then(response => response.json())
        .then(data => {
          hideLoading();
          cancelModal.hide();
          if (data.code === 200) {
            Swal.fire({
              icon: 'success',
              title: 'Thành công',
              text: data.text,
              customClass: {
                confirmButton: 'btn btn-success'
              },
              buttonsStyling: false
            }).then(() => {
              if (data.redirect) {
                window.location.href = data.redirect;
              } else {
                window.location.reload();
              }
            });
          } else {
            Swal.fire({
              title: 'Lỗi',
              text: data.text,
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-primary'
              },
              buttonsStyling: false
            });
          }
        })
        .catch(error => {
          hideLoading();
          cancelModal.hide();
          console.error('Error:', error);
          Swal.fire({
            title: 'Lỗi',
            text: 'Đã xảy ra lỗi khi gửi yêu cầu. Vui lòng thử lại sau.',
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
          });
        });
    });
  }

  // Create Transfer button
  const createTransferBtn = document.getElementById('createTransferBtn');
  if (createTransferBtn) {
    createTransferBtn.addEventListener('click', function () {
      Swal.fire({
        title: 'Xác nhận',
        text: 'Bạn có chắc chắn muốn tạo phiếu chuyển kho từ yêu cầu này?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Tạo phiếu',
        cancelButtonText: 'Hủy',
        customClass: {
          confirmButton: 'btn btn-primary me-3',
          cancelButton: 'btn btn-label-secondary'
        },
        buttonsStyling: false
      }).then(function (result) {
        if (result.isConfirmed) {
          showLoading();
          // Gửi yêu cầu tạo phiếu chuyển kho - sử dụng URL tuyệt đối
          fetch(window.location.origin + `/admin/warehouses/transfer-requests/${window.requestId}/create-transfer`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
              'X-Requested-With': 'XMLHttpRequest'
            }
          })
            .then(response => response.json())
            .then(data => {
              hideLoading();
              if (data.code === 200) {
                Swal.fire({
                  icon: 'success',
                  title: 'Thành công',
                  text: data.text,
                  customClass: {
                    confirmButton: 'btn btn-success'
                  },
                  buttonsStyling: false
                }).then(() => {
                  if (data.redirect) {
                    window.location.href = data.redirect;
                  } else {
                    window.location.reload();
                  }
                });
              } else {
                Swal.fire({
                  title: 'Lỗi',
                  text: data.text,
                  icon: 'error',
                  customClass: {
                    confirmButton: 'btn btn-primary'
                  },
                  buttonsStyling: false
                });
              }
            })
            .catch(error => {
              hideLoading();
              console.error('Error:', error);
              Swal.fire({
                title: 'Lỗi',
                text: 'Đã xảy ra lỗi khi gửi yêu cầu. Vui lòng thử lại sau.',
                icon: 'error',
                customClass: {
                  confirmButton: 'btn btn-primary'
                },
                buttonsStyling: false
              });
            });
        }
      });
    });
  }

  // Show loading
  function showLoading() {
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
      button.disabled = true;
    });
  }

  // Hide loading
  function hideLoading() {
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
      button.disabled = false;
    });
  }
});
