/**
 * Good Receipt Show
 */

'use strict';

// DOM elements
const cancelBtn = document.getElementById('cancelBtn');
const confirmCancelBtn = document.getElementById('confirmCancelBtn');
const viewSerialsButtons = document.querySelectorAll('.view-serials-btn');

// Global variables
let cancelModal = null;
let serialsModal = null;
let goodReceiptStatus = null;
let goodReceiptId = null;
let baseUrl = window.location.origin;
let currentLocale = document.documentElement.lang || 'vi';

// Initialize components
document.addEventListener('DOMContentLoaded', function () {
  // Lấy dữ liệu từ thuộc tính data-* của container
  const container = document.getElementById('good-receipt-container');
  if (container) {
    goodReceiptStatus = container.dataset.goodReceiptStatus;
    goodReceiptId = parseInt(container.dataset.goodReceiptId);
  } else {
    // Fallback: Lấy ID từ URL nếu không có container
    goodReceiptId = parseInt(window.location.pathname.split('/').pop());
  }

  // Initialize Bootstrap Modals
  if (document.getElementById('cancelModal')) {
    cancelModal = new bootstrap.Modal(document.getElementById('cancelModal'));
  }

  if (document.getElementById('serialsModal')) {
    serialsModal = new bootstrap.Modal(document.getElementById('serialsModal'));
  }

  // Event listeners
  if (cancelBtn) {
    cancelBtn.addEventListener('click', showCancelModal);
  }

  if (confirmCancelBtn) {
    confirmCancelBtn.addEventListener('click', cancelGoodReceipt);
  }

  // Add event listeners to view serials buttons
  viewSerialsButtons.forEach(btn => {
    btn.addEventListener('click', function() {
      const itemId = this.dataset.id;
      viewSerials(itemId);
    });
  });

  // Kiểm tra và cập nhật trạng thái từ localStorage
  updateItemStatusFromLocalStorage();
});

// Show cancel modal
function showCancelModal() {
  document.getElementById('cancel_id').value = goodReceiptId;
  document.getElementById('cancel_reason').value = '';
  cancelModal.show();
}

// Cancel good receipt
function cancelGoodReceipt() {
  const goodReceiptId = document.getElementById('cancel_id').value;
  const reason = document.getElementById('cancel_reason').value;

  if (!reason) {
    Swal.fire({
      title: 'Lỗi',
      text: 'Vui lòng nhập lý do hủy',
      icon: 'error',
      customClass: {
        confirmButton: 'btn btn-primary'
      },
      buttonsStyling: false
    });
    return;
  }

  // Show loading
  showLoading();

  fetch(`${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/${goodReceiptId}/cancel`, {
    method: 'POST',
    headers: {
      'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ reason: reason })
  })
    .then(response => response.json())
    .then(data => {
      hideLoading();
      if (data.success) {
        cancelModal.hide();
        Swal.fire({
          title: 'Thành công',
          text: data.message,
          icon: 'success',
          customClass: {
            confirmButton: 'btn btn-success'
          },
          buttonsStyling: false
        }).then(() => {
          window.location.href = data.redirect;
        });
      } else {
        Swal.fire({
          title: 'Lỗi',
          text: data.message,
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
      }
    })
    .catch(error => {
      console.error('Error:', error);
      Swal.fire({
        title: 'Lỗi',
        text: 'Đã xảy ra lỗi khi hủy phiếu nhập kho',
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-primary'
        },
        buttonsStyling: false
      });
    });
}

// View serials
function viewSerials(itemId) {
  const loadingRow = document.getElementById('loadingRow');
  const noSerialsRow = document.getElementById('noSerialsRow');
  const serialsTable = document.getElementById('serialsTable');

  // Show loading row
  loadingRow.style.display = '';
  noSerialsRow.style.display = 'none';

  // Clear existing rows except loading and no serials rows
  const rows = serialsTable.querySelectorAll('tbody tr:not(#loadingRow):not(#noSerialsRow)');
  rows.forEach(row => row.remove());

  // Show modal
  serialsModal.show();

  // Kiểm tra xem có dữ liệu trong localStorage không
  const storageKey = `good_receipt_${goodReceiptId}_import`;
  const savedData = JSON.parse(localStorage.getItem(storageKey) || '{}');
  const localSerials = [];

  // Nếu có dữ liệu trong localStorage và có dữ liệu cho item này
  if (savedData[itemId] && savedData[itemId].type === 'serial' && savedData[itemId].serials) {
    // Thêm các serial từ localStorage vào danh sách
    savedData[itemId].serials.forEach(serialNumber => {
      localSerials.push({
        serial_number: serialNumber,
        status: 'pending',
        created_at: new Date().toISOString(),
        source: 'local' // Đánh dấu nguồn là từ localStorage
      });
    });
  }

  // Fetch serials từ server
  fetch(`${baseUrl}/${currentLocale}/admin/warehouses/good-receipts/${goodReceiptId}/serials/${itemId}`)
    .then(response => response.json())
    .then(data => {
      loadingRow.style.display = 'none';

      if (data.success) {
        // Kết hợp dữ liệu từ server và localStorage
        const allSerials = [...data.data];

        // Thêm các serial từ localStorage mà không trùng với dữ liệu từ server
        if (localSerials.length > 0) {
          const serverSerialNumbers = data.data.map(s => s.serial_number);

          localSerials.forEach(localSerial => {
            if (!serverSerialNumbers.includes(localSerial.serial_number)) {
              allSerials.push(localSerial);
            }
          });
        }

        if (allSerials.length === 0) {
          noSerialsRow.style.display = '';
        } else {
          renderSerials(allSerials);
        }
      } else {
        // Nếu có lỗi khi lấy dữ liệu từ server nhưng có dữ liệu từ localStorage
        if (localSerials.length > 0) {
          renderSerials(localSerials);
        } else {
          noSerialsRow.style.display = '';
          noSerialsRow.querySelector('td').textContent = 'Có lỗi xảy ra khi tải danh sách IMEI/Serial';
        }
      }
    })
    .catch(error => {
      console.error('Error:', error);
      loadingRow.style.display = 'none';

      // Nếu có lỗi khi lấy dữ liệu từ server nhưng có dữ liệu từ localStorage
      if (localSerials.length > 0) {
        renderSerials(localSerials);
      } else {
        noSerialsRow.style.display = '';
        noSerialsRow.querySelector('td').textContent = 'Có lỗi xảy ra khi tải danh sách IMEI/Serial';
      }
    });
}

// Render serials
function renderSerials(serials) {
  const serialsTable = document.getElementById('serialsTable');
  const tbody = serialsTable.querySelector('tbody');

  serials.forEach((serial, index) => {
    const row = document.createElement('tr');

    // Thêm class cho hàng nếu serial chưa được lưu vào database
    if (serial.source === 'local') {
      row.classList.add('table-warning');
    }

    row.innerHTML = `
      <td>${index + 1}</td>
      <td>${serial.serial_number}</td>
      <td>
        <span class="badge bg-label-${getStatusColor(serial.status)}">${getStatusText(serial.status)}</span>
        ${serial.source === 'local' ? '<span class="badge bg-label-warning ms-1">Chưa lưu</span>' : ''}
      </td>
      <td>${formatDate(serial.created_at)}</td>
    `;

    tbody.appendChild(row);
  });
}

// Get status text
function getStatusText(status) {
  switch (status) {
    case 'in_stock':
      return 'Trong kho';
    case 'sold':
      return 'Đã bán';
    case 'transferred':
      return 'Đã chuyển';
    case 'returned':
      return 'Đã trả';
    case 'cancelled':
      return 'Đã hủy';
    case 'pending':
      return 'Chờ duyệt';
    default:
      return 'Không xác định';
  }
}

// Get status color
function getStatusColor(status) {
  switch (status) {
    case 'in_stock':
      return 'success';
    case 'sold':
      return 'info';
    case 'transferred':
      return 'primary';
    case 'returned':
      return 'warning';
    case 'cancelled':
      return 'danger';
    case 'pending':
      return 'warning';
    default:
      return 'secondary';
  }
}

// Format date
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit' });
}



// Cập nhật trạng thái các dòng sản phẩm dựa trên trạng thái phiếu
function updateItemStatusFromLocalStorage() {
  // Lấy tất cả các dòng sản phẩm
  const rows = document.querySelectorAll('tr[data-item-id]');

  // Nếu phiếu đã được duyệt hoặc đang chờ duyệt, các sản phẩm đều đã được nhập
  if (goodReceiptStatus === 'completed' || goodReceiptStatus === 'pending') {
    rows.forEach(row => {
      const itemId = row.dataset.itemId;
      updateItemStatus(itemId, true);
    });
    return; // Thoát khỏi hàm vì đã xử lý xong
  }

  // Nếu phiếu đang ở trạng thái nháp, kiểm tra các phần tử giao diện và localStorage
  if (goodReceiptStatus === 'draft') {
    // Kiểm tra trạng thái hiện tại của các nút xem IMEI/Batch và warehouse_area_id
    rows.forEach(row => {
      const itemId = row.dataset.itemId;
      const trackingType = row.dataset.trackingType;
      const dataImported = row.dataset.isImported;
      const viewSerialsBtn = row.querySelector('.view-serials-btn');
      const viewBatchesBtn = row.querySelector('.view-batches-btn');
      const warehouseArea = row.querySelector('td:nth-child(9)').textContent.trim();
      const statusBadge = row.querySelector('.item-status');

      // Mặc định là chưa nhập
      let isImported = false;

      // Kiểm tra theo loại sản phẩm
      if (trackingType === 'serial' && dataImported === 'true') {
        // Nếu có nút xem IMEI, cập nhật trạng thái thành Đã nhập
        isImported = true;
      } else if (trackingType === 'batch' && dataImported === 'true') {
        // Nếu có nút xem Batch hoặc đã có warehouse_area_id
        isImported = (dataImported === 'true' || warehouseArea !== 'N/A');
      } else if (trackingType === 'quantity' && dataImported === 'true') {
        // Nếu đã có warehouse_area_id
        isImported = (warehouseArea !== 'N/A' || dataImported === 'true');
      }

      // Nếu badge đã có class bg-label-success, có nghĩa là đã được nhập
      if (statusBadge && statusBadge.classList.contains('bg-label-success')) {
        isImported = true;
      }

      // Cập nhật trạng thái
      updateItemStatus(itemId, isImported);
    });

    // Kiểm tra localStorage nếu có
    const storageKey = `good_receipt_${goodReceiptId}_import`;
    const savedData = JSON.parse(localStorage.getItem(storageKey) || '{}');

    // Cập nhật trạng thái các dòng sản phẩm từ localStorage
    for (const itemId in savedData) {
      updateItemStatus(itemId, true);
    }
  }
}

// Cập nhật trạng thái dòng sản phẩm
function updateItemStatus(itemId, hasSerials = true) {
  const row = document.querySelector(`tr[data-item-id="${itemId}"]`);
  if (!row) return;

  const statusBadge = row.querySelector('.item-status');
  if (!statusBadge) return;

  // Tìm nút nhập hàng trong dòng này (nếu có)
  const importButton = row.querySelector('.import-item-btn');

  // Lấy loại sản phẩm từ data attribute
  const trackingType = row.dataset.trackingType;

  if (hasSerials) {
    // Cập nhật trạng thái thành Đã nhập
    statusBadge.textContent = 'Đã nhập';
    statusBadge.classList.remove('bg-label-secondary');
    statusBadge.classList.add('bg-label-success');
    statusBadge.title = 'Sản phẩm đã được nhập vào kho';

    // Ẩn nút nhập hàng nếu có
    if (importButton) {
      // Ẩn nút nhập hàng cho tất cả các loại sản phẩm đã nhập
      importButton.style.display = 'none';
    }
  } else {
    // Cập nhật trạng thái thành Chưa nhập
    statusBadge.textContent = 'Chưa nhập';
    statusBadge.classList.remove('bg-label-success');
    statusBadge.classList.add('bg-label-secondary');
    statusBadge.title = 'Sản phẩm chưa được nhập vào kho';

    // Hiển thị lại nút nhập hàng nếu có
    if (importButton) {
      importButton.style.display = '';
    }
  }
}
