'use strict';

document.addEventListener('DOMContentLoaded', function (e) {
    $(function () {
        const vnptForm = document.getElementById('vnptForm');
        const vnptSubmitButton = vnptForm.querySelector('button[type="submit"]');
        $(document).ajaxStop($.unblockUI);
        FormValidation.formValidation(vnptForm, {
            fields: {
                publicServiceEndpoint: {
                    validators: {
                        callback: {
                            message: 'Public Service Endpoint không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnpt"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                portalServiceEndpoint: {
                    validators: {
                        callback: {
                            message: 'Portal Service Endpoint không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnpt"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                bizServiceEndpoint: {
                    validators: {
                        callback: {
                            message: 'Biz Service Endpoint không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnpt"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                vnptUsername: {
                    validators: {
                        callback: {
                            message: 'Tên đăng nhập không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnpt"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                vnptPassword: {
                    validators: {
                        callback: {
                            message: 'Mật khẩu không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnpt"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                vnptAccountName: {
                    validators: {
                        callback: {
                            message: 'Tên tài khoản không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnpt"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                vnptAccountPassword: {
                    validators: {
                        callback: {
                            message: 'Mật khẩu không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnpt"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                vnptPattern: {
                    validators: {
                        callback: {
                            message: 'Mẫu không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnpt"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                vnptSerial: {
                    validators: {
                        callback: {
                            message: 'Số Serial không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnpt"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                publicServiceEndpointSandbox: {
                    validators: {
                        callback: {
                            message: 'Public Service Endpoint không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnptSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                portalServiceEndpointSandbox: {
                    validators: {
                        callback: {
                            message: 'Portal Service Endpoint không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnptSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                bizServiceEndpointSandbox: {
                    validators: {
                        callback: {
                            message: 'Biz Service Endpoint không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnptSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                vnptUsernameSandbox: {
                    validators: {
                        callback: {
                            message: 'Tên đăng nhập không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnptSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                vnptPasswordSandbox: {
                    validators: {
                        callback: {
                            message: 'Mật khẩu không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnptSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                vnptAccountNameSandbox: {
                    validators: {
                        callback: {
                            message: 'Tên tài khoản không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnptSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                vnptAccountPasswordSandbox: {
                    validators: {
                        callback: {
                            message: 'Mật khẩu không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnptSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                vnptPatternSandbox: {
                    validators: {
                        callback: {
                            message: 'Mẫu không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnptSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                vnptSerialSandbox: {
                    validators: {
                        callback: {
                            message: 'Số Serial không được để trống',
                            callback: function (input) {
                                const isEnable = vnptForm.querySelector('[name="enableSettingsVnptSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                    // Use this for enabling/changing valid/invalid class
                    // eleInvalidClass: '',
                    eleValidClass: '',
                    rowSelector: '.col-md-6,.col-md-12'
                }),
                submitButton: new FormValidation.plugins.SubmitButton(),
                // Submit the form when all fields are valid
                // defaultSubmit: new FormValidation.plugins.DefaultSubmit(),
                autoFocus: new FormValidation.plugins.AutoFocus()
            }
        }).on('core.form.valid', function () {
            vnptSubmitButton.setAttribute('disabled', true);
            const formData = new FormData();
            console.log(vnptForm.querySelector('[name="enableSettingsVnpt"]').checked)
            if (vnptForm.querySelector('[name="enableSettingsVnpt"]').checked) {
                formData.append('vnpt_public_service_endpoint', vnptForm.querySelector('[name="publicServiceEndpoint"]').value);
                formData.append('vnpt_portal_service_endpoint', vnptForm.querySelector('[name="portalServiceEndpoint"]').value);
                formData.append('vnpt_biz_service_endpoint', vnptForm.querySelector('[name="bizServiceEndpoint"]').value);
                formData.append('vnpt_username', vnptForm.querySelector('[name="vnptUsername"]').value);
                formData.append('vnpt_password', vnptForm.querySelector('[name="vnptPassword"]').value);
                formData.append('vnpt_account_username', vnptForm.querySelector('[name="vnptAccountName"]').value);
                formData.append('vnpt_account_password', vnptForm.querySelector('[name="vnptAccountPassword"]').value);
                formData.append('vnpt_pattern', vnptForm.querySelector('[name="vnptPattern"]').value);
                formData.append('vnpt_serial', vnptForm.querySelector('[name="vnptSerial"]').value);
            }
            if (vnptForm.querySelector('[name="enableSettingsVnptSandbox"]').checked) {
                formData.append('vnpt_sandbox_public_service_endpoint', vnptForm.querySelector('[name="publicServiceEndpointSandbox"]').value);
                formData.append('vnpt_sandbox_portal_service_endpoint', vnptForm.querySelector('[name="portalServiceEndpointSandbox"]').value);
                formData.append('vnpt_sandbox_biz_service_endpoint', vnptForm.querySelector('[name="bizServiceEndpointSandbox"]').value);
                formData.append('vnpt_sandbox_username', vnptForm.querySelector('[name="vnptUsernameSandbox"]').value);
                formData.append('vnpt_sandbox_password', vnptForm.querySelector('[name="vnptPasswordSandbox"]').value);
                formData.append('vnpt_sandbox_account_username', vnptForm.querySelector('[name="vnptAccountNameSandbox"]').value);
                formData.append('vnpt_sandbox_account_password', vnptForm.querySelector('[name="vnptAccountPasswordSandbox"]').value);
                formData.append('vnpt_sandbox_pattern', vnptForm.querySelector('[name="vnptPatternSandbox"]').value);
                formData.append('vnpt_sandbox_serial', vnptForm.querySelector('[name="vnptSerialSandbox"]').value);
            }
            formData.append('vnpt_active', vnptForm.querySelector('[name="enableSettingsVnpt"]').checked ? 1 : 0);
            formData.append('vnpt_sandbox_active', vnptForm.querySelector('[name="enableSettingsVnptSandbox"]').checked ? 1 : 0);

            BlockUI()

            $.ajax({
                url: `${baseUrl}admin/settings/update/vnpt`,
                type: 'POST',
                data: formData,
                contentType: false, // Quan trọng cho việc tải file
                processData: false, // Không xử lý dữ liệu dưới dạng URL encoded
                success: function (resp) {
                    vnptSubmitButton.removeAttribute('disabled');
                    toastr[resp.status](resp.text, resp.title);
                    window.location.reload();
                },
                error: function (resp) {
                    vnptSubmitButton.removeAttribute('disabled');
                    toastr[resp.responseJSON.status](resp.responseJSON.message, resp.responseJSON.errors);
                }
            })
        })
    });
});

