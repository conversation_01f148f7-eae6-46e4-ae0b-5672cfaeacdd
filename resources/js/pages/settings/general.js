'use strict';

document.addEventListener('DOMContentLoaded', function (e) {
    $(function () {
        /*chọn hình ảnh*/
        selectImage($('#formFileLogo'), $('#logoImg'))
        selectImage($('#formFileFav'), $('#favImg'))

        const generalForm = document.getElementById('generalForm');
        const submitButton = generalForm.querySelector('button[type="submit"]');
        $(document).ajaxStop($.unblockUI);
        FormValidation.formValidation(generalForm, {
            fields: {
                siteName: {
                    validators: {
                        notEmpty: {
                            message: 'Tên trang web không được để trống'
                        },
                        stringLength: {
                            min: 3,
                            max: 50,
                            message: 'Tên trang web phải từ 3 đến 50 ký tự'
                        },
                        regexp: {
                            regexp: /^[a-zA-Z0-9 ]+$/,
                            message: 'Tên trang web chỉ chứa chữ cái và số'
                        }
                    }
                },
                siteUrl: {
                    validators: {
                        notEmpty: {
                            message: 'URL trang web không được để trống'
                        },
                        uri: {
                            message: 'URL trang web không đúng định dạng'
                        }
                    }
                },
                siteEmail: {
                    validators: {
                        emailAddress: {
                            message: 'Email không đúng định dạng'
                        }
                    }
                },
                siteMetaTitle: {
                    validators: {
                        stringLength: {
                            min: 3,
                            max: 50,
                            message: 'Tiêu đề trang web phải từ 3 đến 50 ký tự'
                        }
                    }
                },
                siteMetaDescription: {
                    validators: {
                        stringLength: {
                            min: 3,
                            max: 50,
                            message: 'Mô tả trang web phải từ 3 đến 50 ký tự'
                        }
                    }
                },
                formFileLogo: {
                    validators: {
                        file: {
                            extension: 'jpeg,jpg,png,webp,gif,svg',
                            type: 'image/jpeg,image/png,image/webp,image/gif,image/svg+xml',
                            maxSize: 2 * 1024 * 1024,
                            message: 'Vui lòng chọn file theo định dạng (jpeg, jpg, png, webp, gif, svg)'
                        }
                    }
                },
                formFileFav: {
                    validators: {
                        file: {
                            extension: 'jpeg,jpg,png,webp,gif,svg',
                            type: 'image/jpeg,image/png,image/webp,image/gif,image/svg+xml',
                            maxSize: 2 * 1024 * 1024,
                            message: 'Vui lòng chọn file theo định dạng (jpeg, jpg, png, webp, gif, svg)'
                        }
                    }
                },
                recaptchaSitekey: {
                    validators: {
                        callback: {
                            message: 'Recaptcha Site Key không được để trống',
                            callback: function (input) {
                                const isLoginChecked = generalForm.querySelector('[name="recaptchaLogin"]').checked;
                                const isRegisterChecked = generalForm.querySelector('[name="recaptchaRegister"]').checked;
                                if (isLoginChecked || isRegisterChecked) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                        regexp: {
                            regexp: /^[a-zA-Z0-9_-]+$/,
                            message: 'Recaptcha Site Key chỉ chứa chữ cái, số và dấu gạch dưới'
                        }
                    }
                },
                recaptchaSecretKey: {
                    validators: {
                        callback: {
                            message: 'Recaptcha Secret Key không được để trống',
                            callback: function (input) {
                                const isLoginChecked = generalForm.querySelector('[name="recaptchaLogin"]').checked;
                                const isRegisterChecked = generalForm.querySelector('[name="recaptchaRegister"]').checked;
                                if (isLoginChecked || isRegisterChecked) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                        regexp: {
                            regexp: /^[a-zA-Z0-9_-]+$/,
                            message: 'Recaptcha Secret Key chỉ chứa chữ cái, số và dấu gạch dưới'
                        }
                    }
                }
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                    // Use this for enabling/changing valid/invalid class
                    // eleInvalidClass: '',
                    eleValidClass: '',
                    rowSelector: '.col-md-6,.col-md-12'
                }),
                submitButton: new FormValidation.plugins.SubmitButton(),
                // Submit the form when all fields are valid
                // defaultSubmit: new FormValidation.plugins.DefaultSubmit(),
                autoFocus: new FormValidation.plugins.AutoFocus()
            }
        }).on('core.form.valid', function () {
            submitButton.setAttribute('disabled', true);
            const formData = new FormData();
            formData.append('site_name', generalForm.querySelector('[name="siteName"]').value);
            formData.append('site_url', generalForm.querySelector('[name="siteUrl"]').value);
            formData.append('site_email', generalForm.querySelector('[name="siteEmail"]').value);
            formData.append('site_meta_title', generalForm.querySelector('[name="siteMetaTitle"]').value);
            formData.append('site_meta_description', generalForm.querySelector('[name="siteMetaDescription"]').value);
            formData.append('recaptcha_login', generalForm.querySelector('[name="recaptchaLogin"]').checked ? 1 : 0);
            formData.append('recaptcha_register', generalForm.querySelector('[name="recaptchaRegister"]').checked ? 1 : 0);
            formData.append('recaptcha_sitekey', generalForm.querySelector('[name="recaptchaSitekey"]').value);
            formData.append('recaptcha_secretkey', generalForm.querySelector('[name="recaptchaSecretKey"]').value);
            formData.append('login_with_otp', generalForm.querySelector('[name="loginWithOtp"]').checked ? 1 : 0);

            var logoFiles = generalForm.querySelector('[name="formFileLogo"]').files;
            var faviconFiles = generalForm.querySelector('[name="formFileFav"]').files;

            if (logoFiles.length > 0) {
                formData.append('logo', logoFiles[0]);
            }
            if (faviconFiles.length > 0) {
                formData.append('favicon', faviconFiles[0]);
            }
            BlockUI()
            $.ajax({
                url: `${baseUrl}admin/settings/update/general`,
                type: 'POST',
                data: formData,
                contentType: false, // Quan trọng cho việc tải file
                processData: false, // Không xử lý dữ liệu dưới dạng URL encoded
                success: function (resp) {
                    submitButton.removeAttribute('disabled');
                    toastr[resp.status](resp.text, resp.title);
                    window.location.reload();
                },
                error: function (resp) {
                    submitButton.removeAttribute('disabled');
                    toastr[resp.responseJSON.status](resp.responseJSON.message, resp.responseJSON.errors);
                }
            })
        })
    });
});

function selectImage(element, elementImg) {
    element.on('change', function (event) {
        var file = event.target.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function (e) {
                elementImg.attr('src', e.target.result).show();
            }
            reader.readAsDataURL(file);
        }
    });
}
