'use strict';

document.addEventListener('DOMContentLoaded', function (e) {
    $(function () {
        const sapForm = document.getElementById('sapForm');
        const sapSubmitButton = sapForm.querySelector('button[type="submit"]');
        $(document).ajaxStop($.unblockUI);
        FormValidation.formValidation(sapForm, {
            fields: {
                sapServer: {
                    validators: {
                        callback: {
                            message: 'Sap Server không được để trống',
                            callback: function (input) {
                                const isEnable = sapForm.querySelector('[name="enableSettingsSap"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                        uri: {
                            message: 'Sap Server không đúng định dạng'
                        }
                    }
                },
                sapDB: {
                    validators: {
                        callback: {
                            message: 'Sap Database không được để trống',
                            callback: function (input) {
                                const isEnable = sapForm.querySelector('[name="enableSettingsSap"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                sapUser: {
                    validators: {
                        callback: {
                            message: 'Sap Username không được để trống',
                            callback: function (input) {
                                const isEnable = sapForm.querySelector('[name="enableSettingsSap"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                sapPassword: {
                    validators: {
                        callback: {
                            message: 'Sap Password không được để trống',
                            callback: function (input) {
                                const isEnable = sapForm.querySelector('[name="enableSettingsSap"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                sapServerSandbox: {
                    validators: {
                        callback: {
                            message: 'Sap Server không được để trống',
                            callback: function (input) {
                                const isEnable = sapForm.querySelector('[name="enableSettingsSapSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                        uri: {
                            message: 'Sap Server không đúng định dạng'
                        }
                    }
                },
                sapDBSandbox: {
                    validators: {
                        callback: {
                            message: 'Sap Database không được để trống',
                            callback: function (input) {
                                const isEnable = sapForm.querySelector('[name="enableSettingsSapSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                sapUserSandbox: {
                    validators: {
                        callback: {
                            message: 'Sap Username không được để trống',
                            callback: function (input) {
                                const isEnable = sapForm.querySelector('[name="enableSettingsSapSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                sapPasswordSandbox: {
                    validators: {
                        callback: {
                            message: 'Sap Password không được để trống',
                            callback: function (input) {
                                const isEnable = sapForm.querySelector('[name="enableSettingsSapSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                    // Use this for enabling/changing valid/invalid class
                    // eleInvalidClass: '',
                    eleValidClass: '',
                    rowSelector: '.col-md-6,.col-md-12'
                }),
                submitButton: new FormValidation.plugins.SubmitButton(),
                // Submit the form when all fields are valid
                // defaultSubmit: new FormValidation.plugins.DefaultSubmit(),
                autoFocus: new FormValidation.plugins.AutoFocus()
            }
        }).on('core.form.valid', function () {
            sapSubmitButton.setAttribute('disabled', true);
            const formData = new FormData();
            if (sapForm.querySelector('[name="enableSettingsSap"]').checked) {
                formData.append('sap_server', sapForm.querySelector('[name="sapServer"]').value);
                formData.append('sap_database', sapForm.querySelector('[name="sapDB"]').value);
                formData.append('sap_username', sapForm.querySelector('[name="sapUser"]').value);
                formData.append('sap_password', sapForm.querySelector('[name="sapPassword"]').value);

            }
            if (sapForm.querySelector('[name="enableSettingsSapSandbox"]').checked) {
                formData.append('sap_sandbox_server', sapForm.querySelector('[name="sapServerSandbox"]').value);
                formData.append('sap_sandbox_database', sapForm.querySelector('[name="sapDBSandbox"]').value);
                formData.append('sap_sandbox_username', sapForm.querySelector('[name="sapUserSandbox"]').value);
                formData.append('sap_sandbox_password', sapForm.querySelector('[name="sapPasswordSandbox"]').value);
            }
            formData.append('sap_active', sapForm.querySelector('[name="enableSettingsSap"]').checked ? 1 : 0);
            formData.append('sap_sandbox_active', sapForm.querySelector('[name="enableSettingsSapSandbox"]').checked ? 1 : 0);

            BlockUI()

            $.ajax({
                url: `${baseUrl}admin/settings/update/sap`,
                type: 'POST',
                data: formData,
                contentType: false, // Quan trọng cho việc tải file
                processData: false, // Không xử lý dữ liệu dưới dạng URL encoded
                success: function (resp) {
                    sapSubmitButton.removeAttribute('disabled');
                    toastr[resp.status](resp.text, resp.title);
                    window.location.reload();
                },
                error: function (resp) {
                    sapSubmitButton.removeAttribute('disabled');
                    toastr[resp.responseJSON.status](resp.responseJSON.message, resp.responseJSON.errors);
                }
            })
        })
    });
});

