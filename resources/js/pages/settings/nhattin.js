'use strict';

document.addEventListener('DOMContentLoaded', function (e) {
    $(function () {
        const nhattinForm = document.getElementById('nhattinForm');
        const nhattinSubmitButton = nhattinForm.querySelector('button[type="submit"]');
        $(document).ajaxStop($.unblockUI);
        FormValidation.formValidation(nhattinForm, {
            fields: {
                nhattinHost: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Host không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                        uri: {
                            message: 'Nhất Tín Host không đúng định dạng'
                        }
                    }
                },
                nhattinParnerId: {
                    validators: {
                        callback: {
                            message: '<PERSON><PERSON><PERSON><PERSON> T<PERSON> Parner Id không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                        number: {
                            message: 'Nhất Tín Parner Id không đúng định dạng'
                        }
                    }
                },
                nhattinUsername: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Username không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                nhattinPassword: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Password không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                nhattinServiceId: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Service Id không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                nhattinPaymentMethod: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Payment Method không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                nhattinCOD: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín COD không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                nhattinCargoType: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Cargo Type không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                nhattinHostSandbox: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Host không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattinSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                        uri: {
                            message: 'Nhất Tín Host không đúng định dạng'
                        }
                    }
                },
                nhattinParnerIdSandbox: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Parner Id không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattinSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                        number: {
                            message: 'Nhất Tín Parner Id không đúng định dạng'
                        }
                    }
                },
                nhattinUsernameSandbox: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Username không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattinSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                nhattinPasswordSandbox: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Password không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattinSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                nhattinServiceIdSandbox: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Service Id không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattinSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                nhattinPaymentMethodSandbox: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Payment Method không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattinSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                nhattinCODSandbox: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín COD không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattinSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        }
                    }
                },
                nhattinCargoTypeSandbox: {
                    validators: {
                        callback: {
                            message: 'Nhất Tín Cargo Type không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattinSandbox"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                nhattinSenderName: {
                    validators: {
                        callback: {
                            message: 'Tên Người Gửi không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                nhattinSenderPhone: {
                    validators: {
                        callback: {
                            message: 'SDT Người Gửi không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                nhattinSenderAddress: {
                    validators: {
                        callback: {
                            message: 'Địa Chỉ Người Gửi không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
                nhattinUfmSource: {
                    validators: {
                        callback: {
                            message: 'UfmSource không được để trống',
                            callback: function (input) {
                                const isEnable = nhattinForm.querySelector('[name="enableNhattin"]').checked;
                                if (isEnable) {
                                    return input.value.trim() !== '';
                                }
                                return true;
                            }
                        },
                    }
                },
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                    // Use this for enabling/changing valid/invalid class
                    // eleInvalidClass: '',
                    eleValidClass: '',
                    rowSelector: '.col-md-6,.col-md-12'
                }),
                submitButton: new FormValidation.plugins.SubmitButton(),
                // Submit the form when all fields are valid
                // defaultSubmit: new FormValidation.plugins.DefaultSubmit(),
                autoFocus: new FormValidation.plugins.AutoFocus()
            }
        }).on('core.form.valid', function () {
            nhattinSubmitButton.setAttribute('disabled', true);
            const formData = new FormData();
            if (nhattinForm.querySelector('[name="enableNhattin"]').checked) {
                formData.append('nhattin_host', nhattinForm.querySelector('[name="nhattinHost"]').value);
                formData.append('nhattin_parner_id', nhattinForm.querySelector('[name="nhattinParnerId"]').value);
                formData.append('nhattin_username', nhattinForm.querySelector('[name="nhattinUsername"]').value);
                formData.append('nhattin_password', nhattinForm.querySelector('[name="nhattinPassword"]').value);
                formData.append('nhattin_service_id', nhattinForm.querySelector('[name="nhattinServiceId"]').value);
                formData.append('nhattin_payment_method', nhattinForm.querySelector('[name="nhattinPaymentMethod"]').value);
                formData.append('nhattin_cod', nhattinForm.querySelector('[name="nhattinCOD"]').value);
                formData.append('nhattin_cargo_type', nhattinForm.querySelector('[name="nhattinCargoType"]').value);
                formData.append('nhattin_sender_name', nhattinForm.querySelector('[name="nhattinSenderName"]').value);
                formData.append('nhattin_sender_phone', nhattinForm.querySelector('[name="nhattinSenderPhone"]').value);
                formData.append('nhattin_sender_address', nhattinForm.querySelector('[name="nhattinSenderAddress"]').value);
                formData.append('nhattin_ufm_source', nhattinForm.querySelector('[name="nhattinUfmSource"]').value);
            }
            if (nhattinForm.querySelector('[name="enableNhattinSandbox"]').checked) {
                formData.append('nhattin_sandbox_host', nhattinForm.querySelector('[name="nhattinHostSandbox"]').value);
                formData.append('nhattin_sandbox_parner_id', nhattinForm.querySelector('[name="nhattinParnerIdSandbox"]').value);
                formData.append('nhattin_sandbox_username', nhattinForm.querySelector('[name="nhattinUsernameSandbox"]').value);
                formData.append('nhattin_sandbox_password', nhattinForm.querySelector('[name="nhattinPasswordSandbox"]').value);
                formData.append('nhattin_sandbox_service_id', nhattinForm.querySelector('[name="nhattinServiceIdSandbox"]').value);
                formData.append('nhattin_sandbox_payment_method', nhattinForm.querySelector('[name="nhattinPaymentMethodSandbox"]').value);
                formData.append('nhattin_sandbox_cod', nhattinForm.querySelector('[name="nhattinCODSandbox"]').value);
                formData.append('nhattin_sandbox_cargo_type', nhattinForm.querySelector('[name="nhattinCargoTypeSandbox"]').value);
            }
            formData.append('nhattin_active', nhattinForm.querySelector('[name="enableNhattin"]').checked ? 1 : 0);
            formData.append('nhattin_sandbox_active', nhattinForm.querySelector('[name="enableNhattinSandbox"]').checked ? 1 : 0);

            BlockUI()

            $.ajax({
                url: `${baseUrl}admin/settings/update/nhattin`,
                type: 'POST',
                data: formData,
                contentType: false, // Quan trọng cho việc tải file
                processData: false, // Không xử lý dữ liệu dưới dạng URL encoded
                success: function (resp) {
                    nhattinSubmitButton.removeAttribute('disabled');
                    toastr[resp.status](resp.text, resp.title);
                    window.location.reload();
                },
                error: function (resp) {
                    nhattinSubmitButton.removeAttribute('disabled');
                    toastr[resp.responseJSON.status](resp.responseJSON.message, resp.responseJSON.errors);
                }
            })
        })
    });
});

