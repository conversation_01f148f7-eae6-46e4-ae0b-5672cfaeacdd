/**
 * Product Settings
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
  // Form validation
  const productSettingsForm = document.getElementById('productSettingsForm');

  if (productSettingsForm) {
    const fv = FormValidation.formValidation(productSettingsForm, {
      fields: {
        product_default_form: {
          validators: {
            notEmpty: {
              message: 'Vui lòng chọn form mặc định'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-12'
        }),
        submitButton: new FormValidation.plugins.SubmitButton(),
        autoFocus: new FormValidation.plugins.AutoFocus()
      }
    }).on('core.form.valid', function () {
      // Get form data
      const formData = new FormData(productSettingsForm);

      // Show loading indicator
      showLoading();

      // Submit form data via AJAX
      $.ajax({
        url: '/admin/settings/product',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function (response) {
          // Hide loading indicator
          hideLoading();

          if (response.status === 'success') {
            Swal.fire({
              title: 'Thành công!',
              text: response.message,
              icon: 'success',
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });
          } else {
            Swal.fire({
              title: 'Lỗi!',
              text: response.message || 'Có lỗi xảy ra khi lưu cấu hình.',
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        },
        error: function (error) {
          // Hide loading indicator
          hideLoading();

          console.error('Error:', error);

          Swal.fire({
            title: 'Lỗi!',
            text: 'Có lỗi xảy ra khi lưu cấu hình.',
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-danger'
            }
          });
        }
      });
    });
  }
});
