/**
 * Product Management - Edit
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
    (function () {
        // Initialize Select2
        const categorySelect = $('#category_id');
        const brandSelect = $('#brand_id');

        if (categorySelect.length) {
            select2Focus(categorySelect);
            categorySelect.wrap('<div class="position-relative"></div>').select2({
                placeholder: 'Chọn danh mục',
                dropdownParent: categorySelect.parent()
            });
        }

        if (brandSelect.length) {
            select2Focus(brandSelect);
            brandSelect.wrap('<div class="position-relative"></div>').select2({
                placeholder: '<PERSON><PERSON>n thương hiệu',
                dropdownParent: brandSelect.parent()
            });
        }

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize Dropzone for product image
        let productImageDropzone;
        const dropzoneBasic = document.querySelector('#productImageUpload');
        const imageInput = document.querySelector('#image');

        if (dropzoneBasic) {
            // Đảm bảo Dropzone không tự động khởi tạo
            Dropzone.autoDiscover = false;

            productImageDropzone = new Dropzone(dropzoneBasic, {
                url: '/admin/products/upload-image',
                paramName: 'file',
                maxFiles: 1,
                maxFilesize: 2, // MB
                acceptedFiles: 'image/jpeg,image/png,image/gif,image/jpg,image/webp',
                addRemoveLinks: true,
                timeout: 60000, // 60 seconds timeout
                uploadMultiple: false,
                chunking: false,
                forceChunking: false,
                autoProcessQueue: true,
                createImageThumbnails: true,
                dictDefaultMessage: "Kéo thả hình ảnh vào đây hoặc click để tải lên",
                dictFallbackMessage: "Trình duyệt của bạn không hỗ trợ kéo thả file",
                dictFileTooBig: "File quá lớn ({{filesize}}MB). Kích thước tối đa: {{maxFilesize}}MB",
                dictInvalidFileType: "Bạn không thể tải lên loại file này",
                dictResponseError: "Máy chủ trả về mã {{statusCode}}",
                dictCancelUpload: "Hủy tải lên",
                dictUploadCanceled: "Đã hủy tải lên",
                dictRemoveFile: "Xóa file",
                dictMaxFilesExceeded: "Bạn không thể tải lên thêm file",
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                init: function () {
                    const dropzone = this;

                    // Kiểm tra nếu sản phẩm đã có hình ảnh, hiển thị nó trong dropzone
                    const existingImage = document.querySelector('.card-body img.img-fluid');
                    if (existingImage) {
                        const imgSrc = existingImage.getAttribute('src');
                        if (imgSrc) {
                            // Tạo file giả từ URL hình ảnh hiện tại
                            const fileName = imgSrc.split('/').pop();
                            const existingFile = {name: fileName, size: 12345, accepted: true};

                            // Thêm file vào dropzone và hiển thị hình ảnh
                            dropzone.displayExistingFile(existingFile, imgSrc);

                            // Đặt giá trị cho input ẩn
                            const currentImagePath = document.querySelector('input[name="image"]').value || existingImage.getAttribute('data-image-path');
                            if (currentImagePath) {
                                imageInput.value = currentImagePath;
                            }

                            // Lưu file hiện tại vào một biến để có thể tham chiếu sau này
                            dropzone.existingFile = existingFile;
                        }
                    }

                    // Xử lý khi thêm file mới
                    this.on('addedfile', function(file) {
                        console.log('File added:', file.name, 'Total files:', this.files.length);

                        // Nếu đã có file hiện tại và file mới không phải là file hiện tại
                        if (this.files.length > 1) {
                            console.log('Multiple files detected, removing others');
                            // Xóa tất cả các file khác
                            const filesToRemove = [];

                            this.files.forEach((existingFile) => {
                                if (existingFile !== file) {
                                    filesToRemove.push(existingFile);
                                }
                            });

                            // Xóa các file trong một vòng lặp riêng để tránh lỗi
                            filesToRemove.forEach((fileToRemove) => {
                                try {
                                    this.removeFile(fileToRemove);
                                } catch (e) {
                                    console.error('Error removing file:', e);
                                }
                            });
                        }
                    });

                    // Thêm biến để theo dõi trạng thái tải lên
                    this.uploadSuccessful = false;

                    this.on('success', function (file, response) {
                        console.log('Upload success response:', response);
                        if (response && response.success) {
                            // Đánh dấu file đã được tải lên thành công
                            file.uploadSuccessful = true;
                            this.uploadSuccessful = true;

                            // Lưu đường dẫn ảnh vào input ẩn
                            imageInput.value = response.path;

                            // Thêm thuộc tính để nhận diện file đã tải lên thành công
                            file.previewElement.classList.add('dz-success');

                            // Hiển thị thông báo thành công
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: 'Hình ảnh đã được tải lên thành công',
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                },
                                timer: 1500,
                                showConfirmButton: false
                            });
                        } else {
                            // Đánh dấu file tải lên thất bại
                            file.uploadSuccessful = false;

                            // Xóa file khỏi dropzone
                            try {
                                this.removeFile(file);
                            } catch (e) {
                                console.error('Error removing file after failed upload:', e);
                            }

                            // Hiển thị thông báo lỗi
                            Swal.fire({
                                icon: 'error',
                                title: 'Lỗi!',
                                text: response.message || 'Có lỗi xảy ra khi tải lên hình ảnh',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        }
                    });

                    this.on('error', function (file, errorMessage, xhr) {
                        console.error('Upload error:', file, errorMessage, xhr);

                        // Kiểm tra nếu file đã được tải lên thành công trước đó
                        if (file.uploadSuccessful || this.uploadSuccessful) {
                            console.log('Ignoring error for successfully uploaded file');
                            return; // Không xử lý lỗi nếu file đã được tải lên thành công
                        }

                        // Xóa file khỏi dropzone
                        try {
                            this.removeFile(file);
                        } catch (e) {
                            console.error('Error removing file:', e);
                        }

                        let errorText = 'Có lỗi xảy ra khi tải lên hình ảnh';

                        if (typeof errorMessage === 'string') {
                            errorText = errorMessage;
                        } else if (errorMessage && errorMessage.message) {
                            errorText = errorMessage.message;
                        } else if (xhr && xhr.status) {
                            errorText = `Lỗi HTTP ${xhr.status}: ${xhr.statusText || 'Không xác định'}`;
                        }

                        // Kiểm tra nếu là lỗi MIME type
                        if (file && file.type && this.options.acceptedFiles) {
                            const acceptedTypes = this.options.acceptedFiles.split(',');
                            if (acceptedTypes.length > 0 && !acceptedTypes.includes(file.type)) {
                                errorText = `Loại file ${file.type} không được hỗ trợ. Chỉ chấp nhận các loại file: ${this.options.acceptedFiles}`;
                            }
                        }

                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi tải lên!',
                            text: errorText,
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    });

                    this.on('removedfile', function (file) {
                        console.log('File removed:', file);

                        // Nếu file đã được tải lên thành công, không xóa giá trị input
                        if (file.uploadSuccessful) {
                            console.log('Not clearing input value for successfully uploaded file');
                            return;
                        }

                        // Nếu xóa file, chỉ xóa giá trị input nếu đó là file mới tải lên
                        // Nếu là file hiện tại, giữ nguyên giá trị để tránh mất hình ảnh khi không tải lên hình mới
                        if (!existingImage || dropzone.files.length === 0) {
                            console.log('Clearing input value');
                            imageInput.value = '';
                        }
                    });

                    // Xử lý khi hoàn thành tải lên (thành công hoặc thất bại)
                    this.on('complete', function(file) {
                        console.log('Upload complete for file:', file.name, 'Status:', file.status);

                        // Nếu file đã được tải lên thành công (status = 'success')
                        if (file.status === 'success' && !file.uploadSuccessful) {
                            // Đánh dấu file đã được tải lên thành công
                            file.uploadSuccessful = true;
                        }
                    });

                    // Giới hạn số lượng file
                    this.on('maxfilesexceeded', function (file) {
                        console.log('Max files exceeded, removing all files and adding new one');
                        // Xóa tất cả các file hiện tại
                        try {
                            this.removeAllFiles(true); // true = cancel uploads
                        } catch (e) {
                            console.error('Error removing all files:', e);
                        }

                        // Thêm file mới sau một khoảng thời gian ngắn
                        setTimeout(() => {
                            try {
                                this.addFile(file);
                            } catch (e) {
                                console.error('Error adding file after removeAllFiles:', e);
                                // Thử lại với cách khác
                                this.files = [];
                                this.addFile(file);
                            }
                        }, 100);
                    });
                }
            });
        }

        // Form validation
        const productForm = document.getElementById('productForm');

        if (productForm) {
            // Define event handlers separately to avoid issues with split function
            const eventHandlers = {
                name: 'input',
                code: 'input',
                description: 'input',
                category_id: 'change',
                brand_id: 'change',
                unit: 'input',
                price: 'input',
                cost: 'input',
                tax: 'input',
                is_active: 'change',
                barcode: 'input',
                weight: 'input',
                length: 'input',
                width: 'input',
                height: 'input',
                inventory_tracking_type: 'change'
            };

            const fv = FormValidation.formValidation(productForm, {
                fields: {
                    name: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập tên sản phẩm'
                            },
                            stringLength: {
                                min: 3,
                                max: 255,
                                message: 'Tên sản phẩm phải có từ 3 đến 255 ký tự'
                            }
                        }
                    },
                    code: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập mã sản phẩm'
                            },
                            stringLength: {
                                min: 2,
                                max: 50,
                                message: 'Mã sản phẩm phải có từ 2 đến 50 ký tự'
                            },
                            regexp: {
                                regexp: /^[a-zA-Z0-9-_]+$/,
                                message: 'Mã sản phẩm chỉ được chứa chữ cái, số, dấu gạch ngang và gạch dưới'
                            }
                        }
                    },
                    category_id: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng chọn danh mục'
                            }
                        }
                    },
                    brand_id: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng chọn thương hiệu'
                            }
                        }
                    },
                    price: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập giá bán'
                            },
                            numeric: {
                                message: 'Giá bán phải là số'
                            },
                            greaterThanOrEqual: {
                                value: 0,
                                message: 'Giá bán không được âm'
                            }
                        }
                    },
                    cost: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập giá vốn'
                            },
                            numeric: {
                                message: 'Giá vốn phải là số'
                            },
                            greaterThanOrEqual: {
                                value: 0,
                                message: 'Giá vốn không được âm'
                            }
                        }
                    },
                    tax: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng chọn thuế'
                            },
                            numeric: {
                                message: 'Thuế phải là số'
                            },
                            between: {
                                min: 0,
                                max: 100,
                                message: 'Thuế phải từ 0 đến 100%'
                            }
                        }
                    },
                    weight: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập trọng lượng'
                            },
                            numeric: {
                                message: 'Trọng lượng phải là số'
                            },
                            greaterThanOrEqual: {
                                value: 0,
                                message: 'Trọng lượng không được âm'
                            }
                        }
                    },
                    length: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập chiều dài'
                            },
                            numeric: {
                                message: 'Chiều dài phải là số'
                            },
                            greaterThanOrEqual: {
                                value: 0,
                                message: 'Chiều dài không được âm'
                            }
                        }
                    },
                    width: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập chiều rộng'
                            },
                            numeric: {
                                message: 'Chiều rộng phải là số'
                            },
                            greaterThanOrEqual: {
                                value: 0,
                                message: 'Chiều rộng không được âm'
                            }
                        }
                    },
                    height: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập chiều cao'
                            },
                            numeric: {
                                message: 'Chiều cao phải là số'
                            },
                            greaterThanOrEqual: {
                                value: 0,
                                message: 'Chiều cao không được âm'
                            }
                        }
                    },
                    inventory_tracking_type: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng chọn phương thức quản lý kho'
                            }
                        }
                    }
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger({
                        event: eventHandlers,
                        // Use string instead of object to avoid split function issues
                        delay: {
                            name: 500,
                            code: 500,
                            description: 500,
                            price: 500,
                            cost: 500,
                            tax: 500
                        }
                    }),
                    bootstrap5: new FormValidation.plugins.Bootstrap5({
                        eleValidClass: '',
                        rowSelector: '.col-md-6, .col-md-12, .col-md-4, .col-md-3'
                    }),
                    submitButton: new FormValidation.plugins.SubmitButton(),
                    autoFocus: new FormValidation.plugins.AutoFocus()
                }
            }).on('core.form.valid', function () {
                // Get form data
                const formData = new FormData(productForm);
                const productId = formData.get('id');

                // Add is_active value
                formData.set('is_active', productForm.querySelector('#is_active').checked ? 1 : 0);

                // Add _method for Laravel to recognize as PATCH request
                formData.append('_method', 'PATCH');

                // Show loading indicator
                const loadingOverlay = document.createElement('div');
                loadingOverlay.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-dark bg-opacity-50';
                loadingOverlay.style.zIndex = '9999';
                loadingOverlay.innerHTML = `
          <div class="bg-white p-4 rounded shadow">
            <div class="spinner-border text-primary mb-2" role="status"></div>
            <p class="mb-0 text-center">Đang cập nhật sản phẩm...</p>
          </div>
        `;
                document.body.appendChild(loadingOverlay);

                // Set a timeout to prevent hanging
                const timeoutId = setTimeout(() => {
                    document.body.removeChild(loadingOverlay);
                    Swal.fire({
                        title: 'Thông báo',
                        text: 'Cập nhật sản phẩm đang mất nhiều thời gian. Sản phẩm có thể đã được cập nhật. Vui lòng kiểm tra lại.',
                        icon: 'warning',
                        customClass: {
                            confirmButton: 'btn btn-primary'
                        }
                    }).then(function () {
                        window.location.href = '/admin/products';
                    });
                }, 25000); // 25 second timeout

                // Submit form data via AJAX
                $.ajax({
                    type: 'POST',
                    url: `/admin/products/update/${productId}`,
                    data: formData,
                    processData: false,
                    contentType: false,
                    timeout: 30000, // 30 second timeout
                    success: function (response) {
                        clearTimeout(timeoutId);
                        document.body.removeChild(loadingOverlay);

                        console.log('Response:', response); // Debug log

                        // Check if response is a string (possibly a redirect)
                        if (typeof response === 'string' && response.includes('<html')) {
                            // Server likely returned a redirect or HTML page
                            window.location.href = '/admin/products';
                            return;
                        }

                        if (response.title === 'success' || response.status === 'success') {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.text || 'Sản phẩm đã được cập nhật thành công',
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function () {
                                window.location.href = '/admin/products';
                            });
                        } else {
                            // Nếu dữ liệu đã được lưu nhưng có lỗi, vẫn chuyển hướng người dùng
                            Swal.fire({
                                title: 'Thông báo',
                                text: response.text || 'Sản phẩm đã được cập nhật nhưng có thể có một số vấn đề',
                                icon: 'info',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            }).then(function () {
                                window.location.href = '/admin/products';
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        clearTimeout(timeoutId);
                        document.body.removeChild(loadingOverlay);

                        console.log('Error:', xhr, status, error); // Debug log

                        // Kiểm tra nếu status là 200 hoặc 201, có thể dữ liệu đã được lưu
                        if (xhr.status === 200 || xhr.status === 201) {
                            Swal.fire({
                                title: 'Thông báo',
                                text: 'Sản phẩm có thể đã được cập nhật. Vui lòng kiểm tra lại danh sách sản phẩm.',
                                icon: 'info',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            }).then(function () {
                                window.location.href = '/admin/products';
                            });
                            return;
                        }

                        let errorMessage = 'Có lỗi xảy ra khi cập nhật sản phẩm. Vui lòng thử lại sau.';

                        if (status === 'timeout') {
                            errorMessage = 'Quá thời gian kết nối. Sản phẩm có thể đã được cập nhật. Vui lòng kiểm tra lại.';
                            Swal.fire({
                                title: 'Thông báo',
                                text: errorMessage,
                                icon: 'warning',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            }).then(function () {
                                window.location.href = '/admin/products';
                            });
                            return;
                        }

                        if (xhr.responseJSON && xhr.responseJSON.errors) {
                            errorMessage = Object.values(xhr.responseJSON.errors).flat().join('<br>');
                        } else if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            title: 'Lỗi!',
                            html: errorMessage,
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            });
        }
        // Product Attributes Handling
        const productAttributesContainer = document.getElementById('productAttributesContainer');
        const attributesList = document.getElementById('attributesList');
        const loadingAttributes = document.getElementById('loadingAttributes');
        const noAttributesMessage = document.getElementById('noAttributesMessage');

        let attributes = [];
        let productAttributes = [];

        // Load attributes when page loads
        loadAttributes();

        // Load attributes from API
        function loadAttributes() {
            // Check if elements exist
            if (!productAttributesContainer || !attributesList || !loadingAttributes || !noAttributesMessage) {
                return;
            }

            // Show loading indicator
            loadingAttributes.style.display = 'block';
            attributesList.style.display = 'none';
            noAttributesMessage.style.display = 'none';

            // Lấy ID sản phẩm từ input ẩn
            const productId = document.querySelector('input[name="id"]').value;

            // Lấy danh sách thuộc tính và giá trị hiện tại của sản phẩm
            $.ajax({
                url: '/admin/api/product-attributes/available',
                type: 'GET',
                dataType: 'json',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function (response) {
                    // Hide loading indicator
                    loadingAttributes.style.display = 'none';

                    if (response.status && response.data && response.data.length > 0) {
                        attributes = response.data;

                        // Lấy các thuộc tính hiện tại của sản phẩm
                        $.ajax({
                            url: `/admin/api/products/${productId}/attributes`,
                            type: 'GET',
                            dataType: 'json',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function (productAttributesResponse) {
                                if (productAttributesResponse.status && productAttributesResponse.data) {
                                    // Lưu các thuộc tính hiện tại của sản phẩm
                                    productAttributes = productAttributesResponse.data;
                                    console.log('Product attributes loaded:', productAttributes);
                                }

                                // Tiếp tục hiển thị các thuộc tính
                                renderAttributes();
                            },
                            error: function (xhr, status, error) {
                                console.error('Error loading product attributes:', error);
                                // Nếu không lấy được thuộc tính hiện tại, vẫn hiển thị form
                                renderAttributes();
                            }
                        });
                    } else {
                        // No attributes found
                        noAttributesMessage.style.display = 'block';
                    }
                },
                error: function (xhr, status, error) {
                    // Hide loading indicator and show error
                    loadingAttributes.style.display = 'none';
                    noAttributesMessage.style.display = 'block';

                    // Try to parse the response
                    let errorMessage = 'Không thể tải danh sách thuộc tính. Vui lòng thử lại sau.';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        // Parsing error
                    }

                    Swal.fire({
                        title: 'Lỗi!',
                        text: errorMessage,
                        icon: 'error',
                        customClass: {
                            confirmButton: 'btn btn-primary'
                        }
                    });
                }
            });
        }

        // Render attributes with product's current values
        function renderAttributes() {
            // Generate HTML for all attributes
            let attributesHtml = '';

            // Filter attributes by type
            const textAttributes = attributes.filter(attr => attr.type === 'text');
            const numberAttributes = attributes.filter(attr => attr.type === 'number');
            const dateAttributes = attributes.filter(attr => attr.type === 'date');
            const booleanAttributes = attributes.filter(attr => attr.type === 'boolean');
            const selectAttributes = attributes.filter(attr => attr.type === 'select' && attr.active_values && attr.active_values.length > 0);

            // Process all attributes
            attributes.forEach(attribute => {
                // For select attributes, only show if they have values
                if (attribute.type === 'select' && (!attribute.active_values || attribute.active_values.length === 0)) {
                    return;
                }

                const html = generateAttributeHtml(attribute);
                if (html) {
                    attributesHtml += html;
                }
            });

            if (attributesHtml) {
                attributesList.innerHTML = attributesHtml;
                attributesList.style.display = 'flex';

                // Set current values for attributes
                setAttributeValues();

                // Add event listeners to attribute inputs
                setupAttributeEventListeners();
            } else {
                // No attributes with values
                noAttributesMessage.querySelector('h6').textContent = 'Không có thuộc tính nào có giá trị';
                noAttributesMessage.querySelector('p').textContent = 'Các thuộc tính hiện tại chưa có giá trị nào. Vui lòng thêm giá trị cho thuộc tính trước khi sử dụng.';
                noAttributesMessage.style.display = 'block';
            }
        }

        // Set current values for attributes
        function setAttributeValues() {
            if (!productAttributes || productAttributes.length === 0) {
                return;
            }

            productAttributes.forEach(attr => {
                const attributeId = attr.attribute_id;
                const attributeType = attr.attribute_type;
                const attributeValueId = attr.attribute_value_id;
                const textValue = attr.text_value;

                // Tìm input tương ứng
                const input = document.querySelector(`[data-attribute-id="${attributeId}"]`);
                if (!input) return;

                // Đặt giá trị
                if (attributeType === 'select' && attributeValueId) {
                    input.value = attributeValueId;
                } else if (attributeType === 'boolean') {
                    input.checked = textValue === '1';
                } else if (textValue) {
                    input.value = textValue;
                }
            });
        }

        // Generate HTML for an attribute
        function generateAttributeHtml(attribute) {
            let html = '';

            // Skip select attributes without values
            if (attribute.type === 'select' && (!attribute.active_values || attribute.active_values.length === 0)) {
                return '';
            }

            html += `<div class="col-md-4 mb-3">
        <div class="card h-100">
          <div class="card-header">
            <h6 class="mb-0">${attribute.name}</h6>
          </div>
          <div class="card-body">`;

            // Generate input based on attribute type
            switch (attribute.type) {
                case 'text':
                    html += `
            <div class="form-floating form-floating-outline mb-2">
              <input type="text" class="form-control attribute-input"
                data-attribute-id="${attribute.id}"
                data-attribute-type="${attribute.type}"
                id="attribute_${attribute.id}"
                placeholder="Nhập ${attribute.name}">
              <label for="attribute_${attribute.id}">Giá trị</label>
            </div>`;
                    break;

                case 'number':
                    html += `
            <div class="form-floating form-floating-outline mb-2">
              <input type="number" class="form-control attribute-input"
                data-attribute-id="${attribute.id}"
                data-attribute-type="${attribute.type}"
                id="attribute_${attribute.id}"
                placeholder="Nhập ${attribute.name}">
              <label for="attribute_${attribute.id}">Giá trị</label>
            </div>`;
                    break;

                case 'date':
                    html += `
            <div class="form-floating form-floating-outline mb-2">
              <input type="date" class="form-control attribute-input"
                data-attribute-id="${attribute.id}"
                data-attribute-type="${attribute.type}"
                id="attribute_${attribute.id}">
              <label for="attribute_${attribute.id}">Giá trị</label>
            </div>`;
                    break;

                case 'boolean':
                    html += `
            <div class="form-check form-switch mb-2">
              <input class="form-check-input attribute-input"
                type="checkbox"
                data-attribute-id="${attribute.id}"
                data-attribute-type="${attribute.type}"
                id="attribute_${attribute.id}">
              <label class="form-check-label" for="attribute_${attribute.id}">Có/Không</label>
            </div>`;
                    break;

                case 'select':
                    if (attribute.active_values && attribute.active_values.length > 0) {
                        html += `
              <div class="form-floating form-floating-outline mb-2">
                <select class="form-select attribute-input"
                  data-attribute-id="${attribute.id}"
                  data-attribute-type="${attribute.type}"
                  id="attribute_${attribute.id}">
                  <option value="">Chọn giá trị</option>`;

                        attribute.active_values.forEach(value => {
                            html += `<option value="${value.id}">${value.display_value || value.value}</option>`;
                        });

                        html += `
                </select>
                <label for="attribute_${attribute.id}">Giá trị</label>
              </div>`;
                    } else {
                        return ''; // Skip if no values
                    }
                    break;

                default:
                    return ''; // Skip unknown types
            }

            html += `
          </div>
        </div>
      </div>`;

            return html;
        }

        // Setup event listeners for attribute inputs
        function setupAttributeEventListeners() {
            const attributeInputs = document.querySelectorAll('.attribute-input');

            attributeInputs.forEach(input => {
                const attributeId = input.dataset.attributeId;
                const attributeType = input.dataset.attributeType;

                // Add event listener based on input type
                if (attributeType === 'select') {
                    input.addEventListener('change', function () {
                        updateProductAttribute(attributeId, attributeType, this.value);
                    });
                } else if (attributeType === 'boolean') {
                    input.addEventListener('change', function () {
                        updateProductAttribute(attributeId, attributeType, this.checked ? '1' : '0');
                    });
                } else {
                    input.addEventListener('input', function () {
                        updateProductAttribute(attributeId, attributeType, this.value);
                    });
                }
            });
        }

        // Update product attribute in the productAttributes array
        function updateProductAttribute(attributeId, attributeType, value) {
            // Find existing attribute
            const existingIndex = productAttributes.findIndex(attr => attr.attribute_id === attributeId);

            if (value) {
                // If value exists, update or add
                if (existingIndex !== -1) {
                    // Update existing
                    if (attributeType === 'select') {
                        productAttributes[existingIndex].attribute_value_id = value;
                        delete productAttributes[existingIndex].text_value;
                    } else {
                        productAttributes[existingIndex].text_value = value;
                        delete productAttributes[existingIndex].attribute_value_id;
                    }
                } else {
                    // Add new
                    const newAttribute = {
                        attribute_id: attributeId
                    };

                    if (attributeType === 'select') {
                        newAttribute.attribute_value_id = value;
                    } else {
                        newAttribute.text_value = value;
                    }

                    productAttributes.push(newAttribute);
                }
            } else {
                // If no value, remove if exists
                if (existingIndex !== -1) {
                    productAttributes.splice(existingIndex, 1);
                }
            }

            // For debugging
            console.log('Updated product attributes:', productAttributes);
        }

        // Helper function to show loading indicator
        function showLoading() {
            const loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'loadingOverlay';
            loadingOverlay.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-dark bg-opacity-50';
            loadingOverlay.style.zIndex = '9999';
            loadingOverlay.innerHTML = `
        <div class="bg-white p-4 rounded shadow">
          <div class="spinner-border text-primary mb-2" role="status"></div>
          <p class="mb-0 text-center">Đang xử lý...</p>
        </div>
      `;
            document.body.appendChild(loadingOverlay);
        }

        // Helper function to hide loading indicator
        function hideLoading() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                document.body.removeChild(loadingOverlay);
            }
        }

        // Update form submission to include product attributes
        if (productForm) {
            const formValidation = productForm.querySelector('.btn-primary');
            if (formValidation) {
                formValidation.addEventListener('click', function () {
                    // Add product attributes to form data before submission
                    const formData = new FormData(productForm);

                    // Add each product attribute to the form data
                    productAttributes.forEach((attr, index) => {
                        if (attr.attribute_id) {
                            formData.append(`attributes[${index}][attribute_id]`, attr.attribute_id);

                            if (attr.attribute_value_id) {
                                formData.append(`attributes[${index}][attribute_value_id]`, attr.attribute_value_id);
                            } else if (attr.text_value) {
                                formData.append(`attributes[${index}][text_value]`, attr.text_value);
                            }
                        }
                    });
                });
            }
        }

    })();
});
