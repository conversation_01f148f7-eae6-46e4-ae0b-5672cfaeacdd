/**
 * Category Management - Index
 */

'use strict';

// Datatable (jquery)
$(function () {
    // Filters
    const parentFilter = document.getElementById('parent-filter');
    const statusFilter = document.getElementById('status-filter');

    // Categories datatable
    if ($('.datatables-categories').length) {
        var dt_categories = $('.datatables-categories').DataTable({
            ajax: {
                url: '/admin/categories/datatable',
                type: 'POST',
                data: function (d) {
                    d.parent_id = parentFilter.value;
                    d.status = statusFilter.value;
                    d._token = $('meta[name="csrf-token"]').attr('content');
                }
            },
            columns: [
                {data: ''}, // Responsive Control column
                {data: 'name'},
                {data: 'parent'},
                {data: 'description'},
                {data: 'is_active'},
                {data: 'actions'}
            ],
            columnDefs: [
                {
                    // For Responsive
                    className: 'control',
                    orderable: false,
                    searchable: false,
                    responsivePriority: 2,
                    targets: 0,
                    render: function (data, type, full, meta) {
                        return '';
                    }
                },
                {
                    // Category Name
                    targets: 1,
                    responsivePriority: 1,
                    render: function (data, type, full, meta) {
                        return `<a href="/admin/categories/edit/${full.id}" class="fw-medium">${data}</a>`;
                    }
                },
                {
                    // Parent Category
                    targets: 2,
                    render: function (data, type, full, meta) {
                        return data;
                    }
                },
                {
                    // Description
                    targets: 3,
                    render: function (data, type, full, meta) {
                        return data ? data : '<span class="text-muted">Không có mô tả</span>';
                    }
                },
                {
                    // Status
                    targets: 4,
                    render: function (data, type, full, meta) {
                        const status = data ? 'Đang hoạt động' : 'Ngừng hoạt động';
                        const color = data ? 'success' : 'danger';
                        return `<span class="badge bg-label-${color}">${status}</span>`;
                    }
                },
                {
                    // Actions
                    targets: -1,
                    title: 'Thao tác',
                    searchable: false,
                    orderable: false,
                    render: function (data, type, full, meta) {
                        return `
              <div class="d-flex align-items-center">
                <a href="/admin/categories/edit/${full.id}" class="btn btn-sm btn-icon" data-bs-toggle="tooltip" data-bs-placement="top" title="Chỉnh sửa">
                  <i class="ri-edit-line"></i>
                </a>
                <button class="btn btn-sm btn-icon delete-record" data-id="${full.id}" data-bs-toggle="tooltip" data-bs-placement="top" title="Xóa">
                  <i class="ri-delete-bin-line"></i>
                </button>
              </div>`;
                    }
                }
            ],
            order: [[1, 'asc']], // Order by name
            dom: '<"card-header d-flex flex-column flex-md-row"<"head-label text-center">f>t<"row mx-2"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
            displayLength: 10,
            lengthMenu: [10, 25, 50, 75, 100],
            responsive: {
                details: {
                    display: $.fn.dataTable.Responsive.display.modal({
                        header: function (row) {
                            var data = row.data();
                            return 'Chi tiết danh mục ' + data.name;
                        }
                    }),
                    type: 'column',
                    renderer: function (api, rowIdx, columns) {
                        var data = $.map(columns, function (col, i) {
                            return col.title !== '' // ? Do not show row in modal popup if title is blank (for check box)
                                ? '<tr data-dt-row="' +
                                col.rowIndex +
                                '" data-dt-column="' +
                                col.columnIndex +
                                '">' +
                                '<td>' +
                                col.title +
                                ':' +
                                '</td> ' +
                                '<td>' +
                                col.data +
                                '</td>' +
                                '</tr>'
                                : '';
                        }).join('');

                        return data ? $('<table class="table"/><tbody />').append(data) : false;
                    }
                }
            }
        });
    }

    // Filter form control to default size
    $('.dataTables_filter .form-control').removeClass('form-control-sm');
    $('.dataTables_length .form-select').removeClass('form-select-sm');

    // Filter events
    if (parentFilter) {
        parentFilter.addEventListener('change', function () {
            dt_categories.ajax.reload();
        });
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', function () {
            dt_categories.ajax.reload();
        });
    }

    // Delete Record
    $('.datatables-categories tbody').on('click', '.delete-record', function () {
        var category_id = $(this).data('id');

        Swal.fire({
            title: 'Xác nhận xóa?',
            text: "Bạn có chắc chắn muốn xóa danh mục này không?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-danger me-3',
                cancelButton: 'btn btn-label-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.value) {
                // Delete record
                $.ajax({
                    type: 'DELETE',
                    url: `/admin/categories/delete/${category_id}`,
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        if (response.code === 200) {
                            dt_categories.ajax.reload();
                            Swal.fire({
                                icon: 'success',
                                title: 'Đã xóa!',
                                text: response.text,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Lỗi!',
                                text: response.text,
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        }
                    },
                    error: function (error) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: 'Không thể xóa danh mục. Vui lòng thử lại sau.',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            }
        });
    });
});
