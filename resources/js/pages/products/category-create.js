/**
 * Category Management - Create
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
    (function () {
        // Initialize Select2
        const parentSelect = $('#parent_id');

        if (parentSelect.length) {
            select2Focus(parentSelect);
            parentSelect.wrap('<div class="position-relative"></div>').select2({
                placeholder: 'Chọn danh mục cha',
                dropdownParent: parentSelect.parent()
            });
        }

        // Form validation
        const categoryForm = document.getElementById('categoryForm');

        if (categoryForm) {
            const fv = FormValidation.formValidation(categoryForm, {
                fields: {
                    name: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập tên danh mục'
                            },
                            stringLength: {
                                min: 2,
                                max: 255,
                                message: 'Tên danh mục phải có từ 2 đến 255 ký tự'
                            }
                        }
                    }
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap5: new FormValidation.plugins.Bootstrap5({
                        eleValidClass: '',
                        rowSelector: '.col-md-6, .col-md-12'
                    }),
                    submitButton: new FormValidation.plugins.SubmitButton(),
                    autoFocus: new FormValidation.plugins.AutoFocus()
                }
            }).on('core.form.valid', function () {
                // Get form data
                const formData = {
                    name: categoryForm.querySelector('#name').value,
                    parent_id: categoryForm.querySelector('#parent_id').value || null,
                    description: categoryForm.querySelector('#description').value,
                    is_active: categoryForm.querySelector('#is_active').checked ? 1 : 0,
                    _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                };

                // Submit form data via AJAX
                $.ajax({
                    type: 'POST',
                    url: '/admin/categories/store',
                    data: formData,
                    success: function (response) {
                        console.log('Response:', response); // Debug log
                        if (response.title === 'success') {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.text,
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function () {
                                if (response.redirect) {
                                    window.location.href = response.redirect;
                                } else {
                                    window.location.href = '/admin/categories';
                                }
                            });
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.text,
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        }
                    },
                    error: function (error) {
                        console.log('Error:', error); // Debug log
                        let errorMessage = 'Có lỗi xảy ra khi tạo danh mục. Vui lòng thử lại sau.';

                        if (error.responseJSON && error.responseJSON.errors) {
                            errorMessage = Object.values(error.responseJSON.errors).flat().join('<br>');
                        } else if (error.responseJSON && error.responseJSON.text) {
                            errorMessage = error.responseJSON.text;
                        }

                        Swal.fire({
                            title: 'Lỗi!',
                            html: errorMessage,
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            });
        }
    })();
});
