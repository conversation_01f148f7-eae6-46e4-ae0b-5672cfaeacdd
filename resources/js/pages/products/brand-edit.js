/**
 * Brand Management - Edit
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
    (function () {
        // Initialize Dropzone for brand logo
        let brandLogoDropzone;
        const dropzoneBasic = document.querySelector('#brandLogoUpload');
        const logoInput = document.querySelector('#logo');

        if (dropzoneBasic) {
            brandLogoDropzone = new Dropzone(dropzoneBasic, {
                url: '/admin/brands/upload-logo',
                paramName: 'file',
                maxFiles: 1,
                maxFilesize: 2, // MB
                acceptedFiles: 'image/*',
                addRemoveLinks: true,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                init: function () {
                    this.on('success', function (file, response) {
                        if (response.success) {
                            logoInput.value = response.path;
                        } else {
                            this.removeFile(file);
                            Swal.fire({
                                icon: 'error',
                                title: 'Lỗi!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        }
                    });
                    this.on('error', function (file, errorMessage) {
                        this.removeFile(file);
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: typeof errorMessage === 'string' ? errorMessage : 'Có lỗi xảy ra khi tải lên hình ảnh',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    });
                    this.on('removedfile', function () {
                        logoInput.value = '';
                    });
                }
            });
        }

        // Form validation
        const brandForm = document.getElementById('brandForm');

        if (brandForm) {
            const fv = FormValidation.formValidation(brandForm, {
                fields: {
                    name: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng nhập tên thương hiệu'
                            },
                            stringLength: {
                                min: 2,
                                max: 255,
                                message: 'Tên thương hiệu phải có từ 2 đến 255 ký tự'
                            }
                        }
                    }
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap5: new FormValidation.plugins.Bootstrap5({
                        eleValidClass: '',
                        rowSelector: '.col-md-6, .col-md-12'
                    }),
                    submitButton: new FormValidation.plugins.SubmitButton(),
                    autoFocus: new FormValidation.plugins.AutoFocus()
                }
            }).on('core.form.valid', function () {
                // Get form data
                const formData = new FormData(brandForm);
                const brandId = formData.get('id');

                // Add is_active value
                formData.set('is_active', brandForm.querySelector('#is_active').checked ? 1 : 0);

                // Add _method for Laravel to recognize as PATCH request
                formData.append('_method', 'PATCH');

                // Submit form data via AJAX
                $.ajax({
                    type: 'POST',
                    url: `/admin/brands/update/${brandId}`,
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response.code === 200) {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.text,
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function () {
                                window.location.href = '/admin/brands';
                            });
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.text,
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        }
                    },
                    error: function (error) {
                        let errorMessage = 'Có lỗi xảy ra khi cập nhật thương hiệu. Vui lòng thử lại sau.';

                        if (error.responseJSON && error.responseJSON.errors) {
                            errorMessage = Object.values(error.responseJSON.errors).flat().join('<br>');
                        }

                        Swal.fire({
                            title: 'Lỗi!',
                            html: errorMessage,
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            });
        }
    })();
});
