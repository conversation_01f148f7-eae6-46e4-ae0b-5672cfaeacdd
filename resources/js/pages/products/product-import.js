/**
 * Product Management - Import
 */

'use strict';

// <PERSON><PERSON><PERSON> bảo Dropzone được định nghĩa trước khi sử dụng
if (typeof Dropzone === 'undefined') {
    console.error('Dropzone is not defined. Make sure the library is loaded correctly.');
}

document.addEventListener('DOMContentLoaded', function () {
    console.log('DOM Content Loaded');
    (function () {
        // Initialize Dropzone for file import
        let importDropzone;
        const dropzoneBasic = document.querySelector('#importDropzone');
        const fileInput = document.querySelector('#file');
        const importResults = document.getElementById('importResults');
        const successCount = document.getElementById('successCount');
        const updatedCount = document.getElementById('updatedCount');
        const errorCount = document.getElementById('errorCount');
        const errorDetails = document.getElementById('errorDetails');
        const errorList = document.getElementById('errorList');

        if (dropzoneBasic) {
            try {
                // Thêm log để debug
                console.log('Initializing Dropzone...');

                // Lấy CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (!csrfToken) {
                    console.error('CSRF token not found');
                }

                // Tắt tự động khởi tạo Dropzone
                try {
                    Dropzone.autoDiscover = false;
                } catch (e) {
                    console.error('Error setting Dropzone.autoDiscover:', e);
                }

                // Xóa các instance Dropzone cũ nếu có
                if (importDropzone) {
                    importDropzone.destroy();
                }

                // Cấu hình Dropzone
                console.log('Creating new Dropzone instance');
                importDropzone = new Dropzone(dropzoneBasic, {
                    url: '/admin/products/upload-import-file',
                    method: 'POST',
                    chunking: false,
                    paramName: 'file',
                    maxFiles: 1,
                    maxFilesize: 10, // MB
                    acceptedFiles: '.xlsx,.xls,.csv',
                    addRemoveLinks: true,
                    timeout: 180000, // Tăng timeout lên 3 phút
                    createImageThumbnails: false,
                    autoProcessQueue: true, // Tự động xử lý hàng đợi
                    uploadMultiple: false, // Chỉ upload một file mỗi lần
                    dictDefaultMessage: 'Kéo thả file hoặc nhấp vào để tải lên',
                    dictFallbackMessage: 'Trình duyệt của bạn không hỗ trợ kéo thả file',
                    dictFileTooBig: 'File quá lớn ({{filesize}}MB). Kích thước tối đa: {{maxFilesize}}MB',
                    dictInvalidFileType: 'Bạn không thể tải lên loại file này',
                    dictResponseError: 'Máy chủ trả về mã {{statusCode}}',
                    dictCancelUpload: 'Hủy tải lên',
                    dictUploadCanceled: 'Đã hủy tải lên',
                    dictRemoveFile: 'Xóa file',
                    dictMaxFilesExceeded: 'Bạn không thể tải lên nhiều file hơn',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : '',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    },
                    withCredentials: true,
                    // Đảm bảo Dropzone gửi request lên server
                    // forceFallback: false,
                    // disablePreviews: false,
                    // parallelUploads: 1,
                    // clickable: true,
                    // capture: null,
                    init: function () {
                        const dropzoneInstance = this;

                        // Sự kiện khi thêm file vào dropzone
                        this.on('addedfile', function(file) {
                            console.log('File added:', file.name);
                            console.log('File size:', file.size);
                            console.log('File type:', file.type);

                            // Kiểm tra loại file
                            const acceptedTypes = ['.xlsx', '.xls', '.csv'];
                            const fileExtension = '.' + (file.name.match(/\.([^.]+)$/)?.[1] || '').toLowerCase();

                            if (!acceptedTypes.includes(fileExtension)) {
                                this.removeFile(file);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Loại file không hợp lệ!',
                                    text: 'Chỉ chấp nhận file Excel (.xlsx, .xls) hoặc CSV (.csv)',
                                    customClass: {
                                        confirmButton: 'btn btn-primary'
                                    }
                                });
                                return false;
                            }

                            // Kiểm tra kích thước file
                            if (file.size > 10 * 1024 * 1024) { // 10MB
                                this.removeFile(file);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'File quá lớn!',
                                    text: 'Kích thước tối đa là 10MB',
                                    customClass: {
                                        confirmButton: 'btn btn-primary'
                                    }
                                });
                                return false;
                            }
                        });

                        // Sự kiện khi bắt đầu tải lên
                        this.on('sending', function(file, xhr, formData) {
                            console.log('Sending file:', file.name);
                            console.log('XHR object:', xhr);
                            console.log('Form data:', formData);

                            // Thêm CSRF token vào formData
                            if (csrfToken) {
                                formData.append('_token', csrfToken.getAttribute('content'));
                            }

                            // Hiển thị loading
                            Swal.fire({
                                title: 'Đang tải lên...',
                                html: 'Vui lòng đợi trong khi chúng tôi tải lên file.',
                                allowOutsideClick: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                }
                            });
                        });

                        // Sự kiện khi chuẩn bị tải lên
                        this.on('processing', function(file) {
                            console.log('Processing file:', file.name);
                        });

                        // Sự kiện khi tải lên thành công
                        this.on('success', function (file, response) {
                            // Đảm bảo response không phải là undefined hoặc null
                            if (!response) {
                                console.error('Response is empty');
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Lỗi!',
                                    text: 'Không nhận được phản hồi từ server. Vui lòng thử lại sau.',
                                    customClass: {
                                        confirmButton: 'btn btn-primary'
                                    }
                                });
                                // Xóa file khỏi dropzone
                                try {
                                    dropzoneInstance.removeFile(file);
                                } catch (e) {
                                    console.error('Error removing file:', e);
                                }
                                return;
                            }
                            console.log('Upload success - response type:', typeof response);
                            console.log('Upload success - raw response:', response);
                            Swal.close();

                            // Xử lý phản hồi
                            let responseData = response;

                            // Nếu response là chuỗi, thử chuyển thành JSON
                            if (typeof response === 'string') {
                                try {
                                    responseData = JSON.parse(response);
                                    console.log('Parsed response:', responseData);
                                } catch (e) {
                                    console.error('Error parsing response:', e);
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Lỗi!',
                                        text: 'Không thể xử lý phản hồi từ server. Vui lòng thử lại sau.',
                                        customClass: {
                                            confirmButton: 'btn btn-primary'
                                        }
                                    });
                                    // Xóa file khỏi dropzone
                                    try {
                                        dropzoneInstance.removeFile(file);
                                    } catch (e) {
                                        console.error('Error removing file:', e);
                                    }
                                    return;
                                }
                            } else if (typeof response !== 'object' || response === null) {
                                console.error('Response is not an object:', response);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Lỗi!',
                                    text: 'Phản hồi từ server không hợp lệ. Vui lòng thử lại sau.',
                                    customClass: {
                                        confirmButton: 'btn btn-primary'
                                    }
                                });
                                // Xóa file khỏi dropzone
                                try {
                                    dropzoneInstance.removeFile(file);
                                } catch (e) {
                                    console.error('Error removing file:', e);
                                }
                                return;
                            }

                            // Kiểm tra chi tiết về responseData
                            console.log('Final responseData type:', typeof responseData);
                            console.log('Final responseData:', responseData);

                            // Đảm bảo responseData có thuộc tính success
                            if (responseData && typeof responseData.success === 'undefined') {
                                responseData.success = true;
                            }

                            if (responseData && typeof responseData.path === 'undefined' && file) {
                                responseData.path = 'imports/' + file.name;
                            }

                            console.log('ResponseData after defaults:', responseData);

                            // Kiểm tra xem response có thuộc tính success và có giá trị true không
                            if (responseData && responseData.success && (responseData.success === true || responseData.success === 'true' || responseData.success == 1 || responseData.success == '1')) {
                                console.log('Success condition met, path:', responseData.path);

                                // Lưu đường dẫn file đã upload
                                if (responseData.path) {
                                    // Đường dẫn file đã upload (file X)
                                    fileInput.value = responseData.path;
                                    console.log('File input value set to uploaded file path:', fileInput.value);

                                    // Lưu thông tin file vào localStorage để dùng sau này nếu cần
                                    localStorage.setItem('lastUploadedFile', fileInput.value);
                                    localStorage.setItem('lastUploadedFileName', file.name);
                                    localStorage.setItem('lastUploadedTime', new Date().toISOString());

                                    // Hiển thị thông tin file đã upload
                                    const fileInfoElement = document.getElementById('fileInfo');
                                    if (fileInfoElement) {
                                        fileInfoElement.innerHTML = `
                                            <div class="alert alert-success">
                                                <h6 class="alert-heading mb-1"><i class="ri-check-line me-1"></i> File đã tải lên thành công</h6>
                                                <p class="mb-0">Tên file: ${file.name}</p>
                                                <p class="mb-0">Kích thước: ${(file.size / 1024).toFixed(2)} KB</p>
                                                <p class="mb-0">Thời gian: ${new Date().toLocaleString()}</p>
                                            </div>
                                        `;
                                        fileInfoElement.classList.remove('d-none');
                                    }
                                } else {
                                    console.error('Response path is missing');
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Lỗi!',
                                        text: 'Đường dẫn file không hợp lệ. Vui lòng thử lại sau.',
                                        customClass: {
                                            confirmButton: 'btn btn-primary'
                                        }
                                    });
                                    // Xóa file khỏi dropzone
                                    try {
                                        dropzoneInstance.removeFile(file);
                                    } catch (e) {
                                        console.error('Error removing file:', e);
                                    }
                                    return;
                                }

                                // Hiển thị thông báo thành công
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Thành công!',
                                    text: 'File đã được tải lên thành công',
                                    customClass: {
                                        confirmButton: 'btn btn-primary'
                                    }
                                });

                                return; // Thoát khỏi hàm để tránh chạy vào phần else
                            }

                            // Nếu không thành công, hiển thị thông báo lỗi
                            console.log('Success condition not met, removing file');
                            try {
                                dropzoneInstance.removeFile(file);
                            } catch (e) {
                                console.error('Error removing file:', e);
                            }

                            Swal.fire({
                                icon: 'error',
                                title: 'Lỗi!',
                                text: (responseData && responseData.message) ? responseData.message : 'Có lỗi xảy ra khi tải lên file',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        });

                        // Sự kiện khi có lỗi
                        this.on('error', function (file, errorMessage, xhr) {
                            console.error('Upload error type:', typeof errorMessage);
                            console.error('Upload error:', errorMessage);
                            console.error('File status:', file.status);
                            console.error('File upload progress:', file.upload.progress);
                            console.error('File upload total bytes:', file.upload.total);
                            console.error('File upload bytes sent:', file.upload.bytesSent);

                            // Đảm bảo đóng loading nếu đang hiển thị
                            Swal.close();

                            // Xử lý lỗi từ server
                            let errorText = 'Có lỗi xảy ra khi tải lên file';

                            // Kiểm tra lỗi từ Dropzone
                            if (file.status === Dropzone.ERROR) {
                                console.error('File status: ERROR');
                            }

                            // Xử lý các loại lỗi khác nhau
                            if (typeof errorMessage === 'string') {
                                errorText = errorMessage;
                                console.error('Error message (string):', errorMessage);
                            } else if (errorMessage && errorMessage.message) {
                                errorText = errorMessage.message;
                                console.error('Error message (object):', errorMessage.message);
                            } else if (xhr) {
                                console.error('XHR status:', xhr.status);
                                console.error('XHR response type:', xhr.responseType);

                                if (xhr.responseText) {
                                    console.error('XHR response text:', xhr.responseText);
                                    try {
                                        const response = JSON.parse(xhr.responseText);
                                        errorText = response.message || response.text || errorText;
                                        console.error('Parsed response:', response);
                                    } catch (e) {
                                        // Nếu không phải JSON, sử dụng responseText trực tiếp
                                        errorText = xhr.responseText || errorText;
                                        console.error('Error parsing response:', e);
                                    }
                                }
                            }

                            // Xóa file khỏi dropzone
                            try {
                                dropzoneInstance.removeFile(file);
                                console.log('File removed from dropzone');
                            } catch (e) {
                                console.error('Error removing file:', e);
                            }

                            // Hiển thị thông báo lỗi
                            Swal.fire({
                                icon: 'error',
                                title: 'Lỗi khi tải file!',
                                html: errorText,
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        });

                        // Sự kiện khi xóa file
                        this.on('removedfile', function () {
                            console.log('File removed');
                            fileInput.value = '';
                        });

                        // Sự kiện khi tải lên tiến triển
                        this.on('uploadprogress', function(file, progress, bytesSent) {
                            console.log(`Upload progress: ${progress}% (${bytesSent} bytes sent)`);
                        });

                        // Sự kiện khi hủy tải lên
                        this.on('canceled', function(file) {
                            console.log('Upload canceled:', file.name);
                        });

                        // Sự kiện khi tải lên hoàn tất (thành công hoặc thất bại)
                        this.on('complete', function(file) {
                            console.log('Upload complete:', file.name);
                            console.log('File status:', file.status);
                        });
                    }
                });

                console.log('Dropzone initialized successfully');
            } catch (error) {
                console.error('Error initializing Dropzone:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: 'Không thể khởi tạo trình tải lên: ' + error.message,
                    customClass: {
                        confirmButton: 'btn btn-primary'
                    }
                });
            }
        }

        // Form validation
        const importForm = document.getElementById('importForm');

        if (importForm) {
            const fv = FormValidation.formValidation(importForm, {
                fields: {
                    file: {
                        validators: {
                            notEmpty: {
                                message: 'Vui lòng tải lên file để nhập sản phẩm'
                            }
                        }
                    }
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap5: new FormValidation.plugins.Bootstrap5({
                        eleValidClass: '',
                        rowSelector: '.col-md-12'
                    }),
                    submitButton: new FormValidation.plugins.SubmitButton(),
                    autoFocus: new FormValidation.plugins.AutoFocus()
                }
            }).on('core.form.valid', function () {
                // Get form data
                const formData = new FormData(importForm);

                // Log file path before submit
                console.log('File path before submit:', fileInput.value);

                // Kiểm tra xem đã có file được tải lên chưa
                if (!fileInput.value) {
                    // Hỏi người dùng có muốn sử dụng file mẫu không
                    Swal.fire({
                        title: 'Bạn chưa tải lên file nào',
                        text: 'Bạn có muốn sử dụng file mẫu không?',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonText: 'Sử dụng file mẫu',
                        cancelButtonText: 'Hủy',
                        customClass: {
                            confirmButton: 'btn btn-primary',
                            cancelButton: 'btn btn-outline-danger ms-1'
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Sử dụng file mẫu
                            console.log('Using sample file');
                            fileInput.value = 'imports/mau_nhap_san_pham.xlsx';

                            // Tiếp tục gửi form
                            submitImportForm(formData);
                        }
                    });
                    return;
                }

                // Thêm đường dẫn file vào form data
                console.log('Using uploaded file:', fileInput.value);
                formData.set('file', fileInput.value);

                // Gọi hàm gửi form
                submitImportForm(formData);
            });

            // Hàm gửi form
            function submitImportForm(formData) {

                // Add update_existing value if not already set
                if (!formData.has('update_existing')) {
                    formData.set('update_existing', importForm.querySelector('#update_existing').checked ? 1 : 0);
                }

                // Log form data for debugging
                for (let pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                // Submit form data via AJAX
                $.ajax({
                    type: 'POST',
                    url: '/admin/products/import',
                    data: formData,
                    processData: false,
                    contentType: false,
                    beforeSend: function () {
                        // Show loading
                        Swal.fire({
                            title: 'Đang xử lý...',
                            html: 'Vui lòng đợi trong khi chúng tôi nhập sản phẩm.',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });
                    },
                    success: function (response) {
                        Swal.close();

                        if (response.code === 200) {
                            // Show results
                            successCount.textContent = response.results.success || 0;
                            updatedCount.textContent = response.results.updated || 0;
                            errorCount.textContent = response.results.errors ? response.results.errors.length : 0;

                            // Show error details if any
                            if (response.results.errors && response.results.errors.length > 0) {
                                errorList.innerHTML = '';
                                response.results.errors.forEach(function (error) {
                                    const errorItem = document.createElement('p');
                                    errorItem.innerHTML = `<strong>Dòng ${error.row}:</strong> ${error.message}`;
                                    errorList.appendChild(errorItem);
                                });
                                errorDetails.classList.remove('d-none');
                            } else {
                                errorDetails.classList.add('d-none');
                            }

                            // Show results section
                            importResults.classList.remove('d-none');

                            // Scroll to results
                            importResults.scrollIntoView({behavior: 'smooth'});

                            // Xóa file tạm sau khi import thành công
                            // Đặt giá trị của fileInput về rỗng
                            fileInput.value = '';

                            // Xóa file khỏi dropzone nếu có
                            if (importDropzone) {
                                importDropzone.removeAllFiles();
                            }

                            // Xóa thông tin file đã upload
                            const fileInfoElement = document.getElementById('fileInfo');
                            if (fileInfoElement) {
                                fileInfoElement.innerHTML = '';
                                fileInfoElement.classList.add('d-none');
                            }
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.text,
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        }
                    },
                    error: function (error) {
                        console.error('AJAX error:', error);
                        Swal.close();

                        let errorMessage = 'Có lỗi xảy ra khi nhập sản phẩm. Vui lòng thử lại sau.';
                        let errorDetails = '';

                        // Log error details
                        if (error.responseText) {
                            console.error('Response text:', error.responseText);
                            try {
                                const response = JSON.parse(error.responseText);
                                console.error('Parsed response:', response);
                                if (response.text) {
                                    errorMessage = response.text;
                                }
                            } catch (e) {
                                console.error('Error parsing response:', e);
                                errorDetails = '<br><br><strong>Chi tiết lỗi:</strong><br>' + error.responseText;
                            }
                        }

                        if (error.responseJSON) {
                            console.error('Response JSON:', error.responseJSON);
                            if (error.responseJSON.text) {
                                errorMessage = error.responseJSON.text;
                            } else if (error.responseJSON.message) {
                                errorMessage = error.responseJSON.message;
                            } else if (error.responseJSON.errors) {
                                errorMessage = Object.values(error.responseJSON.errors).flat().join('<br>');
                            }
                        }

                        // Add file path to error message
                        errorMessage += '<br><br><strong>Đường dẫn file:</strong> ' + fileInput.value;

                        // Add error details if available
                        if (errorDetails) {
                            errorMessage += errorDetails;
                        }

                        Swal.fire({
                            title: 'Lỗi!',
                            html: errorMessage,
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        }).then(() => {
                            // Xóa file tạm sau khi import thất bại
                            // Đặt giá trị của fileInput về rỗng
                            fileInput.value = '';

                            // Xóa file khỏi dropzone nếu có
                            if (importDropzone) {
                                importDropzone.removeAllFiles();
                            }

                            // Xóa thông tin file đã upload
                            const fileInfoElement = document.getElementById('fileInfo');
                            if (fileInfoElement) {
                                fileInfoElement.innerHTML = '';
                                fileInfoElement.classList.add('d-none');
                            }
                        });
                    }
                });
            }
        }
    })();
});
