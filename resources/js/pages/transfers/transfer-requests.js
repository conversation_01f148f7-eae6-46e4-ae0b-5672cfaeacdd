/**
 * Transfer Requests Management
 */

'use strict';

$(function () {
    let borderColor, bodyBg, headingColor;

    if (isDarkStyle) {
        borderColor = config.colors_dark.borderColor;
        bodyBg = config.colors_dark.bodyBg;
        headingColor = config.colors_dark.headingColor;
    } else {
        borderColor = config.colors.borderColor;
        bodyBg = config.colors.bodyBg;
        headingColor = config.colors.headingColor;
    }

    // Variable declaration for table
    var dt_transfer_requests_table = $('.datatables-transfer-requests');

    // Transfer Requests datatable
    if (dt_transfer_requests_table.length) {
        var dt_transfer_requests = dt_transfer_requests_table.DataTable({
            ajax: {
                url: baseUrl + 'admin/transfer-requests/datatable',
                type: 'POST',
                data: function (d) {
                    d.status = $('#status-filter').val();
                    d.from_warehouse_id = $('#from-warehouse-filter').val();
                    d.to_warehouse_id = $('#to-warehouse-filter').val();
                    d._token = $('meta[name="csrf-token"]').attr('content');
                }
            },
            columns: [
                { data: '' }, // Responsive Control column
                { data: 'code' },
                { data: 'from_warehouse' },
                { data: 'to_warehouse' },
                { data: 'status' },
                { data: 'created_by' },
                { data: 'created_at' },
                { data: 'actions' }
            ],
            columnDefs: [
                {
                    // For Responsive
                    className: 'control',
                    searchable: false,
                    orderable: false,
                    responsivePriority: 2,
                    targets: 0,
                    render: function (data, type, full, meta) {
                        return '';
                    }
                },
                {
                    // Code
                    targets: 1,
                    responsivePriority: 1,
                    render: function (data, type, full, meta) {
                        return '<span class="fw-medium">' + data + '</span>';
                    }
                },
                {
                    // Status
                    targets: 4,
                    render: function (data, type, full, meta) {
                        return data;
                    }
                },
                {
                    // Actions
                    targets: -1,
                    title: 'Thao tác',
                    searchable: false,
                    orderable: false,
                    width: '100px',
                    className: 'text-center actions-column',
                    render: function (data, type, full, meta) {
                        return data;
                    }
                }
            ],
            order: [[6, 'desc']], // Sort by created_at desc
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end"f>><"table-responsive"t><"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
            displayLength: 10,
            lengthMenu: [10, 25, 50, 75, 100],
            language: {
                sLengthMenu: '_MENU_',
                search: '',
                searchPlaceholder: 'Tìm kiếm yêu cầu chuyển kho...',
                paginate: {
                    next: '<i class="ri-arrow-right-s-line"></i>',
                    previous: '<i class="ri-arrow-left-s-line"></i>'
                }
            },
            responsive: {
                details: {
                    display: $.fn.dataTable.Responsive.display.modal({
                        header: function (row) {
                            var data = row.data();
                            return 'Chi tiết yêu cầu chuyển kho: ' + data['code'];
                        }
                    }),
                    type: 'column',
                    renderer: function (api, rowIdx, columns) {
                        var data = $.map(columns, function (col, i) {
                            return col.title !== '' // ? Do not show row in modal popup if title is blank (for check box)
                                ? '<tr data-dt-row="' +
                                col.rowIndex +
                                '" data-dt-column="' +
                                col.columnIndex +
                                '">' +
                                '<td>' +
                                col.title +
                                ':' +
                                '</td> ' +
                                '<td>' +
                                col.data +
                                '</td>' +
                                '</tr>'
                                : '';
                        }).join('');

                        return data ? $('<table class="table"/><tbody />').append(data) : false;
                    }
                }
            },
            drawCallback: function() {
                // Khởi tạo tooltip cho các icon buttons sau khi DataTable được vẽ lại
                $('[data-bs-toggle="tooltip"]').tooltip();
            }
        });
    }

    // Filter functionality
    $('#status-filter, #from-warehouse-filter, #to-warehouse-filter').on('change', function () {
        dt_transfer_requests.draw();
    });

    // Khởi tạo tooltip khi trang được load
    $('[data-bs-toggle="tooltip"]').tooltip();
});
