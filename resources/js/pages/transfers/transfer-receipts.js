/**
 * Transfer Receipts Management
 */

'use strict';

$(function () {
    let borderColor, bodyBg, headingColor;

    if (isDarkStyle) {
        borderColor = config.colors_dark.borderColor;
        bodyBg = config.colors_dark.bodyBg;
        headingColor = config.colors_dark.headingColor;
    } else {
        borderColor = config.colors.borderColor;
        bodyBg = config.colors.bodyBg;
        headingColor = config.colors.headingColor;
    }

    // Variable declaration for table
    var dt_transfer_receipts_table = $('.datatables-transfer-receipts');

    // Transfer Receipts datatable
    if (dt_transfer_receipts_table.length) {
        var dt_transfer_receipts = dt_transfer_receipts_table.DataTable({
            ajax: {
                url: baseUrl + 'admin/transfer-receipts/datatable',
                type: 'POST',
                data: function (d) {
                    d.status = $('#status-filter').val();
                    d.from_warehouse_id = $('#from-warehouse-filter').val();
                    d.to_warehouse_id = $('#to-warehouse-filter').val();
                    d._token = $('meta[name="csrf-token"]').attr('content');
                }
            },
            columns: [
                { data: '' }, // Responsive Control column
                { data: 'code' },
                { data: 'transfer_order_code' },
                { data: 'from_warehouse' },
                { data: 'to_warehouse' },
                { data: 'transit_warehouse' },
                { data: 'status' },
                { data: 'created_by' },
                { data: 'created_at' },
                { data: 'actions' }
            ],
            columnDefs: [
                {
                    // For Responsive
                    className: 'control',
                    searchable: false,
                    orderable: false,
                    responsivePriority: 2,
                    targets: 0,
                    render: function (data, type, full, meta) {
                        return '';
                    }
                },
                {
                    // Code
                    targets: 1,
                    responsivePriority: 1,
                    render: function (data, type, full, meta) {
                        return '<span class="fw-medium">' + data + '</span>';
                    }
                },
                {
                    // Transfer Order Code
                    targets: 2,
                    render: function (data, type, full, meta) {
                        return data ? '<span class="text-primary">' + data + '</span>' : '-';
                    }
                },
                {
                    // Status
                    targets: 6,
                    render: function (data, type, full, meta) {
                        return data;
                    }
                },
                {
                    // Actions
                    targets: -1,
                    title: 'Thao tác',
                    searchable: false,
                    orderable: false,
                    render: function (data, type, full, meta) {
                        return data;
                    }
                }
            ],
            order: [[8, 'desc']], // Sort by created_at desc
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end"f>><"table-responsive"t><"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
            displayLength: 10,
            lengthMenu: [10, 25, 50, 75, 100],
            language: {
                sLengthMenu: '_MENU_',
                search: '',
                searchPlaceholder: 'Tìm kiếm phiếu nhận hàng...',
                paginate: {
                    next: '<i class="ri-arrow-right-s-line"></i>',
                    previous: '<i class="ri-arrow-left-s-line"></i>'
                }
            },
            responsive: {
                details: {
                    display: $.fn.dataTable.Responsive.display.modal({
                        header: function (row) {
                            var data = row.data();
                            return 'Chi tiết phiếu nhận hàng: ' + data['code'];
                        }
                    }),
                    type: 'column',
                    renderer: function (api, rowIdx, columns) {
                        var data = $.map(columns, function (col, i) {
                            return col.title !== '' // ? Do not show row in modal popup if title is blank (for check box)
                                ? '<tr data-dt-row="' +
                                col.rowIndex +
                                '" data-dt-column="' +
                                col.columnIndex +
                                '">' +
                                '<td>' +
                                col.title +
                                ':' +
                                '</td> ' +
                                '<td>' +
                                col.data +
                                '</td>' +
                                '</tr>'
                                : '';
                        }).join('');

                        return data ? $('<table class="table"/><tbody />').append(data) : false;
                    }
                }
            }
        });
    }

    // Filter functionality
    $('#status-filter, #from-warehouse-filter, #to-warehouse-filter').on('change', function () {
        dt_transfer_receipts.draw();
    });

    // Submit for approval
    $(document).on('click', '.submit-approval-btn', function () {
        var transferReceiptId = $(this).data('id');

        Swal.fire({
            title: 'Xác nhận gửi duyệt',
            text: 'Bạn có chắc chắn muốn gửi phiếu nhận hàng này để duyệt?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Gửi duyệt',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-primary me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-receipts/' + transferReceiptId + '/submit',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            });
                            dt_transfer_receipts.draw();
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi gửi duyệt.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // Approve
    $(document).on('click', '.approve-btn', function () {
        var transferReceiptId = $(this).data('id');

        Swal.fire({
            title: 'Xác nhận duyệt',
            text: 'Bạn có chắc chắn muốn duyệt phiếu nhận hàng này?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Duyệt',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-success me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-receipts/' + transferReceiptId + '/approve',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            });
                            dt_transfer_receipts.draw();
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi duyệt.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // Reject
    var rejectTransferReceiptId = null;
    $(document).on('click', '.reject-btn', function () {
        rejectTransferReceiptId = $(this).data('id');
        $('#rejectModal').modal('show');
    });

    $('#rejectForm').on('submit', function (e) {
        e.preventDefault();

        if (!rejectTransferReceiptId) return;

        var reason = $('#reject-reason').val().trim();
        if (!reason) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập lý do từ chối.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        showLoading();

        $.ajax({
            url: baseUrl + 'admin/transfer-receipts/' + rejectTransferReceiptId + '/reject',
            type: 'POST',
            data: {
                reason: reason,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                hideLoading();
                $('#rejectModal').modal('hide');

                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        text: response.message,
                        customClass: {
                            confirmButton: 'btn btn-success'
                        }
                    });
                    dt_transfer_receipts.draw();
                    $('#reject-reason').val('');
                    rejectTransferReceiptId = null;
                }
            },
            error: function (xhr) {
                hideLoading();
                var response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi từ chối.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    });

    // Cancel
    $(document).on('click', '.cancel-btn', function () {
        var transferReceiptId = $(this).data('id');

        Swal.fire({
            title: 'Xác nhận hủy',
            text: 'Bạn có chắc chắn muốn hủy phiếu nhận hàng này?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Hủy phiếu',
            cancelButtonText: 'Không',
            customClass: {
                confirmButton: 'btn btn-warning me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-receipts/' + transferReceiptId + '/cancel',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            });
                            dt_transfer_receipts.draw();
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi hủy.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });
});
