/**
 * Transfer Request Detail Management
 */

'use strict';

$(function () {
    // Submit for approval
    $(document).on('click', '.submit-approval-btn', function () {
        var transferRequestId = $(this).data('id');
        
        Swal.fire({
            title: '<PERSON><PERSON><PERSON> nhận gửi duyệt',
            text: '<PERSON>ạn có chắc chắn muốn gửi yêu cầu chuyển kho này để duyệt?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Gửi duyệt',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-primary me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();
                
                $.ajax({
                    url: baseUrl + 'admin/transfer-requests/' + transferRequestId + '/submit',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi gửi duyệt.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // Approve
    $(document).on('click', '.approve-btn', function () {
        var transferRequestId = $(this).data('id');
        
        Swal.fire({
            title: 'Xác nhận duyệt',
            text: 'Bạn có chắc chắn muốn duyệt yêu cầu chuyển kho này?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Duyệt',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-success me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();
                
                $.ajax({
                    url: baseUrl + 'admin/transfer-requests/' + transferRequestId + '/approve',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi duyệt.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // Reject
    var rejectTransferRequestId = null;
    $(document).on('click', '.reject-btn', function () {
        rejectTransferRequestId = $(this).data('id');
        $('#rejectModal').modal('show');
    });

    $('#rejectForm').on('submit', function (e) {
        e.preventDefault();
        
        if (!rejectTransferRequestId) return;
        
        var reason = $('#reject-reason').val().trim();
        if (!reason) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập lý do từ chối.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }
        
        showLoading();
        
        $.ajax({
            url: baseUrl + 'admin/transfer-requests/' + rejectTransferRequestId + '/reject',
            type: 'POST',
            data: {
                reason: reason,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                hideLoading();
                $('#rejectModal').modal('hide');
                
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        text: response.message,
                        customClass: {
                            confirmButton: 'btn btn-success'
                        }
                    }).then(function() {
                        location.reload();
                    });
                    $('#reject-reason').val('');
                    rejectTransferRequestId = null;
                }
            },
            error: function (xhr) {
                hideLoading();
                var response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi từ chối.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    });

    // Cancel
    $(document).on('click', '.cancel-btn', function () {
        var transferRequestId = $(this).data('id');
        
        Swal.fire({
            title: 'Xác nhận hủy',
            text: 'Bạn có chắc chắn muốn hủy yêu cầu chuyển kho này?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Hủy yêu cầu',
            cancelButtonText: 'Không',
            customClass: {
                confirmButton: 'btn btn-warning me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();
                
                $.ajax({
                    url: baseUrl + 'admin/transfer-requests/' + transferRequestId + '/cancel',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi hủy.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });
});
