/**
 * Transfer Order Form Management
 */

'use strict';

$(function () {
    let warehouseProducts = [];
    let selectedProducts = [];
    let productIndex = 0;

    // Initialize Select2
    $('.select2').select2({
        placeholder: function() {
            return $(this).data('placeholder');
        },
        allowClear: true
    });

    // Initialize modal Select2
    $('#modal_product_id').select2({
        placeholder: 'Chọn sản phẩm',
        allowClear: true,
        dropdownParent: $('#addProductModal')
    });

    // Creation type change
    $('input[name="creation_type"]').on('change', function() {
        const creationType = $(this).val();

        $('.creation-form').hide();

        if (creationType === 'from_request') {
            $('#fromRequestForm').show();
        } else {
            $('#manualForm').show();
        }
    });

    // Transfer request selection change
    $('#transfer_request_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');

        if ($(this).val()) {
            $('#detail-from-warehouse').text(selectedOption.data('from-warehouse'));
            $('#detail-to-warehouse').text(selectedOption.data('to-warehouse'));
            $('#detail-created-at').text(selectedOption.data('created-at'));
            $('#view-request-link').attr('href', baseUrl + 'admin/transfer-requests/' + $(this).val());
            $('#requestDetails').show();
        } else {
            $('#requestDetails').hide();
        }
    });

    // From warehouse change event (for manual form)
    $('#from_warehouse_id_manual').on('change', function() {
        const warehouseId = $(this).val();

        if (warehouseId) {
            loadWarehouseProducts(warehouseId);
            $('#addProductBtn').prop('disabled', false);
        } else {
            $('#addProductBtn').prop('disabled', true);
            warehouseProducts = [];
        }

        // Update available quantities for existing products
        updateAvailableQuantities();
    });

    // Load warehouse products
    function loadWarehouseProducts(warehouseId) {
        showLoading();

        $.ajax({
            url: baseUrl + 'admin/transfer-requests/warehouse/' + warehouseId + '/products',
            type: 'GET',
            success: function(response) {
                hideLoading();
                if (response.success) {
                    warehouseProducts = response.data;
                    updateProductSelect();
                    updateAvailableQuantities();
                }
            },
            error: function(xhr) {
                hideLoading();
                console.error('Error loading warehouse products:', xhr);
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: 'Có lỗi xảy ra khi tải danh sách sản phẩm.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    }

    // Update product select options
    function updateProductSelect() {
        const $select = $('#modal_product_id');
        $select.empty().append('<option value="">Chọn sản phẩm</option>');

        warehouseProducts.forEach(function(product) {
            if (!selectedProducts.includes(product.id.toString())) {
                $select.append(`<option value="${product.id}" data-available="${product.available_quantity}" data-code="${product.code}" data-unit="${product.unit}">${product.name}</option>`);
            }
        });

        $select.trigger('change');
    }

    // Update available quantities in table
    function updateAvailableQuantities() {
        $('#productsTable tbody tr').each(function() {
            const productId = $(this).data('product-id');
            if (productId) {
                const product = warehouseProducts.find(p => p.id == productId);
                const availableQty = product ? product.available_quantity : 0;
                $(this).find('.available-quantity').text(availableQty);
            }
        });
    }

    // Product select change event
    $('#modal_product_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const availableQty = selectedOption.data('available') || 0;

        $('#modal_available_quantity').val(availableQty);
        $('#modal_quantity').attr('max', availableQty);
    });

    // Add product button click
    $('#addProductBtn').on('click', function() {
        updateProductSelect();
        $('#addProductModal').modal('show');
    });

    // Confirm add product
    $('#confirmAddProduct').on('click', function() {
        const productId = $('#modal_product_id').val();
        const quantity = parseFloat($('#modal_quantity').val());
        const notes = $('#modal_notes').val();

        if (!productId) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng chọn sản phẩm.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        if (!quantity || quantity <= 0) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập số lượng hợp lệ.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        const selectedOption = $('#modal_product_id').find('option:selected');
        const availableQty = parseFloat(selectedOption.data('available')) || 0;

        if (quantity > availableQty) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: `Số lượng chuyển không được vượt quá số lượng tồn kho (${availableQty}).`,
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        addProductToTable(productId, selectedOption.text(), selectedOption.data('code'), selectedOption.data('unit'), availableQty, quantity, notes);

        // Reset modal
        $('#addProductForm')[0].reset();
        $('#modal_product_id').val('').trigger('change');
        $('#addProductModal').modal('hide');
    });

    // Add product to table
    function addProductToTable(productId, productName, productCode, productUnit, availableQty, quantity, notes) {
        selectedProducts.push(productId.toString());

        // Remove no products row if exists
        $('#noProductsRow').remove();

        const row = `
            <tr data-product-id="${productId}">
                <td>
                    ${productName}
                    <input type="hidden" name="items[${productIndex}][product_id]" value="${productId}">
                </td>
                <td>${productCode}</td>
                <td>${productUnit}</td>
                <td class="available-quantity">${availableQty}</td>
                <td>
                    <input type="number" name="items[${productIndex}][quantity]" class="form-control quantity-input"
                           value="${quantity}" min="0.01" step="0.01" max="${availableQty}" required>
                    <input type="hidden" name="items[${productIndex}][notes]" value="${notes}">
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-product-btn">
                        <i class="ri-delete-bin-7-line"></i>
                    </button>
                </td>
            </tr>
        `;

        $('#productsTable tbody').append(row);
        productIndex++;

        updateProductSelect();
    }

    // Remove product from table
    $(document).on('click', '.remove-product-btn', function() {
        const $row = $(this).closest('tr');
        const productId = $row.data('product-id').toString();

        // Remove from selected products
        selectedProducts = selectedProducts.filter(id => id !== productId);

        // Remove row
        $row.remove();

        // Show no products row if table is empty
        if ($('#productsTable tbody tr').length === 0) {
            $('#productsTable tbody').append(`
                <tr id="noProductsRow">
                    <td colspan="6" class="text-center text-muted">Chưa có sản phẩm nào được thêm</td>
                </tr>
            `);
        }

        // Update product select
        updateProductSelect();

        // Reindex form inputs
        reindexFormInputs();
    });

    // Reindex form inputs after removing items
    function reindexFormInputs() {
        $('#productsTable tbody tr[data-product-id]').each(function(index) {
            $(this).find('input[name*="[product_id]"]').attr('name', `items[${index}][product_id]`);
            $(this).find('input[name*="[quantity]"]').attr('name', `items[${index}][quantity]`);
            $(this).find('input[name*="[notes]"]').attr('name', `items[${index}][notes]`);
        });
        productIndex = $('#productsTable tbody tr[data-product-id]').length;
    }

    // Validate quantity input
    $(document).on('input', '.quantity-input', function() {
        const $input = $(this);
        const max = parseFloat($input.attr('max'));
        const value = parseFloat($input.val());

        if (value > max) {
            $input.val(max);
            Swal.fire({
                icon: 'warning',
                title: 'Cảnh báo!',
                text: `Số lượng không được vượt quá ${max}.`,
                customClass: {
                    confirmButton: 'btn btn-warning'
                }
            });
        }
    });

    // Form submissions
    $('#transferOrderFromRequestForm').on('submit', function(e) {
        e.preventDefault();

        const transferRequestId = $('#transfer_request_id').val();
        const transferRequestElement = $('#transfer_request_id');

        // Kiểm tra xem có transfer_request_id không (có thể là hidden input hoặc select)
        if (!transferRequestId) {
            // Chỉ hiển thị lỗi nếu là select dropdown (không phải hidden input)
            if (transferRequestElement.is('select')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: 'Vui lòng chọn yêu cầu chuyển kho.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
                return;
            }
        }

        submitForm($(this));
    });

    $('#transferOrderManualForm').on('submit', function(e) {
        e.preventDefault();

        // Validate products
        if ($('#productsTable tbody tr[data-product-id]').length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng thêm ít nhất một sản phẩm.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        // Validate warehouses
        const fromWarehouse = $('#from_warehouse_id_manual').val();
        const toWarehouse = $('#to_warehouse_id_manual').val();

        if (fromWarehouse === toWarehouse) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Kho nguồn và kho đích không được giống nhau.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        submitForm($(this));
    });

    function submitForm($form) {
        showLoading();

        $.ajax({
            url: $form.attr('action'),
            type: $form.attr('method'),
            data: $form.serialize(),
            success: function(response) {
                hideLoading();
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        text: response.message,
                        customClass: {
                            confirmButton: 'btn btn-success'
                        }
                    }).then(function() {
                        if (response.redirect) {
                            window.location.href = response.redirect;
                        }
                    });
                }
            },
            error: function(xhr) {
                hideLoading();
                const response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi tạo phiếu chuyển hàng.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    }
});
