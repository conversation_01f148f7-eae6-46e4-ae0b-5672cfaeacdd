/**
 * Transfer Receipt Detail Management
 */

'use strict';

$(function () {
    // Submit for approval
    $(document).on('click', '.submit-approval-btn', function () {
        var transferReceiptId = $(this).data('id');

        Swal.fire({
            title: '<PERSON><PERSON><PERSON> nhận gửi duyệt',
            text: 'Bạn có chắc chắn muốn gửi phiếu nhận hàng này để duyệt?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Gửi duyệt',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-primary me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-receipts/' + transferReceiptId + '/submit',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi gửi duyệt.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // Approve
    $(document).on('click', '.approve-btn', function () {
        var transferReceiptId = $(this).data('id');

        Swal.fire({
            title: 'Xác nhận duyệt',
            text: 'Bạn có chắc chắn muốn duyệt phiếu nhận hàng này?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Duyệt',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-success me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-receipts/' + transferReceiptId + '/approve',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi duyệt.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // Reject
    var rejectTransferReceiptId = null;
    $(document).on('click', '.reject-btn', function () {
        rejectTransferReceiptId = $(this).data('id');
        $('#rejectModal').modal('show');
    });

    $('#rejectForm').on('submit', function (e) {
        e.preventDefault();

        if (!rejectTransferReceiptId) return;

        var reason = $('#reject-reason').val().trim();
        if (!reason) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập lý do từ chối.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        showLoading();

        $.ajax({
            url: baseUrl + 'admin/transfer-receipts/' + rejectTransferReceiptId + '/reject',
            type: 'POST',
            data: {
                reason: reason,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                hideLoading();
                $('#rejectModal').modal('hide');

                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        text: response.message,
                        customClass: {
                            confirmButton: 'btn btn-success'
                        }
                    }).then(function() {
                        location.reload();
                    });
                    $('#reject-reason').val('');
                    rejectTransferReceiptId = null;
                }
            },
            error: function (xhr) {
                hideLoading();
                var response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi từ chối.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    });

    // Cancel
    $(document).on('click', '.cancel-btn', function () {
        var transferReceiptId = $(this).data('id');

        Swal.fire({
            title: 'Xác nhận hủy',
            text: 'Bạn có chắc chắn muốn hủy phiếu nhận hàng này?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Hủy phiếu',
            cancelButtonText: 'Không',
            customClass: {
                confirmButton: 'btn btn-warning me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-receipts/' + transferReceiptId + '/cancel',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi hủy.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // IMEI Management
    var currentItemId = null;
    var currentProductName = null;
    var currentQuantity = null;

    // Manage IMEI button click
    $(document).on('click', '.manage-imei-btn', function () {
        currentItemId = $(this).data('item-id');
        currentProductName = $(this).data('product-name');
        currentQuantity = $(this).data('quantity');

        $('#imei-product-name').text(currentProductName);
        $('#imei-input').val('');

        loadImeiList();
        $('#imeiModal').modal('show');
    });

    // Validate and add IMEI with real-time validation
    function validateAndAddImei(imei) {
        if (!imei) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập IMEI.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        showLoading();

        // First validate IMEI availability
        $.ajax({
            url: baseUrl + 'admin/transfer-receipts/' + getTransferReceiptId() + '/validate-imei',
            type: 'POST',
            data: {
                item_id: currentItemId,
                imei: imei,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                if (response.success && response.data.valid) {
                    // IMEI is valid, proceed to add it
                    addImei(imei);
                } else {
                    hideLoading();
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi!',
                        text: 'IMEI không hợp lệ.',
                        customClass: {
                            confirmButton: 'btn btn-danger'
                        }
                    });
                }
            },
            error: function (xhr) {
                hideLoading();
                var response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi kiểm tra IMEI.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    }

    // Add IMEI (after validation)
    function addImei(imei) {
        $.ajax({
            url: baseUrl + 'admin/transfer-receipts/' + getTransferReceiptId() + '/add-imei',
            type: 'POST',
            data: {
                item_id: currentItemId,
                imei: imei,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                hideLoading();
                if (response.success) {
                    $('#imei-input').val('');
                    loadImeiList();
                    updateItemStatus();

                    // Auto-focus back to input for continuous scanning
                    setTimeout(() => {
                        $('#imei-input').focus();
                    }, 100);

                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        text: response.message,
                        timer: 1500,
                        showConfirmButton: false
                    });
                }
            },
            error: function (xhr) {
                hideLoading();
                var response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi thêm IMEI.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    }

    // Load IMEI list
    function loadImeiList() {
        $.ajax({
            url: baseUrl + 'admin/transfer-receipts/' + getTransferReceiptId() + '/items/' + currentItemId + '/imeis',
            type: 'GET',
            success: function (response) {
                if (response.success) {
                    renderImeiTable(response.data);
                }
            },
            error: function (xhr) {
                console.error('Error loading IMEI list:', xhr);
            }
        });
    }

    // Render IMEI table
    function renderImeiTable(imeis) {
        var tbody = $('#imei-table-body');
        tbody.empty();

        if (imeis.length === 0) {
            tbody.append('<tr><td colspan="5" class="text-center text-muted">Chưa có IMEI nào</td></tr>');
            return;
        }

        imeis.forEach(function (imei, index) {
            var row = `
                <tr>
                    <td>${index + 1}</td>
                    <td>${imei.imei}</td>
                    <td>${imei.scanned_by ? imei.scanned_by.name : '-'}</td>
                    <td>${imei.scanned_at ? new Date(imei.scanned_at).toLocaleString('vi-VN') : '-'}</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-imei-btn" data-imei-id="${imei.id}">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    // Remove IMEI
    $(document).on('click', '.remove-imei-btn', function () {
        var imeiId = $(this).data('imei-id');

        Swal.fire({
            title: 'Xác nhận xóa',
            text: 'Bạn có chắc chắn muốn xóa IMEI này?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-danger me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-receipts/' + getTransferReceiptId() + '/remove-imei/' + imeiId,
                    type: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            loadImeiList();
                            updateItemStatus();
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi xóa IMEI.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // IMEI input auto-submit and validation
    let imeiInputTimeout;
    $('#imei-input').on('input', function() {
        // Clear previous timeout
        if (imeiInputTimeout) {
            clearTimeout(imeiInputTimeout);
        }

        // Set new timeout for auto-submit
        imeiInputTimeout = setTimeout(() => {
            const imei = $(this).val().trim();
            // Auto-submit when IMEI length is sufficient (usually 10-15 characters)
            if (imei && imei.length >= 10) {
                validateAndAddImei(imei);
            }
        }, 500); // Wait 500ms after user stops typing
    });

    // IMEI input enter key
    $('#imei-input').on('keypress', function (e) {
        if (e.which === 13) {
            e.preventDefault();
            const imei = $(this).val().trim();
            if (imei) {
                validateAndAddImei(imei);
            }
        }
    });

    // Add IMEI button
    $('#add-imei-btn').on('click', function () {
        const imei = $('#imei-input').val().trim();
        if (imei) {
            validateAndAddImei(imei);
        }
    });

    // Batch Management
    $(document).on('click', '.manage-batch-btn', function () {
        currentItemId = $(this).data('item-id');
        currentProductName = $(this).data('product-name');
        currentQuantity = $(this).data('quantity');

        $('#batch-product-name').text(currentProductName);
        $('#batch-form')[0].reset();

        loadBatchList();
        $('#batchModal').modal('show');
    });

    // Add Batch with validation
    $('#batch-form').on('submit', function (e) {
        e.preventDefault();

        var batchNumber = $('#batch-number').val().trim();
        var quantity = parseFloat($('#batch-quantity').val());
        var expiryDate = $('#batch-expiry').val();

        if (!batchNumber) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập số lô.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        if (!quantity || quantity <= 0) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập số lượng hợp lệ.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        validateAndAddBatch(batchNumber, quantity, expiryDate);
    });

    // Validate and add batch with real-time validation
    function validateAndAddBatch(batchNumber, quantity, expiryDate) {
        showLoading();

        // First validate batch availability
        $.ajax({
            url: baseUrl + 'admin/transfer-receipts/' + getTransferReceiptId() + '/validate-batch',
            type: 'POST',
            data: {
                item_id: currentItemId,
                batch_number: batchNumber,
                quantity: quantity,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                if (response.success && response.data.valid) {
                    // Batch is valid, proceed to add it
                    addBatch(batchNumber, quantity, expiryDate);
                } else {
                    hideLoading();
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi!',
                        text: 'Batch không hợp lệ.',
                        customClass: {
                            confirmButton: 'btn btn-danger'
                        }
                    });
                }
            },
            error: function (xhr) {
                hideLoading();
                var response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi kiểm tra batch.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    }

    // Add batch (after validation)
    function addBatch(batchNumber, quantity, expiryDate) {
        $.ajax({
            url: baseUrl + 'admin/transfer-receipts/' + getTransferReceiptId() + '/add-batch',
            type: 'POST',
            data: {
                item_id: currentItemId,
                batch_number: batchNumber,
                quantity: quantity,
                expiry_date: expiryDate || null,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                hideLoading();
                if (response.success) {
                    $('#batch-form')[0].reset();
                    loadBatchList();
                    updateItemStatus();

                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        text: response.message,
                        timer: 1500,
                        showConfirmButton: false
                    });
                }
            },
            error: function (xhr) {
                hideLoading();
                var response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi thêm batch.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    }

    // Load Batch list
    function loadBatchList() {
        $.ajax({
            url: baseUrl + 'admin/transfer-receipts/' + getTransferReceiptId() + '/items/' + currentItemId + '/batches',
            type: 'GET',
            success: function (response) {
                if (response.success) {
                    renderBatchTable(response.data);
                }
            },
            error: function (xhr) {
                console.error('Error loading batch list:', xhr);
            }
        });
    }

    // Render Batch table
    function renderBatchTable(batches) {
        var tbody = $('#batch-table-body');
        tbody.empty();

        if (batches.length === 0) {
            tbody.append('<tr><td colspan="5" class="text-center text-muted">Chưa có batch nào</td></tr>');
            return;
        }

        batches.forEach(function (batch, index) {
            var expiryDate = batch.expiry_date ? new Date(batch.expiry_date).toLocaleDateString('vi-VN') : '-';
            var row = `
                <tr>
                    <td>${index + 1}</td>
                    <td>${batch.batch_number}</td>
                    <td>${parseFloat(batch.quantity).toLocaleString('vi-VN')}</td>
                    <td>${expiryDate}</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-batch-btn" data-batch-id="${batch.id}">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    // Remove Batch
    $(document).on('click', '.remove-batch-btn', function () {
        var batchId = $(this).data('batch-id');

        Swal.fire({
            title: 'Xác nhận xóa',
            text: 'Bạn có chắc chắn muốn xóa batch này?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-danger me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-receipts/' + getTransferReceiptId() + '/remove-batch/' + batchId,
                    type: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            loadBatchList();
                            updateItemStatus();
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi xóa batch.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // Helper functions
    function getTransferReceiptId() {
        var pathParts = window.location.pathname.split('/');
        return pathParts[pathParts.length - 1];
    }

    function updateItemStatus() {
        // Reload page to update item status
        setTimeout(function() {
            location.reload();
        }, 1000);
    }
});
