/**
 * Transfer Receipt Form Management
 */

'use strict';

$(function () {
    let selectedProducts = [];
    let productIndex = 0;

    // Initialize Select2
    $('.select2').select2({
        placeholder: function() {
            return $(this).data('placeholder');
        },
        allowClear: true
    });

    // Initialize modal Select2
    $('#modal_product_id').select2({
        placeholder: 'Chọn sản phẩm',
        allowClear: true,
        dropdownParent: $('#addProductModal')
    });

    // Creation type change
    $('input[name="creation_type"]').on('change', function() {
        const creationType = $(this).val();
        
        $('.creation-form').hide();
        
        if (creationType === 'from_order') {
            $('#fromOrderForm').show();
        } else {
            $('#manualForm').show();
        }
    });

    // Transfer order selection change
    $('#transfer_order_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        
        if ($(this).val()) {
            $('#detail-from-warehouse').text(selectedOption.data('from-warehouse'));
            $('#detail-to-warehouse').text(selectedOption.data('to-warehouse'));
            $('#detail-transit-warehouse').text(selectedOption.data('transit-warehouse') || '-');
            $('#detail-created-at').text(selectedOption.data('created-at'));
            $('#view-order-link').attr('href', baseUrl + 'admin/transfer-orders/' + $(this).val());
            $('#orderDetails').show();
        } else {
            $('#orderDetails').hide();
        }
    });

    // Enable add product button when warehouses are selected
    $('#from_warehouse_id_manual, #to_warehouse_id_manual').on('change', function() {
        const fromWarehouse = $('#from_warehouse_id_manual').val();
        const toWarehouse = $('#to_warehouse_id_manual').val();
        
        if (fromWarehouse && toWarehouse) {
            $('#addProductBtn').prop('disabled', false);
        } else {
            $('#addProductBtn').prop('disabled', true);
        }
    });

    // Add product button click
    $('#addProductBtn').on('click', function() {
        updateProductSelect();
        $('#addProductModal').modal('show');
    });

    // Update product select options
    function updateProductSelect() {
        const $select = $('#modal_product_id');
        $select.find('option').each(function() {
            const productId = $(this).val();
            if (productId && selectedProducts.includes(productId)) {
                $(this).hide();
            } else {
                $(this).show();
            }
        });
        $select.trigger('change');
    }

    // Confirm add product
    $('#confirmAddProduct').on('click', function() {
        const productId = $('#modal_product_id').val();
        const quantity = parseFloat($('#modal_quantity').val());
        const notes = $('#modal_notes').val();
        
        if (!productId) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng chọn sản phẩm.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }
        
        if (!quantity || quantity <= 0) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập số lượng hợp lệ.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }
        
        const selectedOption = $('#modal_product_id').find('option:selected');
        addProductToTable(productId, selectedOption.text(), selectedOption.data('code'), selectedOption.data('unit'), quantity, notes);
        
        // Reset modal
        $('#addProductForm')[0].reset();
        $('#modal_product_id').val('').trigger('change');
        $('#addProductModal').modal('hide');
    });

    // Add product to table
    function addProductToTable(productId, productName, productCode, productUnit, quantity, notes) {
        selectedProducts.push(productId);
        
        // Remove no products row if exists
        $('#noProductsRow').remove();
        
        const row = `
            <tr data-product-id="${productId}">
                <td>
                    ${productName}
                    <input type="hidden" name="items[${productIndex}][product_id]" value="${productId}">
                </td>
                <td>${productCode}</td>
                <td>${productUnit}</td>
                <td>
                    <input type="number" name="items[${productIndex}][quantity]" class="form-control quantity-input" 
                           value="${quantity}" min="0.01" step="0.01" required>
                </td>
                <td>
                    <input type="text" name="items[${productIndex}][notes]" class="form-control" value="${notes}" placeholder="Ghi chú...">
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-product-btn">
                        <i class="ri-delete-bin-7-line"></i>
                    </button>
                </td>
            </tr>
        `;
        
        $('#productsTable tbody').append(row);
        productIndex++;
        
        updateProductSelect();
    }

    // Remove product from table
    $(document).on('click', '.remove-product-btn', function() {
        const $row = $(this).closest('tr');
        const productId = $row.data('product-id').toString();
        
        // Remove from selected products
        selectedProducts = selectedProducts.filter(id => id !== productId);
        
        // Remove row
        $row.remove();
        
        // Show no products row if table is empty
        if ($('#productsTable tbody tr').length === 0) {
            $('#productsTable tbody').append(`
                <tr id="noProductsRow">
                    <td colspan="6" class="text-center text-muted">Chưa có sản phẩm nào được thêm</td>
                </tr>
            `);
        }
        
        // Update product select
        updateProductSelect();
        
        // Reindex form inputs
        reindexFormInputs();
    });

    // Reindex form inputs after removing items
    function reindexFormInputs() {
        $('#productsTable tbody tr[data-product-id]').each(function(index) {
            $(this).find('input[name*="[product_id]"]').attr('name', `items[${index}][product_id]`);
            $(this).find('input[name*="[quantity]"]').attr('name', `items[${index}][quantity]`);
            $(this).find('input[name*="[notes]"]').attr('name', `items[${index}][notes]`);
        });
        productIndex = $('#productsTable tbody tr[data-product-id]').length;
    }

    // Form submissions
    $('#transferReceiptFromOrderForm').on('submit', function(e) {
        e.preventDefault();
        
        if (!$('#transfer_order_id').val()) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng chọn phiếu chuyển hàng.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }
        
        submitForm($(this));
    });

    $('#transferReceiptManualForm').on('submit', function(e) {
        e.preventDefault();
        
        // Validate products
        if ($('#productsTable tbody tr[data-product-id]').length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng thêm ít nhất một sản phẩm.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }
        
        // Validate warehouses
        const fromWarehouse = $('#from_warehouse_id_manual').val();
        const toWarehouse = $('#to_warehouse_id_manual').val();
        
        if (fromWarehouse === toWarehouse) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Kho nguồn và kho đích không được giống nhau.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }
        
        submitForm($(this));
    });

    function submitForm($form) {
        showLoading();
        
        $.ajax({
            url: $form.attr('action'),
            type: $form.attr('method'),
            data: $form.serialize(),
            success: function(response) {
                hideLoading();
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        text: response.message,
                        customClass: {
                            confirmButton: 'btn btn-success'
                        }
                    }).then(function() {
                        if (response.redirect) {
                            window.location.href = response.redirect;
                        }
                    });
                }
            },
            error: function(xhr) {
                hideLoading();
                const response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi tạo phiếu nhận hàng.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    }
});
