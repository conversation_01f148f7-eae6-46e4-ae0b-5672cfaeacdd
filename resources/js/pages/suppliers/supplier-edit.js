/**
 * Supplier Management - Edit
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
    (function () {
        // Initialize variables
        const supplierForm = document.getElementById('supplierForm');
        const submitBtn = document.getElementById('submitBtn');
        const supplierId = document.querySelector('input[name="id"]').value;

        // Form validation
        const fv = FormValidation.formValidation(supplierForm, {
            fields: {
                name: {
                    validators: {
                        notEmpty: {
                            message: 'Vui lòng nhập tên nhà cung cấp'
                        },
                        stringLength: {
                            max: 255,
                            message: 'Tên nhà cung cấp không được vượt quá 255 ký tự'
                        }
                    }
                },
                code: {
                    validators: {
                        stringLength: {
                            max: 50,
                            message: 'Mã nhà cung cấp không được vượt quá 50 ký tự'
                        }
                    }
                },
                contact_person: {
                    validators: {
                        stringLength: {
                            max: 255,
                            message: 'T<PERSON><PERSON> người liên hệ không được vượt quá 255 ký tự'
                        }
                    }
                },
                email: {
                    validators: {
                        emailAddress: {
                            message: '<PERSON><PERSON> không hợp lệ'
                        },
                        stringLength: {
                            max: 255,
                            message: 'Email không được vượt quá 255 ký tự'
                        }
                    }
                },
                phone: {
                    validators: {
                        stringLength: {
                            max: 20,
                            message: 'Số điện thoại không được vượt quá 20 ký tự'
                        }
                    }
                },
                tax_code: {
                    validators: {
                        stringLength: {
                            max: 50,
                            message: 'Mã số thuế không được vượt quá 50 ký tự'
                        }
                    }
                },
                address: {
                    validators: {
                        stringLength: {
                            max: 1000,
                            message: 'Địa chỉ không được vượt quá 1000 ký tự'
                        }
                    }
                },
                notes: {
                    validators: {
                        stringLength: {
                            max: 1000,
                            message: 'Ghi chú không được vượt quá 1000 ký tự'
                        }
                    }
                }
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                    eleValidClass: '',
                    rowSelector: '.col-md-6, .col-md-12'
                }),
                submitButton: new FormValidation.plugins.SubmitButton(),
                autoFocus: new FormValidation.plugins.AutoFocus()
            }
        }).on('core.form.valid', function () {
            // Submit form
            updateSupplier();
        });

        // Submit button click handler
        if (submitBtn) {
            submitBtn.addEventListener('click', function () {
                fv.validate();
            });
        }

        // Function to update supplier
        function updateSupplier() {
            const formData = new FormData(supplierForm);
            formData.append('is_active', document.getElementById('is_active').checked ? 1 : 0);
            formData.append('_method', 'PATCH');

            // Convert FormData to object
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });

            // Add CSRF token
            data._token = document.querySelector('meta[name="csrf-token"]').content;

            $.ajax({
                url: `/admin/suppliers/${supplierId}/update`,
                type: 'POST',
                data: data,
                beforeSend: function () {
                    // Show loading
                    showLoading();
                },
                success: function (response) {
                    hideLoading();
                    if (response.code === 200) {
                        Swal.fire({
                            title: 'Thành công!',
                            text: response.text,
                            icon: 'success',
                            customClass: {
                                confirmButton: 'btn btn-success'
                            }
                        }).then(function () {
                            if (response.redirect) {
                                window.location.href = response.redirect;
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Lỗi!',
                            text: response.text,
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                },
                error: function (error) {
                    hideLoading();
                    let errorMessage = 'Có lỗi xảy ra khi cập nhật nhà cung cấp. Vui lòng thử lại sau.';
                    Swal.fire({
                        title: 'Lỗi!',
                        html: errorMessage,
                        icon: 'error',
                        customClass: {
                            confirmButton: 'btn btn-primary'
                        }
                    });
                }
            });
        }
    })();
});
