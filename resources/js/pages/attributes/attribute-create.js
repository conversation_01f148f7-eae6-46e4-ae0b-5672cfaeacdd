/**
 * Attribute Management - Create
 */

'use strict';

document.addEventListener('DOMContentLoaded', function () {
    (function () {
        // Form validation
        const attributeForm = document.getElementById('attributeForm');

        // Form validation rules
        const fv = FormValidation.formValidation(attributeForm, {
            fields: {
                name: {
                    validators: {
                        notEmpty: {
                            message: 'Vui lòng nhập tên thuộc tính'
                        },
                        stringLength: {
                            min: 2,
                            max: 255,
                            message: 'Tên thuộc tính phải có từ 2 đến 255 ký tự'
                        }
                    }
                },
                code: {
                    validators: {
                        stringLength: {
                            max: 100,
                            message: 'Mã thuộc tính không được vượt quá 100 ký tự'
                        },
                        regexp: {
                            regexp: /^[a-z0-9_-]*$/,
                            message: 'M<PERSON> thuộc tính chỉ được chứa chữ cái thường, số, gạch ngang và gạch dưới'
                        }
                    }
                },
                display_name: {
                    validators: {
                        stringLength: {
                            max: 255,
                            message: 'Tên hiển thị không được vượt quá 255 ký tự'
                        }
                    }
                },
                type: {
                    validators: {
                        notEmpty: {
                            message: 'Vui lòng chọn loại thuộc tính'
                        }
                    }
                },
                description: {
                    validators: {
                        stringLength: {
                            max: 1000,
                            message: 'Mô tả không được vượt quá 1000 ký tự'
                        }
                    }
                },
                sort_order: {
                    validators: {
                        numeric: {
                            message: 'Thứ tự sắp xếp phải là số'
                        },
                        greaterThanOrEqual: {
                            min: 0,
                            message: 'Thứ tự sắp xếp phải lớn hơn hoặc bằng 0'
                        }
                    }
                }
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                    eleValidClass: '',
                    rowSelector: '.col-md-6, .col-md-12'
                }),
                submitButton: new FormValidation.plugins.SubmitButton(),
                autoFocus: new FormValidation.plugins.AutoFocus()
            }
        }).on('core.form.valid', function () {
            // Get form data
            const formData = {
                name: document.getElementById('name').value,
                code: document.getElementById('code').value,
                display_name: document.getElementById('display_name').value,
                type: document.getElementById('type').value,
                description: document.getElementById('description').value,
                sort_order: document.getElementById('sort_order').value,
                is_required: document.getElementById('is_required').checked,
                is_filterable: document.getElementById('is_filterable').checked,
                is_active: document.getElementById('is_active').checked
            };

            // Convert boolean values to actual boolean
            formData.is_required = formData.is_required === true ? 1 : 0;
            formData.is_filterable = formData.is_filterable === true ? 1 : 0;
            formData.is_active = formData.is_active === true ? 1 : 0;

            // Submit form data
            $.ajax({
                url: '/admin/attributes/store',
                type: 'POST',
                data: formData,
                success: function (response) {
                    if (response.title === 'success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Thành công!',
                            text: response.text,
                            customClass: {
                                confirmButton: 'btn btn-success'
                            }
                        }).then(function () {
                            window.location.href = response.redirect;
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.text,
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                },
                error: function (xhr) {
                    let errorMessage = 'Đã xảy ra lỗi khi tạo thuộc tính.';

                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        errorMessage = Object.values(xhr.responseJSON.errors).flat().join('<br>');
                    } else if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi!',
                        html: errorMessage,
                        customClass: {
                            confirmButton: 'btn btn-primary'
                        }
                    });
                }
            });
        });

        // Type change handler
        document.getElementById('type').addEventListener('change', function () {
            const type = this.value;
            const isRequiredField = document.getElementById('is_required');
            const isFilterableField = document.getElementById('is_filterable');

            // Enable/disable fields based on type
            if (type === 'boolean') {
                // Boolean type is always required and filterable
                isRequiredField.checked = true;
                isRequiredField.disabled = true;

                isFilterableField.checked = true;
                isFilterableField.disabled = true;
            } else {
                isRequiredField.disabled = false;
                isFilterableField.disabled = false;
            }
        });
    })();
});
