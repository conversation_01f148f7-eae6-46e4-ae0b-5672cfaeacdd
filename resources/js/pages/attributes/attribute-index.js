/**
 * Attribute Management - Index
 */

'use strict';

// Datatable (jquery)
$(function () {
    let statusFilter = 'all';
    let typeFilter = 'all';

    // Initialize datatable
    let attributeTable = $('.datatables-attributes').DataTable({
        ajax: {
            url: '/admin/attributes/datatable',
            type: 'POST',
            data: function (d) {
                d.status = statusFilter;
                d.type = typeFilter;
                d._token = $('meta[name="csrf-token"]').attr('content');
                return d;
            },
            error: function (xhr, error, thrown) {
                console.error('DataTables error:', error, thrown);
                console.log('Server response:', xhr.responseText);
            }
        },
        columns: [
            {data: ''}, // Responsive control column
            {data: 'name'},
            {data: 'code'},
            {data: 'type'},
            {data: 'is_required'},
            {data: 'is_filterable'},
            {data: 'is_active'},
            {data: 'sort_order'},
            {data: 'actions'}
        ],
        processing: true,
        serverSide: true,
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                searchable: false,
                responsivePriority: 2,
                targets: 0,
                render: function () {
                    return '';
                }
            },
            {
                targets: 1,
                responsivePriority: 1,
                render: function (data, typeParam, full) {
                    return `<span class="fw-medium">${data}</span>`;
                }
            },
            {
                targets: 3, // Type column
                render: function (data, typeParam, full) {
                    const typeMap = {
                        'select': {title: 'Lựa chọn', class: 'bg-label-info'},
                        'text': {title: 'Văn bản', class: 'bg-label-primary'},
                        'number': {title: 'Số', class: 'bg-label-success'},
                        'boolean': {title: 'Boolean', class: 'bg-label-warning'},
                        'date': {title: 'Ngày tháng', class: 'bg-label-danger'}
                    };

                    const typeInfo = typeMap[data] || {title: data, class: 'bg-label-secondary'};

                    return `<span class="badge ${typeInfo.class}">${typeInfo.title}</span>`;
                }
            },
            {
                targets: 4, // Required column
                render: function (data, typeParam, full) {
                    return data ? '<span class="badge bg-label-success">Có</span>' : '<span class="badge bg-label-secondary">Không</span>';
                }
            },
            {
                targets: 5, // Filterable column
                render: function (data, typeParam, full) {
                    return data ? '<span class="badge bg-label-success">Có</span>' : '<span class="badge bg-label-secondary">Không</span>';
                }
            },
            {
                targets: 6, // Status column
                render: function (data, typeParam, full) {
                    return data
                        ? '<span class="badge bg-label-success">Đang hoạt động</span>'
                        : '<span class="badge bg-label-danger">Không hoạt động</span>';
                }
            },
            {
                targets: -1, // Actions column
                title: 'Thao tác',
                orderable: false,
                searchable: false,
                render: function (data, typeParam, full) {
                    return `
            <div class="d-inline-block">
              <button type="button" class="btn btn-sm btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                <i class="ri-more-fill"></i>
              </button>
              <div class="dropdown-menu dropdown-menu-end">
                <a href="/admin/attributes/${full.id}" class="dropdown-item">
                  <i class="ri-eye-line me-1"></i> Xem
                </a>
                <a href="/admin/attributes/edit/${full.id}" class="dropdown-item">
                  <i class="ri-edit-line me-1"></i> Chỉnh sửa
                </a>
                <a href="javascript:void(0);" class="dropdown-item text-danger delete-record" data-id="${full.id}">
                  <i class="ri-delete-bin-line me-1"></i> Xóa
                </a>
              </div>
            </div>
          `;
                }
            }
        ],
        order: [[1, 'asc']], // Order by name
        dom: '<"row me-2"<"col-md-2"<"me-3"l>><"col-md-10"<"dt-action-buttons text-xl-end text-lg-start text-md-end text-start d-flex align-items-center justify-content-end flex-md-row flex-column mb-3 mb-md-0"fB>>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
        language: {
            search: '',
            searchPlaceholder: 'Tìm kiếm...',
            lengthMenu: '_MENU_',
            paginate: {
                first: 'Đầu',
                last: 'Cuối',
                next: 'Sau',
                previous: 'Trước'
            },
            info: 'Hiển thị _START_ đến _END_ của _TOTAL_ bản ghi',
            infoEmpty: 'Hiển thị 0 đến 0 của 0 bản ghi',
            infoFiltered: '(lọc từ _MAX_ bản ghi)',
            zeroRecords: 'Không tìm thấy bản ghi nào phù hợp',
            emptyTable: 'Không có dữ liệu'
        },
        responsive: {
            details: {
                display: $.fn.dataTable.Responsive.display.modal({
                    header: function (row) {
                        const data = row.data();
                        return 'Chi tiết thuộc tính';
                    }
                }),
                type: 'column',
                renderer: function (api, rowIdx, columns) {
                    const data = $.map(columns, function (col, i) {
                        return col.title !== '' && col.hidden
                            ? `<tr data-dt-row="${col.rowIndex}" data-dt-column="${col.columnIndex}">
                  <td>${col.title}:</td>
                  <td>${col.data}</td>
                </tr>`
                            : '';
                    }).join('');

                    return data ? $('<table class="table"/><tbody />').append(data) : false;
                }
            }
        }
    });

    // Filter by status
    $('.dropdown-menu a[data-status]').on('click', function () {
        statusFilter = $(this).data('status');
        attributeTable.ajax.reload();
    });

    // Filter by type
    $('.dropdown-menu a[data-type]').on('click', function () {
        typeFilter = $(this).data('type');
        attributeTable.ajax.reload();
    });

    // Delete attribute
    $(document).on('click', '.delete-record', function () {
        const attributeId = $(this).data('id');

        Swal.fire({
            title: 'Xác nhận xóa?',
            text: "Bạn có chắc chắn muốn xóa thuộc tính này? Dữ liệu đã xóa không thể khôi phục!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-danger me-3',
                cancelButton: 'btn btn-label-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                // Delete the attribute
                $.ajax({
                    url: `/admin/attributes/delete/${attributeId}`,
                    type: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        if (response.title === 'success') {
                            attributeTable.ajax.reload();

                            Swal.fire({
                                icon: 'success',
                                title: 'Đã xóa!',
                                text: response.text,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Lỗi!',
                                text: response.text,
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                }
                            });
                        }
                    },
                    error: function (xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: 'Đã xảy ra lỗi khi xóa thuộc tính.',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            }
        });
    });
});
