/**
 * Add new role Modal JS
 */

'use strict';

document.addEventListener('DOMContentLoaded', function (e) {
    (function () {
        const CreateForm = document.getElementById('addRoleForm');
        // add role form validation
        FormValidation.formValidation(CreateForm, {
            fields: {
                roleName: {
                    validators: {
                        notEmpty: {
                            message: 'Please enter role name'
                        }
                    }
                },
                rolePermissions: {
                    validators: {
                        notEmpty: {
                            message: 'Please select permissions'
                        }
                    }
                }
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                    // Use this for enabling/changing valid/invalid class
                    // eleInvalidClass: '',
                    eleValidClass: '',
                    rowSelector: '.col-12'
                }),
                submitButton: new FormValidation.plugins.SubmitButton(),
                // Submit the form when all fields are valid
                // defaultSubmit: new FormValidation.plugins.DefaultSubmit(),
                autoFocus: new FormValidation.plugins.AutoFocus()
            }
        }).on('core.form.valid', function () {
            const permissions = $.map($('input[name="rolePermissions"]:checked'), function (c) {
                return c.value;
            });
            // adding or updating roles when form successfully validate
            $.ajax({
                data: {
                    'name': $('#roleName').val(),
                    'permissions': permissions
                },
                url: `${baseUrl}admin/roles/store`,
                type: 'POST',
                success: function (data) {
                    // sweetalert
                    Swal.fire({
                        icon: 'success',
                        title: data.title,
                        text: data.text,
                        customClass: {
                            confirmButton: 'btn btn-success'
                        }
                    }).then(function () {
                        window.location.href = `${baseUrl}admin/roles`;
                    });
                },
                error: function (err) {
                    Swal.fire({
                        title: err.responseJSON.errors,
                        text: err.responseJSON.message,
                        icon: 'error',
                        customClass: {
                            confirmButton: 'btn btn-success'
                        }
                    });
                }
            });
        });

        // Select All checkbox click
        const selectAll = document.querySelector('#selectAll'),
            checkboxList = document.querySelectorAll('[type="checkbox"]');
        selectAll.addEventListener('change', t => {
            checkboxList.forEach(e => {
                e.checked = t.target.checked;
            });
        });
    })();
});
