$(function () {
    $(document).on('click', '#deleteRole', function () {
        const roleId = $(this).data('id');
        Swal.fire({
            title: 'Bạn chắc chưa ?',
            text: "<PERSON>ạn sẽ không thể hoàn nguyên điều này!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Ok, Xoá luôn !',
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger ms-1'
            }
        }).then(function (result) {
            if (result.value) {
                $.ajax({
                    url: `${baseUrl}admin/roles/delete/${roleId}`,
                    type: 'DELETE',
                    success: function (response) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Đã xoá',
                            text: 'Đã xoá thành công',
                            customClass: {
                                confirmButton: 'btn btn-success'
                            }
                        }).then(function (result) {
                            window.location.reload()
                        })
                    }
                })
            }
        });
    })
})
