{"menu": [{"name": "menu.dashboard", "icon": "menu-icon tf-icons ri-home-smile-line", "slug": "dashboard", "can": "dashboard.view", "submenu": [{"url": "app/ecommerce/dashboard", "name": "eCommerce", "slug": "app-ecommerce-dashboard", "active": "app-ecommerce-dashboard*"}]}, {"name": "menu.products", "icon": "menu-icon tf-icons ri-shopping-bag-3-line", "slug": "products", "can": "products.list", "submenu": [{"url": "admin/products", "name": "menu.list", "slug": "products.list", "can": "products.list", "active": "products.list*"}, {"url": "admin/products/create", "name": "menu.products.create", "slug": "products.create", "can": "products.create", "active": "products.create*"}, {"url": "admin/categories", "name": "menu.categories", "slug": "categories.list", "can": "categories.list", "active": "categories*"}, {"url": "admin/brands", "name": "menu.brands", "slug": "brands.list", "can": "brands.list", "active": "brands*"}, {"url": "admin/products/import", "name": "menu.products.import", "slug": "products.import", "can": "products.import", "active": "products.import*"}, {"url": "admin/products/export", "name": "menu.products.export", "slug": "products.export", "can": "products.export", "active": "products.export*"}, {"url": "admin/attributes", "name": "menu.attributes", "slug": "attributes.list", "can": "attributes.list", "active": "attributes*"}]}, {"name": "menu.warehouses", "icon": "menu-icon tf-icons ri-store-2-line", "slug": ["warehouses", "warehouses.list", "warehouses.create", "warehouses.edit", "warehouses.areas.list", "warehouses.inventory", "warehouses.transactions", "warehouses.reports", "warehouses.purchase-orders", "warehouses.good-receipts", "transfer-requests", "transfer-orders", "transfer-receipts"], "can": "warehouses.list", "submenu": [{"name": "menu.warehouses.purchase_management", "slug": ["warehouses.purchase-orders.list", "warehouses.purchase-orders.show", "warehouses.purchase-orders.create", "warehouses.purchase-orders.edit", "warehouses.purchase-orders.approve", "warehouses.good-receipts.edit", "warehouses.good-receipts.index", "warehouses.good-receipts.show", "warehouses.good-receipts*"], "icon": "menu-icon tf-icons ri-shopping-cart-line", "submenu": [{"url": "admin/warehouses/purchase-orders", "name": "menu.warehouses.purchase_orders", "slug": "warehouses.purchase-orders.view", "can": "warehouses.purchase-orders.view", "active": "warehouses.purchase-orders*"}, {"url": "admin/warehouses/good-receipts", "name": "menu.warehouses.good_receipts", "slug": "warehouses.good-receipts.view", "can": "warehouses.good-receipts.view", "active": ["warehouses.good-receipts.index", "warehouses.good-receipts.show", "warehouses.good-receipts*"]}]}, {"name": "menu.warehouses.inventory_management", "slug": ["warehouses.inventory.view", "warehouses.transactions.index", "warehouses.transactions.show", "warehouses.transactions.datatable"], "icon": "menu-icon tf-icons ri-stack-line", "submenu": [{"url": "admin/warehouses/inventory", "name": "menu.warehouses.inventory", "slug": "warehouses.inventory.view", "can": "warehouses.inventory.view", "active": "warehouses.inventory*"}, {"url": "admin/warehouses/transactions", "name": "menu.warehouses.transactions", "slug": "warehouses.transactions.view", "can": "warehouses.transactions.view", "active": "warehouses.transactions*"}]}, {"name": "menu.warehouses.transfer_management", "slug": ["transfer-requests", "transfer-orders", "transfer-receipts"], "icon": "menu-icon tf-icons ri-truck-line", "submenu": [{"url": "admin/transfer-requests", "name": "menu.warehouses.transfer_requests", "slug": "transfer-requests.list", "can": "transfer-requests.list", "active": "transfer-requests*"}, {"url": "admin/transfer-orders", "name": "menu.warehouses.transfer_orders", "slug": "transfer-orders.list", "can": "transfer-orders.list", "active": "transfer-orders*"}, {"url": "admin/transfer-receipts", "name": "menu.warehouses.transfer_receipts", "slug": "transfer-receipts.list", "can": "transfer-receipts.list", "active": "transfer-receipts*"}]}, {"name": "menu.warehouses.management", "slug": ["warehouses.list", "warehouses.create", "warehouses.edit", "warehouses.areas.list"], "icon": "menu-icon tf-icons ri-building-line", "submenu": [{"url": "admin/warehouses", "name": "menu.list", "slug": "warehouses.list", "can": "warehouses.list"}, {"url": "admin/warehouses/create", "name": "menu.warehouses.create", "slug": "warehouses.create", "can": "warehouses.create"}, {"url": "admin/warehouses/areas", "name": "menu.warehouses.areas", "slug": "warehouses.areas.list", "can": "warehouses.areas.list", "active": "warehouses.areas*"}]}, {"name": "menu.warehouses.reports_analytics", "slug": ["warehouses.reports.view", "warehouses.reports.export"], "icon": "menu-icon tf-icons ri-bar-chart-line", "submenu": [{"url": "admin/warehouses/reports", "name": "menu.warehouses.reports", "slug": "warehouses.reports.view", "can": "warehouses.reports.view", "active": "warehouses.reports*"}]}]}, {"name": "menu.suppliers", "icon": "menu-icon tf-icons ri-user-star-line", "slug": "suppliers", "can": "suppliers.list", "submenu": [{"url": "admin/suppliers", "name": "suppliers.list", "slug": "suppliers.list", "can": "suppliers.list", "active": "suppliers.list*"}, {"url": "admin/suppliers/create", "name": "suppliers.create", "slug": "suppliers.create", "can": "suppliers.create", "active": "suppliers.create*"}]}, {"name": "menu.settings", "icon": "menu-icon tf-icons ri-settings-2-line", "slug": "settings", "can": "settings.list", "submenu": [{"url": "admin/settings/general", "name": "menu.settings.general", "slug": "settings.list", "can": "settings.list", "active": "settings.list*"}, {"url": "admin/settings/authorize", "name": "menu.settings.authorize", "slug": "settings.authorize", "can": "settings.authorize", "active": "settings.authorize*"}, {"url": "admin/settings/health", "name": "menu.settings.health", "slug": "settings.health", "can": "settings.health", "active": "settings.health*"}, {"url": "admin/log-viewer", "name": "menu.settings.logs", "slug": "settings.logs", "can": "settings.logs", "target": "_blank", "active": "settings.logs*"}, {"url": "admin/activity-logs", "name": "menu.activity-logs", "slug": "activity-logs.list", "can": "activity-logs.list", "active": "activity-logs*"}, {"url": "/pulse", "name": "menu.settings.monitor", "slug": "pulse", "can": "settings.list", "active": "pulse*"}]}, {"name": "menu.users", "icon": "menu-icon tf-icons ri-user-line", "slug": "users", "can": "users.list", "submenu": [{"url": "admin/users", "name": "menu.list", "slug": "users.list", "active": "users.list*"}, {"name": "View", "slug": "user-view", "active": "user-view*", "submenu": [{"url": "/user/view/account", "name": "Account", "slug": "user-view-account", "active": "user-view-account*"}, {"url": "/user/view/security", "name": "Security", "slug": "user-view-security", "active": "user-view-security*"}, {"url": "/user/view/billing", "name": "Billing & Plans", "slug": "user-view-billing", "active": "user-view-billing*"}, {"url": "/user/view/notifications", "name": "Notifications", "slug": "user-view-notifications", "active": "user-view-notifications*"}, {"url": "/user/view/connections", "name": "Connections", "slug": "user-view-connections", "active": "user-view-connections*"}]}]}, {"name": "menu.roles_permissions", "icon": "menu-icon tf-icons ri-lock-2-line", "slug": "roles", "can": "roles.list", "submenu": [{"url": "admin/roles", "name": "menu.list", "slug": "roles.list", "active": "roles.list*"}, {"url": "admin/roles/create", "name": "menu.roles_create", "slug": "roles.create", "active": "roles.create*"}]}]}