<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

echo "=== Testing TransferRequestRepository Specifically ===\n";

try {
    // Test the original problematic repository
    echo "1. Testing direct instantiation:\n";
    $model = new \App\Models\TransferRequest();
    $repository = new \App\Repositories\TransferRequestRepository($model);
    echo "✅ TransferRequestRepository constructor: OK\n";

    // Test calling parent methods
    echo "\n2. Testing parent methods:\n";
    try {
        // This should work now since parent::__construct() is called properly
        $result = $repository->getAll();
        echo "✅ getAll() method: OK\n";
    } catch (Exception $e) {
        echo "❌ getAll() method: FAIL - " . $e->getMessage() . "\n";
    }

    // Test specific methods
    echo "\n3. Testing specific methods:\n";
    try {
        $result = $repository->getAllWithPagination(10);
        echo "✅ getAllWithPagination() method: OK\n";
    } catch (Exception $e) {
        echo "❌ getAllWithPagination() method: FAIL - " . $e->getMessage() . "\n";
    }

    echo "\n=== Test Completed Successfully ===\n";
    echo "The 'Cannot call constructor' error has been fixed!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
