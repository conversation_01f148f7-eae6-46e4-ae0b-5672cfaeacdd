<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

echo "=== Testing Transfer System ===\n";

try {
    // Test models
    echo "1. Testing Models:\n";
    echo "   - TransferRequest: " . (class_exists('App\Models\TransferRequest') ? 'OK' : 'FAIL') . "\n";
    echo "   - TransferOrder: " . (class_exists('App\Models\TransferOrder') ? 'OK' : 'FAIL') . "\n";
    echo "   - TransferReceipt: " . (class_exists('App\Models\TransferReceipt') ? 'OK' : 'FAIL') . "\n";

    // Test repositories
    echo "\n2. Testing Repositories:\n";
    try {
        $transferRequestRepo = $app->make('App\Contracts\TransferRequestInterface');
        echo "   - TransferRequestRepository: OK\n";
    } catch (Exception $e) {
        echo "   - TransferRequestRepository: FAIL - " . $e->getMessage() . "\n";
    }

    try {
        $transferOrderRepo = $app->make('App\Contracts\TransferOrderInterface');
        echo "   - TransferOrderRepository: OK\n";
    } catch (Exception $e) {
        echo "   - TransferOrderRepository: FAIL - " . $e->getMessage() . "\n";
    }

    try {
        $transferReceiptRepo = $app->make('App\Contracts\TransferReceiptInterface');
        echo "   - TransferReceiptRepository: OK\n";
    } catch (Exception $e) {
        echo "   - TransferReceiptRepository: FAIL - " . $e->getMessage() . "\n";
    }

    // Test database tables
    echo "\n3. Testing Database Tables:\n";
    try {
        $transferRequestCount = \App\Models\TransferRequest::count();
        echo "   - transfer_requests table: OK (count: $transferRequestCount)\n";
    } catch (Exception $e) {
        echo "   - transfer_requests table: FAIL - " . $e->getMessage() . "\n";
    }

    try {
        $transferOrderCount = \App\Models\TransferOrder::count();
        echo "   - transfer_orders table: OK (count: $transferOrderCount)\n";
    } catch (Exception $e) {
        echo "   - transfer_orders table: FAIL - " . $e->getMessage() . "\n";
    }

    try {
        $transferReceiptCount = \App\Models\TransferReceipt::count();
        echo "   - transfer_receipts table: OK (count: $transferReceiptCount)\n";
    } catch (Exception $e) {
        echo "   - transfer_receipts table: FAIL - " . $e->getMessage() . "\n";
    }

    // Test permissions
    echo "\n4. Testing Permissions:\n";
    try {
        $transferPermissions = \Spatie\Permission\Models\Permission::where('name', 'like', 'transfer%')->count();
        echo "   - Transfer permissions: OK (count: $transferPermissions)\n";
    } catch (Exception $e) {
        echo "   - Transfer permissions: FAIL - " . $e->getMessage() . "\n";
    }

    echo "\n=== Test Completed Successfully ===\n";

} catch (Exception $e) {
    echo "FATAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
