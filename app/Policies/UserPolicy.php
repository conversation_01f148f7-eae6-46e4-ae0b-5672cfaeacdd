<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
  use HandlesAuthorization;

  public function list(User $user)
  {
    if ($user->can('users.list')) {
      return true;
    }
  }

  public function create(User $user)
  {
    return ($user->can('users.create'));
  }

  public function update(User $user)
  {
    if ($user->can('users.edit')) {
      return true;
    }
  }

  public function delete(User $user)
  {
    if ($user->can('users.delete')) {
      return true;
    }
  }
}
