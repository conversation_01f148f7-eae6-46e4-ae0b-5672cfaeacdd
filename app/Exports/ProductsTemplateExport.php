<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class ProductsTemplateExport implements FromArray, WithHeadings, WithStyles, ShouldAutoSize
{
    /**
     * @return array
     */
    public function array(): array
    {
        // Dữ liệu mẫu
        return [
            [
                'SP001', 
                'Sản phẩm mẫu 1', 
                '<PERSON>ô tả sản phẩm mẫu 1', 
                'Điện tử', 
                'Samsung', 
                'Cái', 
                '1000000', 
                '800000', 
                '10', 
                '8938505974194', 
                '<PERSON> số l<PERSON>', 
                '<PERSON><PERSON><PERSON> động',
                '1.5',
                '20',
                '15',
                '10'
            ],
            [
                'SP002', 
                '<PERSON>ản phẩm mẫu 2', 
                '<PERSON><PERSON> tả sản phẩm mẫu 2', 
                'Thời trang', 
                'Nike', 
                'Đôi', 
                '500000', 
                '300000', 
                '8', 
                '8938505974195', 
                'Theo serial', 
                'Hoạt động',
                '0.5',
                '30',
                '20',
                '10'
            ],
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ma_san_pham',
            'ten_san_pham',
            'mo_ta',
            'danh_muc',
            'thuong_hieu',
            'don_vi',
            'gia_ban',
            'gia_von',
            'thue',
            'ma_vach',
            'kieu_theo_doi_ton_kho',
            'trang_thai',
            'trong_luong',
            'chieu_dai',
            'chieu_rong',
            'chieu_cao'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        // Định dạng tiêu đề
        $sheet->getStyle('A1:P1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4F6D7A'],
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // Định dạng dữ liệu mẫu
        $sheet->getStyle('A2:P3')->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
            'alignment' => [
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // Định dạng cột số
        $sheet->getStyle('G2:I3')->getNumberFormat()->setFormatCode('#,##0');
        $sheet->getStyle('M2:P3')->getNumberFormat()->setFormatCode('#,##0.00');

        // Thêm ghi chú cho các cột
        $sheet->getComment('A1')->getText()->createTextRun('Mã sản phẩm (bắt buộc): Mã duy nhất của sản phẩm');
        $sheet->getComment('B1')->getText()->createTextRun('Tên sản phẩm (bắt buộc): Tên đầy đủ của sản phẩm');
        $sheet->getComment('C1')->getText()->createTextRun('Mô tả: Thông tin chi tiết về sản phẩm');
        $sheet->getComment('D1')->getText()->createTextRun('Danh mục: Tên danh mục sản phẩm (phải tồn tại trong hệ thống)');
        $sheet->getComment('E1')->getText()->createTextRun('Thương hiệu: Tên thương hiệu sản phẩm (phải tồn tại trong hệ thống)');
        $sheet->getComment('F1')->getText()->createTextRun('Đơn vị: Đơn vị tính của sản phẩm (cái, chiếc, kg, ...)');
        $sheet->getComment('G1')->getText()->createTextRun('Giá bán: Giá bán của sản phẩm');
        $sheet->getComment('H1')->getText()->createTextRun('Giá vốn: Giá nhập của sản phẩm');
        $sheet->getComment('I1')->getText()->createTextRun('Thuế: Phần trăm thuế áp dụng cho sản phẩm');
        $sheet->getComment('J1')->getText()->createTextRun('Mã vạch: Mã vạch/barcode của sản phẩm');
        $sheet->getComment('K1')->getText()->createTextRun('Kiểu theo dõi tồn kho: "Theo số lượng", "Theo serial", hoặc "Theo lô"');
        $sheet->getComment('L1')->getText()->createTextRun('Trạng thái: "Hoạt động" hoặc "Không hoạt động"');
        $sheet->getComment('M1')->getText()->createTextRun('Trọng lượng: Trọng lượng của sản phẩm (kg)');
        $sheet->getComment('N1')->getText()->createTextRun('Chiều dài: Chiều dài của sản phẩm (cm)');
        $sheet->getComment('O1')->getText()->createTextRun('Chiều rộng: Chiều rộng của sản phẩm (cm)');
        $sheet->getComment('P1')->getText()->createTextRun('Chiều cao: Chiều cao của sản phẩm (cm)');

        return [];
    }
}
