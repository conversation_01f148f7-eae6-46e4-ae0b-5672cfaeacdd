<?php

namespace App\Console\Commands;

use App\Enums\Permissions;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class MakePermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:permission {name : The name of the permission (e.g. products.create)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new permission in the Permissions enum';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $permissionName = $this->argument('name');

        // Validate permission name
        if (!preg_match('/^[a-z0-9_]+(\.[a-z0-9_]+)+$/', $permissionName)) {
            $this->error('Permission name must be in format: module.action (e.g. products.create)');
            return 1;
        }

        // Check if permission already exists
        $enumPermissions = Permissions::getAll();
        if (in_array($permissionName, $enumPermissions)) {
            $this->error("Permission '{$permissionName}' already exists!");
            return 1;
        }

        // Create constant name
        $parts = explode('.', $permissionName);
        $constantName = Str::upper(implode('_', $parts));

        // Get Permissions enum file content
        $enumPath = app_path('Enums/Permissions.php');
        $enumContent = File::get($enumPath);

        // Find the last case definition
        preg_match_all('/case ([A-Z0-9_]+) = \'([a-z0-9_.]+)\';/', $enumContent, $matches);
        $lastCase = end($matches[0]);

        // Insert new case after the last one
        $newCase = "  case {$constantName} = '{$permissionName}';";
        $enumContent = str_replace($lastCase, $lastCase . "\n" . $newCase, $enumContent);

        // Update the file
        File::put($enumPath, $enumContent);

        // Add to language files
        $this->updateLanguageFiles($permissionName);

        // Sync permissions
        $this->call('permissions:sync');

        $this->info("Permission '{$permissionName}' created successfully!");
        return 0;
    }

    /**
     * Update language files with new permission
     */
    protected function updateLanguageFiles(string $permissionName): void
    {
        $parts = explode('.', $permissionName);
        $module = $parts[0];
        $action = $parts[1];

        // Get translation for the permission
        $translation = $this->ask("Enter translation for '{$permissionName}' permission", Str::title($action) . ' ' . Str::title($module));

        // Update English language file
        $this->updateLanguageFile('en', $permissionName, $translation);

        // Update Vietnamese language file if exists
        if (File::exists(lang_path('vi/permissions.php'))) {
            $viTranslation = $this->ask("Enter Vietnamese translation for '{$permissionName}' permission", $translation);
            $this->updateLanguageFile('vi', $permissionName, $viTranslation);
        }
    }

    /**
     * Update a specific language file
     */
    protected function updateLanguageFile(string $locale, string $permissionName, string $translation): void
    {
        $langPath = lang_path("{$locale}/permissions.php");
        
        if (!File::exists($langPath)) {
            $this->warn("Language file for '{$locale}' not found. Skipping...");
            return;
        }

        $langContent = File::get($langPath);
        
        // Convert to array
        $permissions = include($langPath);
        
        // Add new permission
        $permissions[$permissionName] = $translation;
        
        // Sort permissions
        ksort($permissions);
        
        // Convert back to PHP code
        $newContent = "<?php\nreturn [\n";
        foreach ($permissions as $key => $value) {
            $newContent .= "  '{$key}' => '{$value}',\n";
        }
        $newContent .= "];";
        
        // Update the file
        File::put($langPath, $newContent);
        
        $this->info("Updated {$locale} language file with '{$permissionName}' translation");
    }
}
