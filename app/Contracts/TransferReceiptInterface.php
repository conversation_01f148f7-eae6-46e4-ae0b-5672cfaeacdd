<?php

namespace App\Contracts;

interface TransferReceiptInterface
{
    /**
     * Get all transfer receipts with pagination
     */
    public function getAllWithPagination($perPage = 15);

    /**
     * Get transfer receipt by ID
     */
    public function getById($id);

    /**
     * Create new transfer receipt
     */
    public function create(array $data);

    /**
     * Update transfer receipt
     */
    public function update($id, array $data);

    /**
     * Delete transfer receipt
     */
    public function delete($id);

    /**
     * Get transfer receipts for datatable
     */
    public function getForDatatable(array $params);

    /**
     * Submit transfer receipt for approval
     */
    public function submitForApproval($id);

    /**
     * Approve transfer receipt
     */
    public function approve($id, $approvedBy);

    /**
     * Reject transfer receipt
     */
    public function reject($id, $rejectedBy, $reason = null);

    /**
     * Cancel transfer receipt
     */
    public function cancel($id);

    /**
     * Create transfer receipt items
     */
    public function createItems($transferReceiptId, array $items);

    /**
     * Update transfer receipt items
     */
    public function updateItems($transferReceiptId, array $items);

    /**
     * Delete transfer receipt items
     */
    public function deleteItems($transferReceiptId, array $itemIds = []);

    /**
     * Create transfer receipt from transfer order
     */
    public function createFromTransferOrder($transferOrderId, array $data);
}
