<?php

namespace App\Contracts;

interface ProductAttributeInterface
{
    /**
     * Get all attributes for a product
     *
     * @param int $productId
     * @return array
     */
    public function getProductAttributes(int $productId);

    /**
     * Add an attribute to a product
     *
     * @param int $productId
     * @param array $data
     * @return array
     */
    public function addProductAttribute(int $productId, array $data);

    /**
     * Update a product attribute
     *
     * @param int $productId
     * @param int $attributeId
     * @param array $data
     * @return array
     */
    public function updateProductAttribute(int $productId, int $attributeId, array $data);

    /**
     * Delete a product attribute
     *
     * @param int $productId
     * @param int $attributeId
     * @return array
     */
    public function deleteProductAttribute(int $productId, int $attributeId);

    /**
     * Batch update product attributes
     *
     * @param int $productId
     * @param array $attributes
     * @return array
     */
    public function batchUpdateProductAttributes(int $productId, array $attributes);

    /**
     * Get all available attributes
     *
     * @return array
     */
    public function getAvailableAttributes();
}
