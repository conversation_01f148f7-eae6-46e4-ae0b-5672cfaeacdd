<?php

namespace App\Contracts;

use Illuminate\Http\Request;

interface PurchaseOrderInterface
{
    /**
     * Get all purchase orders
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAll();

    /**
     * Get purchase order by ID
     *
     * @param int $id
     * @return \App\Models\PurchaseOrder
     */
    public function findById($id);

    /**
     * Create a new purchase order
     *
     * @param array $data
     * @return array
     */
    public function createPurchaseOrder(array $data);

    /**
     * Update purchase order
     *
     * @param int $id
     * @param array $data
     * @return array
     */
    public function updatePurchaseOrder($id, array $data);

    /**
     * Delete purchase order
     *
     * @param int $id
     * @return bool
     */
    public function deletePurchaseOrder($id);

    /**
     * Get purchase orders for datatable
     *
     * @param Request $request
     * @return array
     */
    public function getPurchaseOrdersForDatatable(Request $request);

    /**
     * Get purchase order items
     *
     * @param int $purchaseOrderId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPurchaseOrderItems($purchaseOrderId);

    /**
     * Add item to purchase order
     *
     * @param int $purchaseOrderId
     * @param array $data
     * @return mixed
     */
    public function addPurchaseOrderItem($purchaseOrderId, array $data);

    /**
     * Update purchase order item
     *
     * @param int $purchaseOrderId
     * @param int $itemId
     * @param array $data
     * @return bool
     */
    public function updatePurchaseOrderItem($purchaseOrderId, $itemId, array $data);

    /**
     * Delete purchase order item
     *
     * @param int $purchaseOrderId
     * @param int $itemId
     * @return bool
     */
    public function deletePurchaseOrderItem($purchaseOrderId, $itemId);

    /**
     * Submit purchase order for approval
     *
     * @param int $id
     * @return bool
     */
    public function submitForApproval($id);

    /**
     * Approve purchase order
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function approvePurchaseOrder($id, array $data);

    /**
     * Reject purchase order
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function rejectPurchaseOrder($id, array $data);

    /**
     * Cancel purchase order
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function cancelPurchaseOrder($id, array $data);

    /**
     * Receive purchase order
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function receivePurchaseOrder($id, array $data);

    /**
     * Get purchase order attachments
     *
     * @param int $purchaseOrderId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPurchaseOrderAttachments($purchaseOrderId);

    /**
     * Upload attachment for purchase order
     *
     * @param int $purchaseOrderId
     * @param array $data
     * @return mixed
     */
    public function uploadAttachment($purchaseOrderId, array $data);

    /**
     * Delete purchase order attachment
     *
     * @param int $purchaseOrderId
     * @param int $attachmentId
     * @return bool
     */
    public function deleteAttachment($purchaseOrderId, $attachmentId);

    /**
     * Get warehouse areas
     *
     * @param int $warehouseId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getWarehouseAreas($warehouseId);
}
