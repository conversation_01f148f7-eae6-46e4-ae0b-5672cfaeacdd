<?php

namespace App\Contracts;

interface TransferRequestInterface
{
    /**
     * Get all transfer requests with pagination
     */
    public function getAllWithPagination($perPage = 15);

    /**
     * Get transfer request by ID
     */
    public function getById($id);

    /**
     * Create new transfer request
     */
    public function create(array $data);

    /**
     * Update transfer request
     */
    public function update($id, array $data);

    /**
     * Delete transfer request
     */
    public function delete($id);

    /**
     * Get transfer requests for datatable
     */
    public function getForDatatable(array $params);

    /**
     * Submit transfer request for approval
     */
    public function submitForApproval($id);

    /**
     * Approve transfer request
     */
    public function approve($id, $approvedBy);

    /**
     * Reject transfer request
     */
    public function reject($id, $rejectedBy, $reason = null);

    /**
     * Cancel transfer request
     */
    public function cancel($id);

    /**
     * Get approved transfer requests that don't have transfer orders
     */
    public function getApprovedWithoutTransferOrder();

    /**
     * Create transfer request items
     */
    public function createItems($transferRequestId, array $items);

    /**
     * Update transfer request items
     */
    public function updateItems($transferRequestId, array $items);

    /**
     * Delete transfer request items
     */
    public function deleteItems($transferRequestId, array $itemIds = []);
}
