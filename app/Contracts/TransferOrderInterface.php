<?php

namespace App\Contracts;

interface TransferOrderInterface
{
    /**
     * Get all transfer orders with pagination
     */
    public function getAllWithPagination($perPage = 15);

    /**
     * Get transfer order by ID
     */
    public function getById($id);

    /**
     * Create new transfer order
     */
    public function create(array $data);

    /**
     * Update transfer order
     */
    public function update($id, array $data);

    /**
     * Delete transfer order
     */
    public function delete($id);

    /**
     * Get transfer orders for datatable
     */
    public function getForDatatable(array $params);

    /**
     * Submit transfer order for approval
     */
    public function submitForApproval($id);

    /**
     * Approve transfer order
     */
    public function approve($id, $approvedBy);

    /**
     * Reject transfer order
     */
    public function reject($id, $rejectedBy, $reason = null);

    /**
     * Cancel transfer order
     */
    public function cancel($id);

    /**
     * Get approved transfer orders that don't have transfer receipts
     */
    public function getApprovedWithoutTransferReceipt();

    /**
     * Create transfer order items
     */
    public function createItems($transferOrderId, array $items);

    /**
     * Update transfer order items
     */
    public function updateItems($transferOrderId, array $items);

    /**
     * Delete transfer order items
     */
    public function deleteItems($transferOrderId, array $itemIds = []);

    /**
     * Create transfer order from transfer request
     */
    public function createFromTransferRequest($transferRequestId, array $data);
}
