<?php

namespace App\Repositories;

use App\Contracts\SettingsInterface;
use App\Models\Settings;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Schema;

class SettingsRepository extends BaseRepository implements SettingsInterface
{
    protected $model;

    public function __construct(Settings $settings)
    {
        parent::__construct($settings);

        if (Schema::hasTable('settings')) {
            $this->model = $settings->firstOrNew(['id' => 1]);
        }
    }

    /**
     * @param $data
     * @return bool
     */
    public function updateGeneral($data): bool
    {
        try {
            $this->model->update(
                [
                    'site_name' => $data['site_name'],
                    'site_url' => $data['site_url'],
                    'site_email' => $data['site_email'],
                    'meta_title' => $data['site_meta_title'],
                    'meta_description' => $data['site_meta_description'],
                    'recaptcha_login' => $data['recaptcha_login'],
                    'recaptcha_register' => $data['recaptcha_register'],
                    'recaptcha_sitekey' => $data['recaptcha_sitekey'],
                    'recaptcha_secretkey' => $data['recaptcha_secretkey'],
                    'logo' => $data['site_logo'] ?? $this->model->logo,
                    'favicon' => $data['site_favicon'] ?? $this->model->favicon,
                    'logo_path' => $data['logo_path'] ?? $this->model->logo_path,
                    'favicon_path' => $data['favicon_path'] ?? $this->model->favicon_path,
                    'login_with_otp' => $data['login_with_otp'],
                ]
            );
            return true;
        } catch (\Exception $e) {
            createErrorLog($e, 'daily', 'updateGeneral');
            return false;
        }
    }

    /**
     * @return mixed
     */
    public function getGeneral()
    {
        return $this->model->select([
            'site_name',
            'site_url',
            'site_email',
            'meta_title',
            'meta_description',
            'recaptcha_login',
            'recaptcha_register',
            'recaptcha_sitekey',
            'recaptcha_secretkey',
            'logo',
            'favicon',
            'logo_path',
            'favicon_path',
            'login_with_otp'
        ])->first();
    }

    /**
     * @param $data
     * @return bool
     */
    public function updateSmtp($data): bool
    {
        try {
            $this->model->update([
                'smtp_host' => $data['smtp_host'],
                'smtp_port' => $data['smtp_port'],
                'smtp_encryption' => $data['smtp_encryption'],
                'smtp_username' => $data['smtp_username'],
                'smtp_password' => $data['smtp_password'],
                'smtp_sender_email' => $data['smtp_sender_email'],
                'smtp_sender_name' => $data['smtp_sender_name'],
            ]);
            return true;
        } catch (\Exception $e) {
            createErrorLog($e, 'daily', 'updateSmtp');
            return false;
        }
    }

    /**
     * @return mixed
     */
    public function getSmtp()
    {
        if ($this->model){
            return $this->model->select([
                'smtp_host',
                'smtp_port',
                'smtp_encryption',
                'smtp_username',
                'smtp_password',
                'smtp_sender_email',
                'smtp_sender_name',
            ])->first();
        }
    }

    /**
     * @param $data
     * @return bool
     */
    public function updateNotification($data): bool
    {
        try {
            $this->model->update([
                'push_notification' => $data['push_notification'] ?? $this->model->push_notification,
                'push_app_id' => $data['push_app_id'] ?? $this->model->push_app_id,
                'push_api_key' => $data['push_api_key'] ?? $this->model->push_api_key,
                'push_secret_key' => $data['push_secret_key'] ?? $this->model->push_secret_key,
                'push_cluster' => $data['push_cluster'] ?? $this->model->push_cluster,
                'reverb_notification' => $data['reverb_notification'] ?? $this->model->reverb_notification,
                'reverb_app_id' => $data['reverb_app_id'] ?? $this->model->reverb_app_id,
                'reverb_api_key' => $data['reverb_api_key'] ?? $this->model->reverb_api_key,
                'reverb_secret_key' => $data['reverb_secret_key'] ?? $this->model->reverb_secret_key,
                'reverb_host' => $data['reverb_host'] ?? $this->model->reverb_host,
                'reverb_port' => $data['reverb_port'] ?? $this->model->reverb_port,
                'reverb_scheme' => $data['reverb_scheme'] ?? $this->model->reverb_scheme,
            ]);
            return true;
        } catch (\Exception $e) {
            createErrorLog($e, 'daily', 'updateNotification');
            return false;
        }
    }

    /**
     * @return mixed
     */
    public function getNotification()
    {
        return $this->model->select([
            'push_notification',
            'push_app_id',
            'push_api_key',
            'push_secret_key',
            'push_cluster',
            'reverb_notification',
            'reverb_app_id',
            'reverb_api_key',
            'reverb_secret_key',
            'reverb_host',
            'reverb_port',
            'reverb_scheme',
        ])->first();
    }

    /**
     * @param $data
     * @return bool
     */
    public function updateVnpt($data): bool
    {
        try {
            $this->model->update([
                'vnpt_public_service_endpoint' => $data['vnpt_public_service_endpoint'] ?? $this->model->vnpt_public_service_endpoint,
                'vnpt_portal_service_endpoint' => $data['vnpt_portal_service_endpoint'] ?? $this->model->vnpt_portal_service_endpoint,
                'vnpt_biz_service_endpoint' => $data['vnpt_biz_service_endpoint'] ?? $this->model->vnpt_biz_service_endpoint,
                'vnpt_username' => $data['vnpt_username'] ?? $this->model->vnpt_username,
                'vnpt_password' => $data['vnpt_password'] ?? $this->model->vnpt_password,
                'vnpt_account_username' => $data['vnpt_account_username'] ?? $this->model->vnpt_account_username,
                'vnpt_account_password' => $data['vnpt_account_password'] ?? $this->model->vnpt_account_password,
                'vnpt_pattern' => $data['vnpt_pattern'] ?? $this->model->vnpt_pattern,
                'vnpt_serial' => $data['vnpt_serial'] ?? $this->model->vnpt_serial,
                'vnpt_sandbox_public_service_endpoint' => $data['vnpt_sandbox_public_service_endpoint'] ?? $this->model->vnpt_sandbox_public_service_endpoint,
                'vnpt_sandbox_portal_service_endpoint' => $data['vnpt_sandbox_portal_service_endpoint'] ?? $this->model->vnpt_sandbox_portal_service_endpoint,
                'vnpt_sandbox_biz_service_endpoint' => $data['vnpt_sandbox_biz_service_endpoint'] ?? $this->model->vnpt_sandbox_biz_service_endpoint,
                'vnpt_sandbox_username' => $data['vnpt_sandbox_username'] ?? $this->model->vnpt_sandbox_username,
                'vnpt_sandbox_password' => $data['vnpt_sandbox_password'] ?? $this->model->vnpt_sandbox_password,
                'vnpt_sandbox_account_username' => $data['vnpt_sandbox_account_username'] ?? $this->model->vnpt_sandbox_account_username,
                'vnpt_sandbox_account_password' => $data['vnpt_sandbox_account_password'] ?? $this->model->vnpt_sandbox_account_password,
                'vnpt_sandbox_pattern' => $data['vnpt_sandbox_pattern'] ?? $this->model->vnpt_sandbox_pattern,
                'vnpt_sandbox_serial' => $data['vnpt_sandbox_serial'] ?? $this->model->vnpt_sandbox_serial,
                'vnpt_active' => $data['vnpt_active'] ?? $this->model->vnpt_active,
                'vnpt_sandbox_active' => $data['vnpt_sandbox_active'] ?? $this->model->vnpt_sandbox_active,
            ]);
            return true;
        } catch (\Exception $e) {
            createErrorLog($e, 'daily', 'updateVnpt');
            return false;
        }
    }

    /**
     * @return mixed
     */
    public function getVnpt()
    {
        return $this->model->select([
            'vnpt_public_service_endpoint',
            'vnpt_portal_service_endpoint',
            'vnpt_biz_service_endpoint',
            'vnpt_username',
            'vnpt_password',
            'vnpt_account_username',
            'vnpt_account_password',
            'vnpt_pattern',
            'vnpt_serial',
            'vnpt_sandbox_public_service_endpoint',
            'vnpt_sandbox_portal_service_endpoint',
            'vnpt_sandbox_biz_service_endpoint',
            'vnpt_sandbox_username',
            'vnpt_sandbox_password',
            'vnpt_sandbox_account_username',
            'vnpt_sandbox_account_password',
            'vnpt_sandbox_pattern',
            'vnpt_sandbox_serial',
            'vnpt_active',
            'vnpt_sandbox_active',
        ])->first();
    }

    public function updateNhattin($data)
    {
        try {
            $this->model->update([
                'nhattin_host' => $data['nhattin_host'] ?? $this->model->nhattin_host,
                'nhattin_parner_id' => $data['nhattin_parner_id'] ?? $this->model->nhattin_parner_id,
                'nhattin_username' => $data['nhattin_username'] ?? $this->model->nhattin_username,
                'nhattin_password' => $data['nhattin_password'] ?? $this->model->nhattin_password,
                'nhattin_service_id' => $data['nhattin_service_id'] ?? $this->model->nhattin_service_id,
                'nhattin_payment_method' => $data['nhattin_payment_method'] ?? $this->model->nhattin_payment_method,
                'nhattin_cod' => $data['nhattin_cod'] ?? $this->model->nhattin_cod,
                'nhattin_cargo_type' => $data['nhattin_cargo_type'] ?? $this->model->nhattin_cargo_type,
                'nhattin_sandbox_host' => $data['nhattin_sandbox_host'] ?? $this->model->nhattin_sandbox_host,
                'nhattin_sandbox_parner_id' => $data['nhattin_sandbox_parner_id'] ?? $this->model->nhattin_sandbox_parner_id,
                'nhattin_sandbox_username' => $data['nhattin_sandbox_username'] ?? $this->model->nhattin_sandbox_username,
                'nhattin_sandbox_password' => $data['nhattin_sandbox_password'] ?? $this->model->nhattin_sandbox_password,
                'nhattin_sandbox_service_id' => $data['nhattin_sandbox_service_id'] ?? $this->model->nhattin_sandbox_service_id,
                'nhattin_sandbox_payment_method' => $data['nhattin_sandbox_payment_method'] ?? $this->model->nhattin_sandbox_payment_method,
                'nhattin_sandbox_cod' => $data['nhattin_sandbox_cod'] ?? $this->model->nhattin_sandbox_cod,
                'nhattin_sandbox_cargo_type' => $data['nhattin_sandbox_cargo_type'] ?? $this->model->nhattin_sandbox_cargo_type,
                'nhattin_sender_name' => $data['nhattin_sender_name'] ?? $this->model->nhattin_sender_name,
                'nhattin_sender_phone' => $data['nhattin_sender_phone'] ?? $this->model->nhattin_sender_phone,
                'nhattin_sender_address' => $data['nhattin_sender_address'] ?? $this->model->nhattin_sender_address,
                'nhattin_ufm_source' => $data['nhattin_ufm_source'] ?? $this->model->nhattin_ufm_source,
                'nhattin_active' => $data['nhattin_active'] ?? $this->model->nhattin_active,
                'nhattin_sandbox_active' => $data['nhattin_sandbox_active'] ?? $this->model->nhattin_sandbox_active,
            ]);
            return true;
        } catch (\Exception $e) {
            createErrorLog($e, 'daily', 'updateNhattin');
            return false;
        }
    }

    public function getNhattin()
    {
        return $this->model->select([
            'nhattin_host',
            'nhattin_parner_id',
            'nhattin_username',
            'nhattin_password',
            'nhattin_service_id',
            'nhattin_payment_method',
            'nhattin_cod',
            'nhattin_cargo_type',
            'nhattin_sandbox_host',
            'nhattin_sandbox_parner_id',
            'nhattin_sandbox_username',
            'nhattin_sandbox_password',
            'nhattin_sandbox_service_id',
            'nhattin_sandbox_payment_method',
            'nhattin_sandbox_cod',
            'nhattin_sandbox_cargo_type',
            'nhattin_sender_name',
            'nhattin_sender_phone',
            'nhattin_sender_address',
            'nhattin_ufm_source',
            'nhattin_active',
            'nhattin_sandbox_active',
        ])->first();
    }

    public function updateSAP($data)
    {
        try {
            $this->model->update([
                'sap_active' => $data['sap_active'] ?? $this->model->sap_active,
                'sap_server' => $data['sap_server'] ?? $this->model->sap_server,
                'sap_database' => $data['sap_database'] ?? $this->model->sap_database,
                'sap_username' => $data['sap_username'] ?? $this->model->sap_username,
                'sap_password' => $data['sap_password'] ?? $this->model->sap_password,
                'sap_sandbox_active' => $data['sap_sandbox_active'] ?? $this->model->sap_sandbox_active,
                'sap_sandbox_server' => $data['sap_sandbox_server'] ?? $this->model->sap_sandbox_server,
                'sap_sandbox_database' => $data['sap_sandbox_database'] ?? $this->model->sap_sandbox_database,
                'sap_sandbox_username' => $data['sap_sandbox_username'] ?? $this->model->sap_sandbox_username,
                'sap_sandbox_password' => $data['sap_sandbox_password'] ?? $this->model->sap_sandbox_password,
            ]);
            return true;
        } catch (\Exception $e) {
            createErrorLog($e, 'daily', 'updateSAP');
            return false;
        }
    }

    public function getSAP()
    {
        return $this->model->select([
            'sap_active',
            'sap_server',
            'sap_database',
            'sap_username',
            'sap_password',
            'sap_sandbox_active',
            'sap_sandbox_server',
            'sap_sandbox_database',
            'sap_sandbox_username',
            'sap_sandbox_password',
        ])->first();
    }

    public function updateZalo($data)
    {
        try {
            $this->model->update([
                'zalo_active' => $data['zalo_active'] ?? $this->model->zalo_active,
                'zalo_host' => $data['zalo_host'] ?? $this->model->zalo_host,
                'zalo_redirect_url' => $data['zalo_redirect_url'] ?? $this->model->zalo_redirect_url,
                'zalo_app_id' => $data['zalo_app_id'] ?? $this->model->zalo_app_id,
                'zalo_app_secret' => $data['zalo_app_secret'] ?? $this->model->zalo_app_secret,
                'zalo_code_verifier' => $data['zalo_code_verifier'] ?? $this->model->zalo_code_verifier,
            ]);
            return true;
        } catch (\Exception $e) {
            createErrorLog($e, 'daily', 'updateZalo');
            return false;
        }
    }

    public function getZalo()
    {
        return $this->model->select([
            'zalo_active',
            'zalo_host',
            'zalo_redirect_url',
            'zalo_app_id',
            'zalo_app_secret',
            'zalo_code_verifier',
        ])->first();
    }

    public function updateAsus($data)
    {
        try {
            $this->model->update([
                'asus_active' => $data['asus_active'] ?? $this->model->asus_active,
                'asus_host' => $data['asus_host'] ?? $this->model->asus_host,
                'asus_token' => $data['asus_token'] ?? $this->model->asus_token,
                'asus_sandbox_active' => $data['asus_sandbox_active'] ?? $this->model->asus_sandbox_active,
                'asus_sandbox_host' => $data['asus_sandbox_host'] ?? $this->model->asus_sandbox_host,
                'asus_sandbox_token' => $data['asus_sandbox_token'] ?? $this->model->asus_sandbox_token,
            ]);
            return true;
        } catch (\Exception $e) {
            createErrorLog($e, 'daily', 'updateAsus');
            return false;
        }
    }

    public function getAsus()
    {
        return $this->model->select([
            'asus_active',
            'asus_host',
            'asus_token',
            'asus_sandbox_active',
            'asus_sandbox_host',
            'asus_sandbox_token',
        ])->first();
    }

    /**
     * @param $data
     * @return bool
     */
    public function updateProductSettings($data): bool
    {
        try {
            $this->model->update([
                'product_default_form' => $data['product_default_form'] ?? $this->model->product_default_form,
            ]);
            return true;
        } catch (\Exception $e) {
            createErrorLog($e, 'daily', 'updateProductSettings');
            return false;
        }
    }

    /**
     * @return mixed
     */
    public function getProductSettings()
    {
        return $this->model->select([
            'product_default_form',
        ])->first();
    }
}
