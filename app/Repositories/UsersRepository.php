<?php

namespace App\Repositories;


use App\Contracts\UsersInterface;
use App\Http\Requests\UserRequest;
use App\Models\Team;
use App\Models\User;
use App\Repositories\BaseRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UsersRepository extends BaseRepository implements UsersInterface
{
  protected $model;
  protected $team;

  public function __construct(User $user)
  {
    parent::__construct($user);
  }

  public function countVerify()
  {
    return $this->model->whereNotNull('email_verified_at')->get()->count();
  }

  public function countNotVerify()
  {
    return $this->model->whereNull('email_verified_at')->get()->count();
  }

  public function countDuplicates()
  {
    $users = $this->model->all();
    $usersUnique = $users->unique(['email']);
    return $users->diff($usersUnique)->count();
  }

  public function getUsersDatatable(Request $request)
  {
    $columns = [
        1 => 'id',
        2 => 'name',
        3 => 'email',
        4 => 'email_verified_at',
    ];
    $totalData = $this->model->count();
    $totalFiltered = $totalData;
    $limit = $request->input('length') ?? 10;
    $start = $request->input('start') ?? 0;
    $order = $columns[$request->input('order.0.column') ?? 0];
    $dir = $request->input('order.0.dir') ?? 'asc';

    if (empty($request->input('search.value'))) {
      // Always use limit with offset to avoid MySQL error
      // If limit is -1, use a very large number instead
      if ($limit == -1) {
        $limit = PHP_INT_MAX;
      }

      $users = $this->model->skip($start)
          ->take($limit)
          ->orderBy($order, $dir)
          ->get();
    } else {
      $search = $request->input('search.value');

      // Always use limit with offset to avoid MySQL error
      // If limit is -1, use a very large number instead
      if ($limit == -1) {
        $limit = PHP_INT_MAX;
      }

      $users = User::where('id', 'LIKE', "%{$search}%")
          ->orWhere('name', 'LIKE', "%{$search}%")
          ->orWhere('email', 'LIKE', "%{$search}%")
          ->skip($start)
          ->take($limit)
          ->orderBy($order, $dir)
          ->get();

      $totalFiltered = User::where('id', 'LIKE', "%{$search}%")
          ->orWhere('name', 'LIKE', "%{$search}%")
          ->orWhere('email', 'LIKE', "%{$search}%")
          ->count();
    }

    return ['users' => $users, 'totalFiltered' => $totalFiltered, 'totalData' => $totalData];
  }

  public function createUser(UserRequest $request)
  {
    $userID = $request->id;
    if ($userID) {
      // update the value
      $this->model->updateOrCreate(
          ['id' => $userID],
          ['name' => $request->name, 'password' => bcrypt($request->password)]
      );
      // user updated
      return "updated";
    } else {
      // create new one if email is unique
      $userEmail = $this->model->where('email', $request->email)->first();
      if (empty($userEmail)) {
        DB::transaction(function () use ($request) {
          tap($this->model::create([
              'name' => $request->name,
              'email' => $request->email,
              'password' => Hash::make($request->password),
          ]), function (User $user) use ($request) {
            $user->ownedTeams()->save(Team::forceCreate([
                'user_id' => $user->id,
                'name' => explode(' ', $user->name, 2)[0] . "'s Team",
                'personal_team' => true,
            ]));
            $userTeam = $this->model->find(1)->ownedTeams()->first();
            setPermissionsTeamId($userTeam->id);
            $user->assignRole($request->roles);
          });
        });
        // user created
        return "created";
      }
    }
  }
}
