<?php

namespace App\Repositories;

use App\Models\Product;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\ProductAttribute;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Contracts\ProductAttributeInterface;

class ProductAttributeRepository extends BaseRepository implements ProductAttributeInterface
{
    protected $model;
    protected $productModel;
    protected $attributeModel;
    protected $attributeValueModel;

    public function __construct(
        ProductAttribute $productAttribute,
        Product $product,
        Attribute $attribute,
        AttributeValue $attributeValue
    ) {
        parent::__construct($productAttribute);
        $this->productModel = $product;
        $this->attributeModel = $attribute;
        $this->attributeValueModel = $attributeValue;
    }

    /**
     * Get all attributes for a product
     *
     * @param int $productId
     * @return array
     */
    public function getProductAttributes(int $productId)
    {
        try {
            $product = $this->productModel->findOrFail($productId);

            $productAttributes = $product->productAttributes()
                ->with(['attribute', 'attributeValue'])
                ->get()
                ->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'attribute_id' => $item->attribute_id,
                        'attribute_name' => $item->attribute->name,
                        'attribute_code' => $item->attribute->code,
                        'attribute_type' => $item->attribute->type,
                        'attribute_value_id' => $item->attribute_value_id,
                        'attribute_value' => $item->attribute_value_id ? $item->attributeValue->value : null,
                        'text_value' => $item->text_value,
                        'display_value' => $item->attribute_value_id ? $item->attributeValue->display_value : $item->text_value,
                    ];
                });

            return [
                'success' => true,
                'message' => 'Thuộc tính sản phẩm đã được lấy thành công',
                'data' => $productAttributes
            ];
        } catch (\Exception $e) {
            Log::error('Error retrieving product attributes: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lấy thuộc tính sản phẩm: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Add an attribute to a product
     *
     * @param int $productId
     * @param array $data
     * @return array
     */
    public function addProductAttribute(int $productId, array $data)
    {
        try {
            $product = $this->productModel->findOrFail($productId);
            $attribute = $this->attributeModel->findOrFail($data['attribute_id']);

            // Check if attribute value belongs to the attribute
            if (!empty($data['attribute_value_id'])) {
                $attributeValue = $this->attributeValueModel
                    ->where('id', $data['attribute_value_id'])
                    ->where('attribute_id', $attribute->id)
                    ->first();

                if (!$attributeValue) {
                    return [
                        'success' => false,
                        'message' => 'Giá trị thuộc tính không thuộc về thuộc tính đã chỉ định'
                    ];
                }
            }

            // Check if attribute is already assigned to the product
            $existingAttribute = $this->model
                ->where('product_id', $product->id)
                ->where('attribute_id', $attribute->id)
                ->first();

            if ($existingAttribute) {
                return [
                    'success' => false,
                    'message' => 'Thuộc tính này đã được gán cho sản phẩm'
                ];
            }

            DB::beginTransaction();

            // Create product attribute
            $productAttribute = new ProductAttribute();
            $productAttribute->product_id = $product->id;
            $productAttribute->attribute_id = $attribute->id;
            $productAttribute->attribute_value_id = $data['attribute_value_id'] ?? null;
            $productAttribute->text_value = $data['text_value'] ?? null;
            $productAttribute->save();

            DB::commit();

            return [
                'success' => true,
                'message' => 'Thuộc tính sản phẩm đã được thêm thành công',
                'data' => [
                    'id' => $productAttribute->id,
                    'product_id' => $productAttribute->product_id,
                    'attribute_id' => $productAttribute->attribute_id,
                    'attribute_name' => $attribute->name,
                    'attribute_code' => $attribute->code,
                    'attribute_type' => $attribute->type,
                    'attribute_value_id' => $productAttribute->attribute_value_id,
                    'text_value' => $productAttribute->text_value,
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error adding product attribute: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi thêm thuộc tính sản phẩm: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update a product attribute
     *
     * @param int $productId
     * @param int $attributeId
     * @param array $data
     * @return array
     */
    public function updateProductAttribute(int $productId, int $attributeId, array $data)
    {
        try {
            $product = $this->productModel->findOrFail($productId);
            $attribute = $this->attributeModel->findOrFail($attributeId);

            // Check if attribute value belongs to the attribute
            if (!empty($data['attribute_value_id'])) {
                $attributeValue = $this->attributeValueModel
                    ->where('id', $data['attribute_value_id'])
                    ->where('attribute_id', $attribute->id)
                    ->first();

                if (!$attributeValue) {
                    return [
                        'success' => false,
                        'message' => 'Giá trị thuộc tính không thuộc về thuộc tính đã chỉ định'
                    ];
                }
            }

            // Find the product attribute
            $productAttribute = $this->model
                ->where('product_id', $product->id)
                ->where('attribute_id', $attribute->id)
                ->first();

            if (!$productAttribute) {
                return [
                    'success' => false,
                    'message' => 'Không tìm thấy thuộc tính sản phẩm'
                ];
            }

            DB::beginTransaction();

            // Update product attribute
            $productAttribute->attribute_value_id = $data['attribute_value_id'] ?? null;
            $productAttribute->text_value = $data['text_value'] ?? null;
            $productAttribute->save();

            DB::commit();

            return [
                'success' => true,
                'message' => 'Thuộc tính sản phẩm đã được cập nhật thành công',
                'data' => [
                    'id' => $productAttribute->id,
                    'product_id' => $productAttribute->product_id,
                    'attribute_id' => $productAttribute->attribute_id,
                    'attribute_name' => $attribute->name,
                    'attribute_code' => $attribute->code,
                    'attribute_type' => $attribute->type,
                    'attribute_value_id' => $productAttribute->attribute_value_id,
                    'text_value' => $productAttribute->text_value,
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating product attribute: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật thuộc tính sản phẩm: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete a product attribute
     *
     * @param int $productId
     * @param int $attributeId
     * @return array
     */
    public function deleteProductAttribute(int $productId, int $attributeId)
    {
        try {
            $product = $this->productModel->findOrFail($productId);
            $attribute = $this->attributeModel->findOrFail($attributeId);

            // Find the product attribute
            $productAttribute = $this->model
                ->where('product_id', $product->id)
                ->where('attribute_id', $attribute->id)
                ->first();

            if (!$productAttribute) {
                return [
                    'success' => false,
                    'message' => 'Không tìm thấy thuộc tính sản phẩm'
                ];
            }

            DB::beginTransaction();

            // Delete product attribute
            $productAttribute->delete();

            DB::commit();

            return [
                'success' => true,
                'message' => 'Thuộc tính sản phẩm đã được xóa thành công'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting product attribute: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa thuộc tính sản phẩm: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Batch update product attributes
     *
     * @param int $productId
     * @param array $attributes
     * @return array
     */
    public function batchUpdateProductAttributes(int $productId, array $attributes)
    {
        try {
            $product = $this->productModel->findOrFail($productId);

            DB::beginTransaction();

            // Delete existing product attributes
            $product->productAttributes()->delete();

            // Add new product attributes
            $attributeData = [];
            foreach ($attributes as $attributeItem) {
                $attribute = $this->attributeModel->findOrFail($attributeItem['attribute_id']);

                // Check if attribute value belongs to the attribute
                if (!empty($attributeItem['attribute_value_id'])) {
                    $attributeValue = $this->attributeValueModel
                        ->where('id', $attributeItem['attribute_value_id'])
                        ->where('attribute_id', $attribute->id)
                        ->first();

                    if (!$attributeValue) {
                        DB::rollBack();
                        return [
                            'success' => false,
                            'message' => 'Giá trị thuộc tính không thuộc về thuộc tính đã chỉ định'
                        ];
                    }
                }

                $productAttribute = new ProductAttribute();
                $productAttribute->product_id = $product->id;
                $productAttribute->attribute_id = $attribute->id;
                $productAttribute->attribute_value_id = $attributeItem['attribute_value_id'] ?? null;
                $productAttribute->text_value = $attributeItem['text_value'] ?? null;
                $productAttribute->save();

                $attributeData[] = [
                    'id' => $productAttribute->id,
                    'product_id' => $productAttribute->product_id,
                    'attribute_id' => $productAttribute->attribute_id,
                    'attribute_name' => $attribute->name,
                    'attribute_code' => $attribute->code,
                    'attribute_type' => $attribute->type,
                    'attribute_value_id' => $productAttribute->attribute_value_id,
                    'text_value' => $productAttribute->text_value,
                ];
            }

            DB::commit();

            return [
                'success' => true,
                'message' => 'Thuộc tính sản phẩm đã được cập nhật thành công',
                'data' => $attributeData
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating product attributes: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật thuộc tính sản phẩm: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get all available attributes
     *
     * @return array
     */
    public function getAvailableAttributes()
    {
        try {
            $attributes = $this->attributeModel
                ->active()
                ->with('activeValues')
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get();

            return [
                'success' => true,
                'message' => 'Thuộc tính có sẵn đã được lấy thành công',
                'data' => $attributes
            ];
        } catch (\Exception $e) {
            Log::error('Error retrieving available attributes: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lấy thuộc tính có sẵn: ' . $e->getMessage()
            ];
        }
    }
}
