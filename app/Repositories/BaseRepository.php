<?php

namespace App\Repositories;

use App\Contracts\BaseInterface;
use Illuminate\Database\Eloquent\Collection;

abstract class BaseRepository implements BaseInterface
{

  /**
   * The repository model
   *
   * @var \Illuminate\Database\Eloquent\Model
   */
  protected $model;

  /**
   * BaseRepository constructor.
   *
   * @param \Illuminate\Database\Eloquent\Model $model
   */
  public function __construct($model)
  {
    $this->model = $model;
  }


  /**
   * The query builder
   *
   * @var \Illuminate\Database\Eloquent\Builder
   */
  protected $query;


  /**
   * Alias for the query limit
   *
   * @var int
   */
  protected $take;


  /**
   * Array of related models to eager load
   *
   * @var array
   */
  protected $with = array();


  /**
   * Array of one or more where clause parameters
   *
   * @var array
   */
  protected $wheres = array();


  /**
   * Array of one or more where in clause parameters
   *
   * @var array
   */
  protected $whereIns = array();


  /**
   * Array of one or more ORDER BY column/value pairs
   *
   * @var array
   */
  protected $orderBys = array();


  /**
   * Array of scope methods to call on the model
   *
   * @var array
   */
  protected $scopes = array();


  /**
   * Get all the model records in the database
   *
   * @return \Illuminate\Database\Eloquent\Collection
   */
  public function all()
  {
    $this->newQuery()->eagerLoad();

    $models = $this->query->get();

    $this->unsetClauses();

    return $models;
  }

  /**
   * Get all resources
   *
   * @return \Illuminate\Database\Eloquent\Collection
   */
  public function getAll()
  {
    return $this->all();
  }

  /**
   * Add relationships to the query builder to eager load
   *
   * @return $this
   */
  protected function eagerLoad()
  {
    foreach ($this->with as $relation) {
      $this->query->with($relation);
    }

    return $this;
  }

  /**
   * Set Eloquent relationships to eager load
   *
   * @param $relations
   *
   * @return $this
   */
  public function with($relations)
  {
    if (is_string($relations)) $relations = func_get_args();

    $this->with = $relations;

    return $this;
  }

  /**
   * Create a new instance of the model's query builder
   *
   * @return $this
   */
  protected function newQuery()
  {
    $this->query = $this->model->newQuery();

    return $this;
  }

  /**
   * Get all the specified model records in the database
   *
   * @return \Illuminate\Database\Eloquent\Collection
   */
  public function get()
  {
    $this->newQuery()->eagerLoad()->setClauses()->setScopes();

    $models = $this->query->get();

    $this->unsetClauses();

    return $models;
  }

  /**
   * Set query scopes
   *
   * @return $this
   */
  protected function setScopes()
  {
    foreach ($this->scopes as $method => $args) {
      $this->query->$method(implode(', ', $args));
    }

    return $this;
  }

  /**
   * Set clauses on the query builder
   *
   * @return $this
   */
  protected function setClauses()
  {
    foreach ($this->wheres as $where) {
      $this->query->where($where['column'], $where['operator'], $where['value']);
    }

    foreach ($this->whereIns as $whereIn) {
      $this->query->whereIn($whereIn['column'], $whereIn['values']);
    }

    foreach ($this->orderBys as $orders) {
      $this->query->orderBy($orders['column'], $orders['direction']);
    }

    if (isset($this->take) and !is_null($this->take)) {
      $this->query->take($this->take);
    }

    return $this;
  }

  /**
   * Add a simple where clause to the query
   *
   * @param string $column
   * @param string $value
   * @param string $operator
   *
   * @return $this
   */
  public function where($column, $value, $operator = '=')
  {
    $this->wheres[] = compact('column', 'value', 'operator');

    return $this;
  }

  /**
   * Add a simple where in clause to the query
   *
   * @param string $column
   * @param mixed $values
   *
   * @return $this
   */
  public function whereIn($column, $values)
  {
    $values = is_array($values) ? $values : array($values);

    $this->whereIns[] = compact('column', 'values');

    return $this;
  }

  /**
   * Set an ORDER BY clause
   *
   * @param string $column
   * @param string $direction
   * @return $this
   */
  public function orderBy($column, $direction = 'asc')
  {
    $this->orderBys[] = compact('column', 'direction');

    return $this;
  }

  /**
   * Reset the query clause parameter arrays
   *
   * @return $this
   */
  protected function unsetClauses()
  {
    $this->wheres = array();
    $this->whereIns = array();
    $this->scopes = array();
    $this->take = null;

    return $this;
  }

  /**
   * Count the number of specified model records in the database
   *
   * @return int
   */
  public function count()
  {
    return $this->get()->count();
  }

  /**
   * Create one or more new model records in the database
   *
   * @param array $data
   *
   * @return \Illuminate\Database\Eloquent\Collection
   */
  public function createMultiple(array $data)
  {
    $models = new Collection();

    foreach ($data as $d) {
      $models->push($this->create($d));
    }

    return $models;
  }

  /**
   * Create a new model record in the database
   *
   * @param array $data
   *
   * @return \Illuminate\Database\Eloquent\Model
   */
  public function create(array $data)
  {
    $this->unsetClauses();

    return $this->model->create($data);
  }

  /**
   * Delete the specified model record from the database
   *
   * @param $id
   *
   * @return bool|null
   * @throws \Exception
   */
  public function deleteById($id)
  {
    $this->unsetClauses();

    return $this->getById($id)->delete();
  }

  /**
   * Delete one or more model records from the database
   *
   * @param $id
   * @return mixed
   */
  public function delete($id)
  {
    $this->newQuery()->setClauses()->setScopes();

    $result = $this->query->delete();

    $this->unsetClauses();

    return $result;
  }

  /**
   * Get the specified model record from the database
   *
   * @param $id
   *
   * @return \Illuminate\Database\Eloquent\Model
   */
  public function getById($id)
  {
    $this->unsetClauses();

    $this->newQuery()->eagerLoad();

    return $this->query->findOrFail($id);
  }

  /**
   * Delete multiple records
   *
   * @param array $ids
   *
   * @return int
   */
  public function deleteMultipleById(array $ids)
  {
    return $this->model->destroy($ids);
  }

  /**
   * Get the first specified model record from the database
   *
   * @return \Illuminate\Database\Eloquent\Model
   */
  public function first()
  {
    $this->newQuery()->eagerLoad()->setClauses()->setScopes();

    $model = $this->query->firstOrFail();

    $this->unsetClauses();

    return $model;
  }

  /**
   * Set the query limit
   *
   * @param int $limit
   *
   * @return $this
   */
  public function limit($limit)
  {
    $this->take = $limit;

    return $this;
  }

  /**
   * Update the specified model record in the database
   *
   * @param       $id
   * @param array $data
   *
   * @return \Illuminate\Database\Eloquent\Model
   */
  public function updateById($id, array $data)
  {
    $this->unsetClauses();

    $model = $this->getById($id);

    $model->update($data);

    return $model;
  }

  /**
   * Update a resource
   *
   * @param int $id
   * @param array $data
   * @return bool
   */
  public function update($id, array $data)
  {
    $model = $this->updateById($id, $data);
    return $model ? true : false;
  }

}
