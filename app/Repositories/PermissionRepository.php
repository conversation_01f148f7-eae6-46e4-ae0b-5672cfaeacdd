<?php

namespace App\Repositories;

use App\Contracts\PermissionsInterface;
use App\Enums\Permissions;
use App\Repositories\BaseRepository;
use Spatie\Permission\Models\Permission;

class PermissionRepository extends BaseRepository implements PermissionsInterface
{
  public function __construct(Permission $permission)
  {
    parent::__construct($permission);
  }

  public function getPermissions(): array
  {
    $items = [];
    foreach (Permissions::cases() as $permission) {
      $items[] = [
        'label' => $permission->label(),
        'name' => $permission->value,
      ];
    }
    return $this->permissionsGroup($items);
  }

  public function permissionsGroup($permissions): array
  {
    $arr = array();
    foreach ($permissions as $item) {
      $parts = explode('.', $item['name']);
      $arr[$parts[0]][] = $item;
    }
    ksort($arr, SORT_NATURAL);
    return $arr;
  }

}
