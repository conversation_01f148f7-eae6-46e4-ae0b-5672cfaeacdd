<?php

namespace App\Repositories;

use App\Contracts\WarehouseTransferInterface;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\WarehouseArea;
use App\Models\WarehouseReceipt;
use App\Models\WarehouseTransfer;
use App\Models\WarehouseTransferItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;

class WarehouseTransferRepository implements WarehouseTransferInterface
{
    protected $model;

    public function __construct(WarehouseTransfer $model)
    {
        $this->model = $model;
    }

    /**
     * Get all warehouse transfers
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAll()
    {
        return $this->model->all();
    }

    /**
     * Get warehouse transfer by ID
     *
     * @param int $id
     * @return \App\Models\WarehouseTransfer
     */
    public function findById($id)
    {
        // Tải dữ liệu từ cơ sở dữ liệu và đảm bảo dữ liệu mới nhất
        $warehouseTransfer = $this->model->findOrFail($id);

        // Tải lại dữ liệu từ cơ sở dữ liệu để đảm bảo trạng thái mới nhất
        $warehouseTransfer->refresh();

        // Tải các quan hệ
        return $warehouseTransfer->load([
            'items.product',
            'items.sourceWarehouseArea.warehouse',
            'items.destinationWarehouseArea.warehouse',
            'items.serials',
            'sourceWarehouse',
            'destinationWarehouse',
            'transitWarehouse',
            'createdBy',
            'approvedBy',
            'receipt'
        ]);
    }

    /**
     * Create a new warehouse transfer
     *
     * @param array $data
     * @return array
     */
    public function createWarehouseTransfer(array $data)
    {
        try {
            DB::beginTransaction();

            // Tạo phiếu chuyển kho
            $warehouseTransfer = $this->model->create([
                'source_warehouse_id' => $data['source_warehouse_id'],
                'destination_warehouse_id' => $data['destination_warehouse_id'],
                'transit_warehouse_id' => $data['transit_warehouse_id'] ?? null,
                'expected_date' => $data['expected_date'] ?? null,
                'notes' => $data['notes'] ?? null,
                'status' => WarehouseTransfer::STATUS_DRAFT,
                'created_by' => Auth::id(),
            ]);

            // Tạo các mục trong phiếu chuyển kho
            if (isset($data['items']) && is_array($data['items'])) {
                foreach ($data['items'] as $item) {
                    WarehouseTransferItem::create([
                        'warehouse_transfer_id' => $warehouseTransfer->id,
                        'product_id' => $item['product_id'],
                        'source_warehouse_area_id' => $item['source_warehouse_area_id'] ?? null,
                        'destination_warehouse_area_id' => $item['destination_warehouse_area_id'] ?? null,
                        'quantity' => $item['quantity'],
                        'received_quantity' => 0,
                        'batch_number' => $item['batch_number'] ?? null,
                        'expiry_date' => $item['expiry_date'] ?? null,
                        'notes' => $item['notes'] ?? null,
                    ]);
                }
            }

            DB::commit();

            return [
                'success' => true,
                'message' => 'Phiếu chuyển kho đã được tạo thành công',
                'data' => $warehouseTransfer
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi tạo phiếu chuyển kho: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo phiếu chuyển kho: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update a warehouse transfer
     *
     * @param int $id
     * @param array $data
     * @return array
     */
    public function updateWarehouseTransfer($id, array $data)
    {
        try {
            DB::beginTransaction();

            $warehouseTransfer = $this->findById($id);

            // Kiểm tra trạng thái
            if ($warehouseTransfer->status !== WarehouseTransfer::STATUS_DRAFT) {
                return [
                    'success' => false,
                    'message' => 'Chỉ có thể cập nhật phiếu chuyển kho ở trạng thái nháp'
                ];
            }

            // Cập nhật phiếu chuyển kho
            $warehouseTransfer->update([
                'source_warehouse_id' => $data['source_warehouse_id'],
                'destination_warehouse_id' => $data['destination_warehouse_id'],
                'transit_warehouse_id' => $data['transit_warehouse_id'] ?? null,
                'expected_date' => $data['expected_date'] ?? null,
                'notes' => $data['notes'] ?? null,
            ]);

            // Cập nhật các mục trong phiếu chuyển kho
            if (isset($data['items']) && is_array($data['items'])) {
                // Xóa các mục cũ
                $warehouseTransfer->items()->delete();

                // Tạo các mục mới
                foreach ($data['items'] as $item) {
                    WarehouseTransferItem::create([
                        'warehouse_transfer_id' => $warehouseTransfer->id,
                        'product_id' => $item['product_id'],
                        'source_warehouse_area_id' => $item['source_warehouse_area_id'] ?? null,
                        'destination_warehouse_area_id' => $item['destination_warehouse_area_id'] ?? null,
                        'quantity' => $item['quantity'],
                        'received_quantity' => 0,
                        'batch_number' => $item['batch_number'] ?? null,
                        'expiry_date' => $item['expiry_date'] ?? null,
                        'notes' => $item['notes'] ?? null,
                    ]);
                }
            }

            DB::commit();

            return [
                'success' => true,
                'message' => 'Phiếu chuyển kho đã được cập nhật thành công',
                'data' => $warehouseTransfer
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi cập nhật phiếu chuyển kho: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật phiếu chuyển kho: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Submit a warehouse transfer for approval
     *
     * @param int $id
     * @return array
     */
    public function submitForApproval($id)
    {
        try {
            DB::beginTransaction();

            $warehouseTransfer = $this->findById($id);

            // Kiểm tra trạng thái
            if ($warehouseTransfer->status !== WarehouseTransfer::STATUS_DRAFT) {
                return [
                    'success' => false,
                    'message' => 'Chỉ có thể gửi duyệt phiếu chuyển kho ở trạng thái nháp'
                ];
            }

            // Kiểm tra xem đã quét đủ IMEI cho tất cả sản phẩm chưa
            if ($warehouseTransfer->has_serial_items && !$warehouseTransfer->is_fully_scanned) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Vui lòng quét đủ IMEI cho tất cả sản phẩm trước khi gửi duyệt'
                ];
            }

            // Kiểm tra số lượng tồn kho
            $sourceWarehouseId = $warehouseTransfer->source_warehouse_id;
            $invalidItems = [];

            foreach ($warehouseTransfer->items as $item) {
                $inventoryItem = InventoryItem::where('warehouse_id', $sourceWarehouseId)
                    ->where('product_id', $item->product_id)
                    ->first();

                if (!$inventoryItem || $inventoryItem->available_quantity < $item->quantity) {
                    $product = Product::find($item->product_id);
                    $invalidItems[] = $product->name . ' (Tồn kho: ' . ($inventoryItem ? $inventoryItem->available_quantity : 0) . ', Yêu cầu: ' . $item->quantity . ')';
                }
            }

            if (!empty($invalidItems)) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Không đủ số lượng tồn kho cho các sản phẩm sau: ' . implode(', ', $invalidItems)
                ];
            }

            // Cập nhật trạng thái
            $warehouseTransfer->update([
                'status' => WarehouseTransfer::STATUS_PENDING
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Phiếu chuyển kho đã được gửi duyệt thành công',
                'data' => $warehouseTransfer
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi gửi duyệt phiếu chuyển kho: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi gửi duyệt phiếu chuyển kho: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Approve a warehouse transfer
     *
     * @param int $id
     * @param int $userId
     * @return array
     */
    public function approveWarehouseTransfer($id, $userId)
    {
        try {
            DB::beginTransaction();

            $warehouseTransfer = $this->model->findOrFail($id);

            // Log trạng thái trước khi refresh
            Log::info('Trạng thái trước khi refresh: ' . $warehouseTransfer->status);

            // Truy vấn trực tiếp từ cơ sở dữ liệu để kiểm tra trạng thái thực tế
            $actualStatus = DB::table('warehouse_transfers')->where('id', $id)->value('status');
            Log::info('Trạng thái thực tế trong cơ sở dữ liệu: ' . $actualStatus);

            // Tải lại dữ liệu từ cơ sở dữ liệu để đảm bảo trạng thái mới nhất
            $warehouseTransfer->refresh();

            // Log trạng thái sau khi refresh
            Log::info('Trạng thái sau khi refresh: ' . $warehouseTransfer->status);
            Log::info('Trạng thái chờ duyệt: ' . WarehouseTransfer::STATUS_PENDING);

            // Nếu phiếu đã ở trạng thái approved, trả về thành công luôn
            if ($warehouseTransfer->status === WarehouseTransfer::STATUS_APPROVED) {
                DB::commit();
                return [
                    'code' => 200,
                    'text' => 'Phiếu chuyển kho đã được duyệt trước đó',
                    'data' => $warehouseTransfer,
                    'redirect' => route('warehouses.transfers.show', $warehouseTransfer->id)
                ];
            }

            // Kiểm tra trạng thái
            if ($warehouseTransfer->status !== WarehouseTransfer::STATUS_PENDING) {
                return [
                    'code' => 400,
                    'text' => 'Chỉ có thể duyệt phiếu chuyển kho ở trạng thái chờ duyệt. Trạng thái hiện tại: ' . $warehouseTransfer->status
                ];
            }

            // Lấy ID kho tạm từ config
            $transitWarehouseId = config('warehouse.transit_warehouse_id');

            // Kiểm tra xem kho tạm có tồn tại không
            $transitWarehouse = \App\Models\Warehouse::find($transitWarehouseId);
            if (!$transitWarehouse) {
                return [
                    'code' => 400,
                    'text' => 'Kho tạm không tồn tại. Vui lòng kiểm tra cấu hình.'
                ];
            }

            // Cập nhật trạng thái
            $warehouseTransfer->update([
                'status' => WarehouseTransfer::STATUS_APPROVED,
                'approved_by' => $userId,
                'approved_at' => now(),
                'transit_warehouse_id' => $transitWarehouseId // Lưu ID kho tạm vào phiếu chuyển kho
            ]);

            // Tải lại dữ liệu từ cơ sở dữ liệu để đảm bảo trạng thái mới nhất
            $warehouseTransfer->refresh();

            // Cập nhật tồn kho: giảm tồn kho nguồn, tăng tồn kho tạm
            foreach ($warehouseTransfer->items as $item) {
                // Giảm tồn kho nguồn
                $this->updateInventory(
                    $warehouseTransfer->source_warehouse_id,
                    $item->source_warehouse_area_id,
                    $item->product_id,
                    -$item->quantity,
                    'Giảm tồn kho nguồn do phiếu chuyển kho #' . $warehouseTransfer->id,
                    $warehouseTransfer
                );

                // Tăng tồn kho tạm
                $this->updateInventory(
                    $transitWarehouseId,
                    null, // Không có khu vực cụ thể trong kho tạm
                    $item->product_id,
                    $item->quantity,
                    'Tăng tồn kho tạm do phiếu chuyển kho #' . $warehouseTransfer->id,
                    $warehouseTransfer
                );
            }

            DB::commit();

            return [
                'code' => 200,
                'text' => 'Phiếu chuyển kho đã được duyệt thành công',
                'data' => $warehouseTransfer,
                'redirect' => route('warehouses.transfers.show', $warehouseTransfer->id)
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi duyệt phiếu chuyển kho: ' . $e->getMessage());

            return [
                'code' => 500,
                'text' => 'Có lỗi xảy ra khi duyệt phiếu chuyển kho: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Cập nhật tồn kho
     *
     * @param int $warehouseId ID kho hàng
     * @param int|null $warehouseAreaId ID khu vực kho (có thể null)
     * @param int $productId ID sản phẩm
     * @param float $quantity Số lượng (dương nếu tăng, âm nếu giảm)
     * @param string $notes Ghi chú
     * @param WarehouseTransfer $warehouseTransfer Phiếu chuyển kho liên quan
     * @return void
     */
    private function updateInventory($warehouseId, $warehouseAreaId, $productId, $quantity, $notes, $warehouseTransfer)
    {
        // Log thông tin đầu vào
        Log::info('Cập nhật tồn kho:', [
            'warehouse_id' => $warehouseId,
            'warehouse_area_id' => $warehouseAreaId,
            'product_id' => $productId,
            'quantity' => $quantity
        ]);

        try {
            // Truy vấn trực tiếp vào cơ sở dữ liệu để kiểm tra xem đã có bản ghi nào với warehouse_id và product_id này chưa
            $existingItem = DB::table('inventory_items')
                ->where('warehouse_id', $warehouseId)
                ->where('product_id', $productId)
                ->first();

            if ($existingItem) {
                // Nếu đã có bản ghi, cập nhật số lượng
                Log::info('Tìm thấy bản ghi tồn kho hiện có:', ['id' => $existingItem->id]);

                // Cập nhật trực tiếp vào cơ sở dữ liệu
                DB::table('inventory_items')
                    ->where('id', $existingItem->id)
                    ->update([
                        'quantity' => DB::raw("quantity + {$quantity}"),
                        'available_quantity' => DB::raw("available_quantity + {$quantity}"),
                        'updated_at' => now()
                    ]);

                // Lấy bản ghi đã cập nhật
                $updatedItem = DB::table('inventory_items')
                    ->where('id', $existingItem->id)
                    ->first();

                // Tạo giao dịch tồn kho
                InventoryTransaction::create([
                    'warehouse_id' => $warehouseId,
                    'warehouse_area_id' => $warehouseAreaId,
                    'product_id' => $productId,
                    'quantity' => $quantity,
                    'balance' => $updatedItem->quantity,
                    'transaction_type' => $quantity > 0 ? 'in' : 'out',
                    'reference_type' => WarehouseTransfer::class,
                    'reference_id' => $warehouseTransfer->id,
                    'notes' => $notes,
                    'transaction_date' => Carbon::now(),
                ]);

                Log::info('Cập nhật tồn kho thành công:', [
                    'inventory_item_id' => $existingItem->id,
                    'new_quantity' => $updatedItem->quantity
                ]);
            } else {
                // Nếu chưa có bản ghi, tạo mới
                Log::info('Tạo mới bản ghi tồn kho');

                // Tạo mới bản ghi tồn kho
                $newItemId = DB::table('inventory_items')->insertGetId([
                    'warehouse_id' => $warehouseId,
                    'warehouse_area_id' => $warehouseAreaId,
                    'product_id' => $productId,
                    'quantity' => $quantity,
                    'available_quantity' => $quantity,
                    'reserved_quantity' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                // Tạo giao dịch tồn kho
                InventoryTransaction::create([
                    'warehouse_id' => $warehouseId,
                    'warehouse_area_id' => $warehouseAreaId,
                    'product_id' => $productId,
                    'quantity' => $quantity,
                    'balance' => $quantity,
                    'transaction_type' => $quantity > 0 ? 'in' : 'out',
                    'reference_type' => WarehouseTransfer::class,
                    'reference_id' => $warehouseTransfer->id,
                    'notes' => $notes,
                    'transaction_date' => Carbon::now(),
                ]);

                Log::info('Tạo mới tồn kho thành công:', [
                    'inventory_item_id' => $newItemId,
                    'quantity' => $quantity
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Lỗi khi cập nhật tồn kho: ' . $e->getMessage(), [
                'warehouse_id' => $warehouseId,
                'warehouse_area_id' => $warehouseAreaId,
                'product_id' => $productId,
                'quantity' => $quantity,
                'exception' => $e
            ]);
            throw $e; // Re-throw để xử lý ở lớp trên
        }
    }

    /**
     * Cancel a warehouse transfer
     *
     * @param int $id
     * @param int $userId
     * @param string|null $reason
     * @return array
     */
    public function cancelWarehouseTransfer($id, $userId, $reason = null)
    {
        try {
            DB::beginTransaction();

            $warehouseTransfer = $this->model->findOrFail($id);

            // Log trạng thái trước khi refresh
            Log::info('Hủy phiếu chuyển kho #' . $id . ' - Trạng thái trước khi refresh: ' . $warehouseTransfer->status);

            // Truy vấn trực tiếp từ cơ sở dữ liệu để kiểm tra trạng thái thực tế
            $actualStatus = DB::table('warehouse_transfers')->where('id', $id)->value('status');
            Log::info('Trạng thái thực tế trong cơ sở dữ liệu: ' . $actualStatus);

            // Tải lại dữ liệu từ cơ sở dữ liệu để đảm bảo trạng thái mới nhất
            $warehouseTransfer->refresh();

            // Log trạng thái sau khi refresh
            Log::info('Trạng thái sau khi refresh: ' . $warehouseTransfer->status);
            Log::info('Trạng thái chờ duyệt: ' . WarehouseTransfer::STATUS_PENDING);
            Log::info('Trạng thái nháp: ' . WarehouseTransfer::STATUS_DRAFT);
            Log::info('Kiểm tra trạng thái có hợp lệ không: ' . (in_array($warehouseTransfer->status, [WarehouseTransfer::STATUS_DRAFT, WarehouseTransfer::STATUS_PENDING]) ? 'true' : 'false'));

            // Nếu trạng thái trong cơ sở dữ liệu khác với trạng thái trong model, sử dụng trạng thái trong cơ sở dữ liệu
            if ($actualStatus !== $warehouseTransfer->status) {
                Log::warning('Phát hiện sự khác biệt giữa trạng thái trong model và cơ sở dữ liệu. Sử dụng trạng thái trong cơ sở dữ liệu.');
                $warehouseTransfer->status = $actualStatus;
            }

            // Kiểm tra trạng thái
            if (!in_array($warehouseTransfer->status, [WarehouseTransfer::STATUS_DRAFT, WarehouseTransfer::STATUS_PENDING])) {
                Log::warning('Không thể hủy phiếu chuyển kho #' . $id . ' vì trạng thái không hợp lệ: ' . $warehouseTransfer->status);
                return [
                    'code' => 400,
                    'text' => 'Chỉ có thể hủy phiếu chuyển kho ở trạng thái nháp hoặc chờ duyệt. Trạng thái hiện tại: ' . $warehouseTransfer->status
                ];
            }

            // Cập nhật trạng thái
            $warehouseTransfer->status = WarehouseTransfer::STATUS_CANCELLED;
            $warehouseTransfer->cancelled_by = $userId;
            $warehouseTransfer->cancelled_at = now();
            $warehouseTransfer->notes = $warehouseTransfer->notes . "\n\nLý do hủy: " . ($reason ?? 'Không có lý do');
            $warehouseTransfer->save();

            DB::commit();

            return [
                'code' => 200,
                'text' => 'Phiếu chuyển kho đã được hủy thành công',
                'data' => $warehouseTransfer,
                'redirect' => route('warehouses.transfers.show', $warehouseTransfer->id)
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi hủy phiếu chuyển kho: ' . $e->getMessage());

            return [
                'code' => 500,
                'text' => 'Có lỗi xảy ra khi hủy phiếu chuyển kho: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create shipping document for a warehouse transfer
     *
     * @param int $id
     * @param int $userId
     * @param array $data
     * @return array
     */
    public function createShippingDocument($id, $userId, array $data)
    {
        try {
            DB::beginTransaction();

            $warehouseTransfer = $this->findById($id);

            // Kiểm tra trạng thái
            if ($warehouseTransfer->status !== WarehouseTransfer::STATUS_APPROVED) {
                return [
                    'code' => 400,
                    'text' => 'Chỉ có thể tạo phiếu vận chuyển cho phiếu chuyển kho đã được duyệt'
                ];
            }

            // Cập nhật thông tin vận chuyển
            $warehouseTransfer->shipping_date = $data['shipping_date'] ?? now()->format('Y-m-d');
            $warehouseTransfer->shipping_notes = $data['shipping_notes'] ?? null;
            $warehouseTransfer->status = WarehouseTransfer::STATUS_SHIPPING;
            $warehouseTransfer->save();

            // Tạo giao dịch xuất kho từ kho nguồn
            $sourceWarehouseId = $warehouseTransfer->source_warehouse_id;
            $transitWarehouseId = $warehouseTransfer->transit_warehouse_id;

            foreach ($warehouseTransfer->items as $item) {
                // Tạo giao dịch xuất kho
                InventoryTransaction::create([
                    'warehouse_id' => $sourceWarehouseId,
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity,
                    'type' => InventoryTransaction::TYPE_TRANSFER_OUT,
                    'batch_number' => $item->batch_number,
                    'expiry_date' => $item->expiry_date,
                    'related_warehouse_id' => $transitWarehouseId,
                    'notes' => 'Xuất kho theo phiếu chuyển kho ' . $warehouseTransfer->code,
                    'created_by' => $userId,
                    'warehouse_area_id' => $item->source_warehouse_area_id,
                    'reference_type' => 'App\Models\WarehouseTransfer',
                    'reference_id' => $warehouseTransfer->id
                ]);

                // Cập nhật tồn kho tại kho nguồn
                $inventoryItem = InventoryItem::where('warehouse_id', $sourceWarehouseId)
                    ->where('product_id', $item->product_id)
                    ->first();

                if ($inventoryItem) {
                    $inventoryItem->quantity -= $item->quantity;
                    $inventoryItem->available_quantity -= $item->quantity;
                    $inventoryItem->last_updated_by = $userId;
                    $inventoryItem->save();
                }

                // Tạo giao dịch nhập kho vào kho trung gian
                InventoryTransaction::create([
                    'warehouse_id' => $transitWarehouseId,
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity,
                    'type' => InventoryTransaction::TYPE_TRANSFER_IN,
                    'batch_number' => $item->batch_number,
                    'expiry_date' => $item->expiry_date,
                    'related_warehouse_id' => $sourceWarehouseId,
                    'notes' => 'Nhập kho trung gian theo phiếu chuyển kho ' . $warehouseTransfer->code,
                    'created_by' => $userId,
                    'warehouse_area_id' => null,
                    'reference_type' => 'App\Models\WarehouseTransfer',
                    'reference_id' => $warehouseTransfer->id
                ]);

                // Cập nhật hoặc tạo mới tồn kho tại kho trung gian
                $transitInventoryItem = InventoryItem::where('warehouse_id', $transitWarehouseId)
                    ->where('product_id', $item->product_id)
                    ->first();

                if ($transitInventoryItem) {
                    $transitInventoryItem->quantity += $item->quantity;
                    $transitInventoryItem->available_quantity += $item->quantity;
                    $transitInventoryItem->last_updated_by = $userId;
                    $transitInventoryItem->save();
                } else {
                    InventoryItem::create([
                        'warehouse_id' => $transitWarehouseId,
                        'product_id' => $item->product_id,
                        'quantity' => $item->quantity,
                        'available_quantity' => $item->quantity,
                        'reserved_quantity' => 0,
                        'last_updated_by' => $userId
                    ]);
                }
            }

            DB::commit();

            return [
                'code' => 200,
                'text' => 'Phiếu vận chuyển đã được tạo thành công',
                'data' => $warehouseTransfer,
                'redirect' => route('warehouses.transfers.show', $warehouseTransfer->id)
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi tạo phiếu vận chuyển: ' . $e->getMessage());

            return [
                'code' => 500,
                'text' => 'Có lỗi xảy ra khi tạo phiếu vận chuyển: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create receipt for a warehouse transfer
     *
     * @param int $id
     * @param int $userId
     * @param array $data
     * @return array
     */
    public function createReceipt($id, $userId, array $data)
    {
        try {
            DB::beginTransaction();

            $warehouseTransfer = $this->findById($id);

            // Kiểm tra trạng thái
            if ($warehouseTransfer->status !== WarehouseTransfer::STATUS_SHIPPING) {
                return [
                    'code' => 400,
                    'text' => 'Chỉ có thể tạo phiếu nhận hàng cho phiếu chuyển kho đang vận chuyển'
                ];
            }

            // Kiểm tra xem đã có phiếu nhận hàng chưa
            if ($warehouseTransfer->receipt) {
                return [
                    'code' => 400,
                    'text' => 'Đã tạo phiếu nhận hàng cho phiếu chuyển kho này'
                ];
            }

            // Tạo phiếu nhận hàng
            $warehouseReceipt = WarehouseReceipt::create([
                'warehouse_transfer_id' => $warehouseTransfer->id,
                'warehouse_id' => $warehouseTransfer->destination_warehouse_id,
                'status' => WarehouseReceipt::STATUS_PENDING,
                'notes' => $data['notes'] ?? null,
                'receipt_date' => $data['receipt_date'] ?? now()->format('Y-m-d'),
                'created_by' => $userId
            ]);

            DB::commit();

            return [
                'code' => 200,
                'text' => 'Phiếu nhận hàng đã được tạo thành công',
                'data' => $warehouseReceipt,
                'redirect' => route('warehouses.receipts.show', $warehouseReceipt->id)
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi tạo phiếu nhận hàng: ' . $e->getMessage());

            return [
                'code' => 500,
                'text' => 'Có lỗi xảy ra khi tạo phiếu nhận hàng: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get warehouse transfers for datatable
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWarehouseTransfersForDatatable(Request $request)
    {
        try {
            // Sử dụng query builder trực tiếp để tối ưu hiệu suất
            $query = DB::table('warehouse_transfers')
                ->select(
                    'warehouse_transfers.id',
                    'warehouse_transfers.code',
                    'warehouse_transfers.source_warehouse_id',
                    'warehouse_transfers.destination_warehouse_id',
                    'warehouse_transfers.transit_warehouse_id',
                    'warehouse_transfers.status',
                    'warehouse_transfers.expected_date',
                    'warehouse_transfers.created_by',
                    'warehouse_transfers.created_at',
                    'source_warehouse.name as source_warehouse_name',
                    'destination_warehouse.name as destination_warehouse_name',
                    'transit_warehouse.name as transit_warehouse_name',
                    'users.name as created_by_name'
                )
                ->leftJoin('warehouses as source_warehouse', 'warehouse_transfers.source_warehouse_id', '=', 'source_warehouse.id')
                ->leftJoin('warehouses as destination_warehouse', 'warehouse_transfers.destination_warehouse_id', '=', 'destination_warehouse.id')
                ->leftJoin('warehouses as transit_warehouse', 'warehouse_transfers.transit_warehouse_id', '=', 'transit_warehouse.id')
                ->leftJoin('users', 'warehouse_transfers.created_by', '=', 'users.id')
                ->whereNull('warehouse_transfers.deleted_at');

            // Tổng số bản ghi trước khi lọc
            $recordsTotal = $query->count();

            // Filter by source warehouse
            if ($request->has('source_warehouse_id') && !empty($request->source_warehouse_id)) {
                $query->where('warehouse_transfers.source_warehouse_id', $request->source_warehouse_id);
            }

            // Filter by destination warehouse
            if ($request->has('destination_warehouse_id') && !empty($request->destination_warehouse_id)) {
                $query->where('warehouse_transfers.destination_warehouse_id', $request->destination_warehouse_id);
            }

            // Filter by status
            if ($request->has('status') && !empty($request->status)) {
                $query->where('warehouse_transfers.status', $request->status);
            }

            // Filter by date range
            if ($request->has('date_from') && !empty($request->date_from)) {
                $date = \DateTime::createFromFormat('d/m/Y', $request->date_from);
                if ($date) {
                    $query->whereDate('warehouse_transfers.created_at', '>=', $date->format('Y-m-d'));
                }
            }

            if ($request->has('date_to') && !empty($request->date_to)) {
                $date = \DateTime::createFromFormat('d/m/Y', $request->date_to);
                if ($date) {
                    $query->whereDate('warehouse_transfers.created_at', '<=', $date->format('Y-m-d'));
                }
            }

            // Search
            if ($request->has('search') && !empty($request->search['value'])) {
                $searchValue = $request->search['value'];
                $query->where(function ($q) use ($searchValue) {
                    $q->where('warehouse_transfers.code', 'like', "%{$searchValue}%")
                        ->orWhere('source_warehouse.name', 'like', "%{$searchValue}%")
                        ->orWhere('destination_warehouse.name', 'like', "%{$searchValue}%")
                        ->orWhere('transit_warehouse.name', 'like', "%{$searchValue}%");
                });
            }

            // Tổng số bản ghi sau khi lọc
            $recordsFiltered = $query->count();

            // Datatable order
            if ($request->has('order') && !empty($request->order)) {
                $columns = [
                    1 => 'warehouse_transfers.code',
                    2 => 'source_warehouse.name',
                    3 => 'destination_warehouse.name',
                    4 => 'transit_warehouse.name',
                    5 => 'warehouse_transfers.status',
                    6 => 'warehouse_transfers.expected_date',
                    7 => 'warehouse_transfers.created_at'
                ];

                $orderColumn = $request->order[0]['column'];
                $orderDir = $request->order[0]['dir'];

                if (isset($columns[$orderColumn])) {
                    $query->orderBy($columns[$orderColumn], $orderDir);
                }
            } else {
                $query->orderBy('warehouse_transfers.created_at', 'desc');
            }

            // Pagination
            $start = $request->start ?? 0;
            $length = $request->length ?? 10;

            // Sử dụng LIMIT và OFFSET trực tiếp trong câu lệnh SQL để tránh lỗi syntax
            $sql = $query->toSql();
            $bindings = $query->getBindings();

            // Thêm LIMIT và OFFSET vào câu lệnh SQL
            $sql .= " LIMIT {$length} OFFSET {$start}";

            // Thực thi câu lệnh SQL với bindings
            $transfers = DB::select($sql, $bindings);

            // Format data
            $data = [];
            $statusMap = [
                'draft' => 'Nháp',
                'pending' => 'Chờ duyệt',
                'approved' => 'Đã duyệt',
                'shipping' => 'Đang vận chuyển',
                'completed' => 'Hoàn thành',
                'cancelled' => 'Đã hủy'
            ];

            $colorMap = [
                'draft' => 'secondary',
                'pending' => 'warning',
                'approved' => 'info',
                'shipping' => 'primary',
                'completed' => 'success',
                'cancelled' => 'danger'
            ];

            foreach ($transfers as $index => $transfer) {
                $statusText = $statusMap[$transfer->status] ?? 'Không xác định';
                $statusColor = $colorMap[$transfer->status] ?? 'secondary';

                $actions = '<div class="d-flex align-items-center">';

                if (auth()->user()->can('warehouses.transfers.view')) {
                    $actions .= '<a href="' . route('warehouses.transfers.show', $transfer->id) . '" class="btn btn-sm btn-icon me-2"><i class="ri-eye-line"></i></a>';
                }

                // Đã xóa nút chỉnh sửa phiếu chuyển kho

                $actions .= '</div>';

                $data[] = [
                    'DT_RowIndex' => $start + $index + 1,
                    'code' => $transfer->code,
                    'source_warehouse' => $transfer->source_warehouse_name,
                    'destination_warehouse' => $transfer->destination_warehouse_name,
                    'transit_warehouse' => $transfer->transit_warehouse_name,
                    'status' => '<span class="badge bg-' . $statusColor . '">' . $statusText . '</span>',
                    'expected_date' => $transfer->expected_date ? date('d/m/Y', strtotime($transfer->expected_date)) : '',
                    'created_at' => date('d/m/Y', strtotime($transfer->created_at)),
                    'actions' => $actions
                ];
            }

            return response()->json([
                'draw' => intval($request->draw),
                'recordsTotal' => $recordsTotal,
                'recordsFiltered' => $recordsFiltered,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getWarehouseTransfersForDatatable: ' . $e->getMessage());
            return response()->json([
                'draw' => intval($request->draw),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => 'Đã xảy ra lỗi khi tải dữ liệu'
            ]);
        }
    }

    /**
     * Get products in warehouse
     *
     * @param int $warehouseId
     * @param string|null $search
     * @return array
     */
    public function getProductsInWarehouse($warehouseId, $search = null)
    {
        $query = DB::table('warehouse_inventories')
            ->select(
                'warehouse_inventories.product_id',
                'warehouse_inventories.quantity',
                'products.name as product_name',
                'products.code as product_code',
                'products.unit as product_unit'
            )
            ->join('products', 'warehouse_inventories.product_id', '=', 'products.id')
            ->where('warehouse_inventories.warehouse_id', $warehouseId)
            ->where('warehouse_inventories.quantity', '>', 0)
            ->whereNull('warehouse_inventories.deleted_at')
            ->whereNull('products.deleted_at');

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('products.name', 'like', "%{$search}%")
                  ->orWhere('products.code', 'like', "%{$search}%");
            });
        }

        $inventories = $query->get();

        $products = [];
        foreach ($inventories as $inventory) {
            $products[] = [
                'id' => $inventory->product_id,
                'text' => $inventory->product_name . ' (' . $inventory->product_code . ')',
                'code' => $inventory->product_code,
                'name' => $inventory->product_name,
                'unit' => $inventory->product_unit,
                'available_quantity' => $inventory->quantity
            ];
        }

        return $products;
    }

    /**
     * Get product details in warehouse
     *
     * @param int $warehouseId
     * @param int $productId
     * @return array
     */
    public function getProductDetailsInWarehouse($warehouseId, $productId)
    {
        // Lấy thông tin sản phẩm
        $product = Product::findOrFail($productId);

        // Lấy thông tin tồn kho
        $inventory = DB::table('warehouse_inventories')
            ->where('warehouse_id', $warehouseId)
            ->where('product_id', $productId)
            ->whereNull('deleted_at')
            ->first();

        // Lấy danh sách khu vực kho
        $warehouseAreas = WarehouseArea::where('warehouse_id', $warehouseId)
            ->orderBy('name')
            ->get()
            ->map(function ($area) {
                return [
                    'id' => $area->id,
                    'name' => $area->name
                ];
            })
            ->toArray();

        return [
            'product' => [
                'id' => $product->id,
                'name' => $product->name,
                'code' => $product->code,
                'unit' => $product->unit,
                'available_quantity' => $inventory ? $inventory->quantity : 0
            ],
            'warehouse_areas' => $warehouseAreas
        ];
    }
}
