<?php

namespace App\Repositories;

use App\Contracts\ProductInterface;
use App\Helpers\Classes\Helper;
use App\Models\Product;
use App\Models\ProductAttribute;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductRepository extends BaseRepository implements ProductInterface
{
    protected $model;

    /**
     * ProductRepository constructor.
     *
     * @param Product $product
     */
    public function __construct(Product $product)
    {
        parent::__construct($product);
    }

    /**
     * Lấy tất cả sản phẩm
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllProducts()
    {
        return $this->model->all();
    }

    /**
     * Lấy sản phẩm theo ID
     *
     * @param int $id
     * @return \App\Models\Product|null
     */
    public function getProductById($id)
    {
        return $this->model->find($id);
    }

    /**
     * Tạo sản phẩm mới
     *
     * @param array $data
     * @return \App\Models\Product
     */
    public function createProduct(array $data)
    {
        try {
            DB::beginTransaction();

            // Handle image upload using Helper
            if (isset($data['image']) && $data['image']) {
                $file = $data['image'];
                $filename = Str::slug($data['name']) . '-' . time();
                $savedFilename = Helper::saveFile($file, 'products', 'ProductRepository', $filename);
                if ($savedFilename) {
                    $data['image'] = 'products/' . $savedFilename;
                }
            } else {
                // Sử dụng hình ảnh mặc định khi không có hình ảnh được tải lên
                $data['image'] = 'assets/img/products/image-placeholder.jpg';
            }

            // Handle dimensions
            if (isset($data['length']) || isset($data['width']) || isset($data['height'])) {
                // Make sure we're working with numeric values
                $length = isset($data['length']) ? (float)$data['length'] : 0;
                $width = isset($data['width']) ? (float)$data['width'] : 0;
                $height = isset($data['height']) ? (float)$data['height'] : 0;

                // Create dimensions array
                $dimensions = [
                    'length' => $length,
                    'width' => $width,
                    'height' => $height
                ];

                // Store as JSON string
                $data['dimensions'] = json_encode($dimensions);

                unset($data['length'], $data['width'], $data['height']);
            }

            // Đảm bảo không có attributes trong data
            if (isset($data['attributes'])) {
                unset($data['attributes']);
            }

            // Tạo sản phẩm
            $product = $this->model->create($data);

            DB::commit();
            return $product;
        } catch (\Exception $e) {
            DB::rollBack();
            createErrorLog($e, 'daily', 'ProductRepository::createProduct');
            throw $e;
        }
    }

    /**
     * Cập nhật sản phẩm
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateProduct($id, array $data)
    {
        try {
            DB::beginTransaction();

            $product = $this->model->findOrFail($id);

            // Handle image upload using Helper
            if (isset($data['image']) && $data['image']) {
                // Delete old image if exists
                if ($product->image && Storage::disk('public')->exists($product->image) && strpos($product->image, 'products/') === 0) {
                    Storage::disk('public')->delete($product->image);
                }

                $file = $data['image'];
                $filename = Str::slug($data['name']) . '-' . time();
                $savedFilename = Helper::saveFile($file, 'products', 'ProductRepository', $filename, true);
                if ($savedFilename) {
                    $data['image'] = 'products/' . $savedFilename;
                }
            } else if (isset($data['remove_image']) && $data['remove_image']) {
                // Xóa hình ảnh hiện tại và sử dụng hình ảnh mặc định
                if ($product->image && Storage::disk('public')->exists($product->image) && strpos($product->image, 'products/') === 0) {
                    Storage::disk('public')->delete($product->image);
                }
                $data['image'] = 'assets/img/products/image-placeholder.jpg';
                unset($data['remove_image']);
            } else {
                // No new image, keep the old one
                unset($data['image']);
                if (isset($data['remove_image'])) {
                    unset($data['remove_image']);
                }
            }

            // Handle dimensions
            if (isset($data['length']) || isset($data['width']) || isset($data['height'])) {
                // Make sure we're working with numeric values
                $length = isset($data['length']) ? (float)$data['length'] : 0;
                $width = isset($data['width']) ? (float)$data['width'] : 0;
                $height = isset($data['height']) ? (float)$data['height'] : 0;

                // Create dimensions array
                $dimensions = [
                    'length' => $length,
                    'width' => $width,
                    'height' => $height
                ];

                // Store as JSON string
                $data['dimensions'] = json_encode($dimensions);

                unset($data['length'], $data['width'], $data['height']);
            }

            // Update product
            $product->update($data);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            createErrorLog($e, 'daily', 'ProductRepository::updateProduct');
            throw $e;
        }
    }

    /**
     * Xóa sản phẩm
     *
     * @param int $id
     * @return bool
     */
    public function deleteProduct($id)
    {
        try {
            // Tăng giới hạn bộ nhớ tạm thời cho thao tác xóa
            ini_set('memory_limit', '512M');

            // Sử dụng DB transaction để đảm bảo tính nhất quán dữ liệu
            DB::beginTransaction();

            // Kiểm tra sản phẩm có trong kho không
            if ($this->hasInventory($id)) {
                throw new \Exception('Không thể xóa sản phẩm đang có trong kho');
            }

            // Lấy thông tin hình ảnh sản phẩm (nếu cần) trước khi xóa
            $productImage = DB::table('products')
                ->where('id', $id)
                ->value('image');

            // Xóa các bản ghi liên quan trước để tránh tràn bộ nhớ
            // Xóa các thuộc tính sản phẩm
            DB::table('product_attributes')
                ->where('product_id', $id)
                ->delete();

            // Xóa hình ảnh nếu có
            if ($productImage && Storage::disk('public')->exists($productImage)) {
                Storage::disk('public')->delete($productImage);
            }

            // Xóa sản phẩm (soft delete)
            DB::table('products')
                ->where('id', $id)
                ->update(['deleted_at' => now()]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            createErrorLog($e, 'daily', 'ProductRepository::deleteProduct');
            throw $e;
        } finally {
            // Khôi phục giới hạn bộ nhớ về mặc định
            if (env('MEMORY_LIMIT')) {
                ini_set('memory_limit', env('MEMORY_LIMIT'));
            }
        }
    }

    /**
     * Kiểm tra sản phẩm có tồn tại trong kho không
     *
     * @param int $id
     * @return bool
     */
    public function hasInventory($id)
    {
        return DB::table('inventory_items')
            ->where('product_id', $id)
            ->exists();
    }

    /**
     * Lấy dữ liệu sản phẩm cho datatable
     *
     * @param array $params
     * @return array
     */
    public function getProductsForDatatable(array $params)
    {
        try {
            // Sử dụng DB facade thay vì Eloquent model để tránh vấn đề với quan hệ
            $query = DB::table('products')
                ->leftJoin('categories', 'products.category_id', '=', 'categories.id')
                ->leftJoin('brands', 'products.brand_id', '=', 'brands.id')
                ->select(
                    'products.id',
                    'products.code',
                    'products.name',
                    'products.price',
                    'products.cost',
                    'products.barcode',
                    'products.inventory_tracking_type',
                    'products.image',
                    'products.is_active',
                    'categories.name as category_name',
                    'brands.name as brand_name'
                )
                ->whereNull('products.deleted_at');

            // Lọc theo danh mục
            if (isset($params['category_id']) && !empty($params['category_id'])) {
                $query->where('products.category_id', $params['category_id']);
            }

            // Lọc theo thương hiệu
            if (isset($params['brand_id']) && !empty($params['brand_id'])) {
                $query->where('products.brand_id', $params['brand_id']);
            }

            // Lọc theo trạng thái
            if (isset($params['status']) && $params['status'] !== 'all') {
                $query->where('products.is_active', $params['status'] === 'active');
            }

            // Lọc theo mã sản phẩm
            if (isset($params['code']) && !empty($params['code'])) {
                $query->where('products.code', 'like', "%{$params['code']}%");
            }

            // Lọc theo mã vạch
            if (isset($params['barcode']) && !empty($params['barcode'])) {
                $query->where('products.barcode', 'like', "%{$params['barcode']}%");
            }

            // Lọc theo loại quản lý kho
            if (isset($params['inventory_type']) && !empty($params['inventory_type'])) {
                $query->where('products.inventory_tracking_type', $params['inventory_type']);
            }

            // Lọc theo khoảng giá bán
            if (isset($params['min_price']) && !empty($params['min_price'])) {
                $query->where('products.price', '>=', floatval($params['min_price']));
            }

            if (isset($params['max_price']) && !empty($params['max_price'])) {
                $query->where('products.price', '<=', floatval($params['max_price']));
            }

            // Lọc theo khoảng giá vốn
            if (isset($params['min_cost']) && !empty($params['min_cost'])) {
                $query->where('products.cost', '>=', floatval($params['min_cost']));
            }

            if (isset($params['max_cost']) && !empty($params['max_cost'])) {
                $query->where('products.cost', '<=', floatval($params['max_cost']));
            }

            // Tìm kiếm
            if (isset($params['search']) && !empty($params['search']['value'])) {
                $searchValue = $params['search']['value'];
                $query->where(function ($q) use ($searchValue) {
                    $q->where('products.name', 'like', "%{$searchValue}%")
                        ->orWhere('products.code', 'like', "%{$searchValue}%")
                        ->orWhere('products.description', 'like', "%{$searchValue}%");
                });
            }

            // Đếm tổng số bản ghi (trước khi phân trang)
            $recordsTotal = DB::table('products')->whereNull('deleted_at')->count();

            // Lưu lại query để đếm số bản ghi sau khi lọc
            $countQuery = clone $query;
            $recordsFiltered = $countQuery->count();

            // Sắp xếp
            if (isset($params['order']) && !empty($params['order'])) {
                $order = $params['order'][0];
                $columnIndex = $order['column'];

                if (isset($params['columns']) && isset($params['columns'][$columnIndex]['data'])) {
                    $columnName = $params['columns'][$columnIndex]['data'];
                    $columnDirection = $order['dir'];

                    if ($columnName === 'category') {
                        $query->orderBy('categories.name', $columnDirection);
                    } else if ($columnName === 'brand') {
                        $query->orderBy('brands.name', $columnDirection);
                    } else if (in_array($columnName, ['id', 'code', 'name', 'price', 'is_active'])) {
                        $query->orderBy('products.' . $columnName, $columnDirection);
                    }
                }
            } else {
                $query->orderBy('products.name', 'asc');
            }

            // Phân trang
            $start = $params['start'] ?? 0;
            $length = $params['length'] ?? 50; // Tăng giá trị mặc định lên 50

            if ($length != -1) {
                $query->skip($start)->take($length);
            } else {
                $query->skip($start)->take(10000); // Tăng giới hạn lên 10000 bản ghi
            }

            // Lấy dữ liệu
            $products = $query->get();

            // Xử lý dữ liệu
            $data = [];
            foreach ($products as $product) {
                $data[] = [
                    'id' => $product->id,
                    'code' => $product->code,
                    'name' => $product->name,
                    'category' => $product->category_name ?? 'N/A',
                    'brand' => $product->brand_name ?? 'N/A',
                    'price' => $product->price,
                    'image' => $product->image ?
                        (strpos($product->image, 'assets/') === 0 ? asset($product->image) : asset('storage/' . $product->image)) :
                        asset('assets/img/products/image-placeholder.jpg'),
                    'is_active' => (bool)$product->is_active,
                    'actions' => '' // Will be rendered on the client side
                ];
            }

            return [
                'draw' => intval($params['draw'] ?? 1),
                'recordsTotal' => $recordsTotal,
                'recordsFiltered' => $recordsFiltered,
                'data' => $data
            ];
        } catch (\Exception $e) {
            // Ghi log lỗi
            Log::error('Error in getProductsForDatatable: ' . $e->getMessage());

            // Trả về phản hồi rỗng
            return [
                'draw' => intval($params['draw'] ?? 1),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => []
            ];
        }
    }

    /**
     * Lưu thuộc tính sản phẩm
     *
     * @param int $productId
     * @param array $attributes
     * @return bool
     */
    public function saveProductAttributes($productId, array $attributes)
    {
        try {
            DB::beginTransaction();

            // Xóa thuộc tính hiện tại
            ProductAttribute::where('product_id', $productId)->delete();

            // Thêm thuộc tính mới
            $attributesToInsert = [];
            foreach ($attributes as $attribute) {
                if (empty($attribute['attribute_id'])) continue;

                $attributeData = [
                    'product_id' => $productId,
                    'attribute_id' => $attribute['attribute_id'],
                    'attribute_value_id' => !empty($attribute['attribute_value_id'])
                        ? $attribute['attribute_value_id'] : null,
                    'text_value' => $attribute['text_value'] ?? null
                ];

                $attributesToInsert[] = $attributeData;
            }

            // Batch insert all attributes at once if there are any
            if (!empty($attributesToInsert)) {
                // Insert in chunks to avoid memory issues with large datasets
                foreach (array_chunk($attributesToInsert, 100) as $chunk) {
                    ProductAttribute::insert($chunk);
                }
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            createErrorLog($e, 'daily', 'ProductRepository::saveProductAttributes');
            throw $e;
        }
    }

    /**
     * Tạo thumbnail cho hình ảnh sản phẩm
     *
     * @param string $imagePath
     * @param int $width
     * @param int $height
     * @return string|null
     */
    public function createProductThumbnail($imagePath, $width = 300, $height = 300)
    {
        try {
            if (!$imagePath) {
                return null;
            }

            // Sử dụng helper ThumbImage để tạo thumbnail
            return ThumbImage($imagePath, $width, $height);
        } catch (\Exception $e) {
            createErrorLog($e, 'daily', 'ProductRepository::createProductThumbnail');
            return null;
        }
    }
}
