<?php

namespace App\Repositories;

use App\Contracts\TransferRequestInterface;
use App\Models\TransferRequest;
use App\Models\TransferRequestItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TransferRequestRepository extends BaseRepository implements TransferRequestInterface
{
    /**
     * TransferRequestRepository constructor.
     */
    public function __construct(TransferRequest $model)
    {
        parent::__construct($model);
    }

    /**
     * Get all transfer requests with pagination
     */
    public function getAllWithPagination($perPage = 15)
    {
        return $this->model->with(['fromWarehouse', 'toWarehouse', 'createdBy', 'approvedBy'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get transfer request by ID
     */
    public function getById($id)
    {
        return $this->model->with(['fromWarehouse', 'toWarehouse', 'createdBy', 'approvedBy', 'items.product'])
            ->findOrFail($id);
    }

    /**
     * Create new transfer request
     */
    public function create(array $data)
    {
        try {
            DB::beginTransaction();

            // Generate code if not provided
            if (!isset($data['code'])) {
                $data['code'] = TransferRequest::generateCode();
            }

            $transferRequest = $this->model->create($data);

            // Create items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                $this->createItems($transferRequest->id, $data['items']);
            }

            DB::commit();
            return $transferRequest;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating transfer request: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update transfer request
     */
    public function update($id, array $data)
    {
        try {
            DB::beginTransaction();

            $transferRequest = $this->getById($id);
            $transferRequest->update($data);

            // Update items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                $this->updateItems($transferRequest->id, $data['items']);
            }

            DB::commit();
            return $transferRequest;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating transfer request: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete transfer request
     */
    public function delete($id)
    {
        try {
            DB::beginTransaction();

            $transferRequest = $this->getById($id);

            // Delete items first
            $transferRequest->items()->delete();

            // Delete transfer request
            $transferRequest->delete();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting transfer request: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get transfer requests for datatable
     */
    public function getForDatatable(array $params)
    {
        try {
            $query = DB::table('transfer_requests as tr')
                ->leftJoin('warehouses as wf', 'tr.from_warehouse_id', '=', 'wf.id')
                ->leftJoin('warehouses as wt', 'tr.to_warehouse_id', '=', 'wt.id')
                ->leftJoin('users as uc', 'tr.created_by', '=', 'uc.id')
                ->leftJoin('users as ua', 'tr.approved_by', '=', 'ua.id')
                ->select(
                    'tr.id',
                    'tr.code',
                    'tr.status',
                    'tr.notes',
                    'tr.created_at',
                    'tr.approved_at',
                    'wf.name as from_warehouse_name',
                    'wt.name as to_warehouse_name',
                    'uc.name as created_by_name',
                    'ua.name as approved_by_name'
                )
                ->whereNull('tr.deleted_at');

            // Apply filters
            if (isset($params['status']) && !empty($params['status'])) {
                $query->where('tr.status', $params['status']);
            }

            if (isset($params['from_warehouse_id']) && !empty($params['from_warehouse_id'])) {
                $query->where('tr.from_warehouse_id', $params['from_warehouse_id']);
            }

            if (isset($params['to_warehouse_id']) && !empty($params['to_warehouse_id'])) {
                $query->where('tr.to_warehouse_id', $params['to_warehouse_id']);
            }

            if (isset($params['search']['value']) && !empty($params['search']['value'])) {
                $searchValue = $params['search']['value'];
                $query->where(function ($q) use ($searchValue) {
                    $q->where('tr.code', 'like', "%{$searchValue}%")
                      ->orWhere('wf.name', 'like', "%{$searchValue}%")
                      ->orWhere('wt.name', 'like', "%{$searchValue}%")
                      ->orWhere('uc.name', 'like', "%{$searchValue}%");
                });
            }

            // Get total count before pagination
            $totalRecords = $query->count();

            // Apply ordering
            if (isset($params['order'][0]['column']) && isset($params['columns'][$params['order'][0]['column']]['data'])) {
                $orderColumn = $params['columns'][$params['order'][0]['column']]['data'];
                $orderDirection = $params['order'][0]['dir'] ?? 'asc';

                $orderColumnMap = [
                    'code' => 'tr.code',
                    'from_warehouse' => 'wf.name',
                    'to_warehouse' => 'wt.name',
                    'status' => 'tr.status',
                    'created_at' => 'tr.created_at',
                    'created_by' => 'uc.name'
                ];

                if (isset($orderColumnMap[$orderColumn])) {
                    $query->orderBy($orderColumnMap[$orderColumn], $orderDirection);
                }
            } else {
                $query->orderBy('tr.created_at', 'desc');
            }

            // Apply pagination
            if (isset($params['start']) && isset($params['length'])) {
                $query->offset($params['start'])->limit($params['length']);
            }

            $data = $query->get();

            return [
                'data' => $data,
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $totalRecords
            ];
        } catch (\Exception $e) {
            Log::error('Error getting transfer requests for datatable: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Submit transfer request for approval
     */
    public function submitForApproval($id)
    {
        $transferRequest = $this->getById($id);

        if (!$transferRequest->canBeSubmitted()) {
            throw new \Exception('Yêu cầu chuyển kho không thể gửi duyệt.');
        }

        $transferRequest->update(['status' => TransferRequest::STATUS_PENDING]);
        return $transferRequest;
    }

    /**
     * Approve transfer request
     */
    public function approve($id, $approvedBy)
    {
        $transferRequest = $this->getById($id);

        if (!$transferRequest->canBeApproved()) {
            throw new \Exception('Yêu cầu chuyển kho không thể duyệt.');
        }

        $transferRequest->update([
            'status' => TransferRequest::STATUS_APPROVED,
            'approved_by' => $approvedBy,
            'approved_at' => now()
        ]);

        return $transferRequest;
    }

    /**
     * Reject transfer request
     */
    public function reject($id, $rejectedBy, $reason = null)
    {
        $transferRequest = $this->getById($id);

        if (!$transferRequest->canBeRejected()) {
            throw new \Exception('Yêu cầu chuyển kho không thể từ chối.');
        }

        $updateData = [
            'status' => TransferRequest::STATUS_REJECTED,
            'approved_by' => $rejectedBy,
            'approved_at' => now()
        ];

        if ($reason) {
            $updateData['notes'] = ($transferRequest->notes ? $transferRequest->notes . "\n\n" : '') . "Lý do từ chối: " . $reason;
        }

        $transferRequest->update($updateData);
        return $transferRequest;
    }

    /**
     * Cancel transfer request
     */
    public function cancel($id)
    {
        $transferRequest = $this->getById($id);

        if (!$transferRequest->canBeCancelled()) {
            throw new \Exception('Yêu cầu chuyển kho không thể hủy.');
        }

        $transferRequest->update(['status' => TransferRequest::STATUS_CANCELLED]);
        return $transferRequest;
    }

    /**
     * Get approved transfer requests that don't have transfer orders
     */
    public function getApprovedWithoutTransferOrder()
    {
        return $this->model->with(['fromWarehouse', 'toWarehouse'])
            ->where('status', TransferRequest::STATUS_APPROVED)
            ->whereDoesntHave('transferOrder')
            ->orderBy('approved_at', 'asc')
            ->get();
    }

    /**
     * Create transfer request items
     */
    public function createItems($transferRequestId, array $items)
    {
        try {
            foreach ($items as $item) {
                $item['transfer_request_id'] = $transferRequestId;
                TransferRequestItem::create($item);
            }
            return true;
        } catch (\Exception $e) {
            Log::error('Error creating transfer request items: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update transfer request items
     */
    public function updateItems($transferRequestId, array $items)
    {
        try {
            // Delete existing items
            TransferRequestItem::where('transfer_request_id', $transferRequestId)->delete();

            // Create new items
            $this->createItems($transferRequestId, $items);

            return true;
        } catch (\Exception $e) {
            Log::error('Error updating transfer request items: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete transfer request items
     */
    public function deleteItems($transferRequestId, array $itemIds = [])
    {
        try {
            $query = TransferRequestItem::where('transfer_request_id', $transferRequestId);

            if (!empty($itemIds)) {
                $query->whereIn('id', $itemIds);
            }

            $query->delete();
            return true;
        } catch (\Exception $e) {
            Log::error('Error deleting transfer request items: ' . $e->getMessage());
            throw $e;
        }
    }
}
