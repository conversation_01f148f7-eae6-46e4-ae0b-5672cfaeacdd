<?php

namespace App\Repositories;

use App\Contracts\WarehouseReceiptInterface;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use App\Models\WarehouseReceipt;
use App\Models\WarehouseTransfer;
use App\Models\WarehouseTransferItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;

class WarehouseReceiptRepository implements WarehouseReceiptInterface
{
    protected $model;

    public function __construct(WarehouseReceipt $model)
    {
        $this->model = $model;
    }

    /**
     * Get all warehouse receipts
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAll()
    {
        return $this->model->all();
    }

    /**
     * Get warehouse receipt by ID
     *
     * @param int $id
     * @return \App\Models\WarehouseReceipt
     */
    public function findById($id)
    {
        return $this->model->with([
            'warehouseTransfer.items.product',
            'warehouseTransfer.sourceWarehouse',
            'warehouseTransfer.transitWarehouse',
            'warehouse',
            'createdBy',
            'completedBy',
            'cancelledBy'
        ])->findOrFail($id);
    }

    /**
     * Create a new warehouse receipt
     *
     * @param array $data
     * @return array
     */
    public function createWarehouseReceipt(array $data)
    {
        try {
            DB::beginTransaction();

            // Tạo phiếu nhận hàng
            $warehouseReceipt = $this->model->create([
                'warehouse_transfer_id' => $data['warehouse_transfer_id'],
                'warehouse_id' => $data['warehouse_id'],
                'status' => WarehouseReceipt::STATUS_PENDING,
                'notes' => $data['notes'] ?? null,
                'receipt_date' => $data['receipt_date'] ?? now()->format('Y-m-d'),
                'created_by' => auth()->id()
            ]);

            DB::commit();

            return [
                'code' => 200,
                'text' => 'Phiếu nhận hàng đã được tạo thành công',
                'data' => $warehouseReceipt,
                'redirect' => route('warehouses.receipts.show', $warehouseReceipt->id)
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi tạo phiếu nhận hàng: ' . $e->getMessage());

            return [
                'code' => 500,
                'text' => 'Có lỗi xảy ra khi tạo phiếu nhận hàng: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Complete a warehouse receipt
     *
     * @param int $id
     * @param int $userId
     * @param array $data
     * @return array
     */
    public function completeWarehouseReceipt($id, $userId, array $data)
    {
        try {
            DB::beginTransaction();

            $warehouseReceipt = $this->findById($id);

            // Kiểm tra trạng thái
            if ($warehouseReceipt->status !== WarehouseReceipt::STATUS_PENDING) {
                return [
                    'code' => 400,
                    'text' => 'Chỉ có thể hoàn thành phiếu nhận hàng ở trạng thái chờ xác nhận'
                ];
            }

            $warehouseTransfer = $warehouseReceipt->warehouseTransfer;

            // Kiểm tra xem đã quét đủ IMEI cho tất cả sản phẩm chưa
            if ($warehouseTransfer->has_serial_items) {
                // Ghi log để kiểm tra
                Log::info('Kiểm tra IMEI trước khi hoàn thành phiếu nhận hàng', [
                    'receipt_id' => $warehouseReceipt->id,
                    'is_fully_scanned' => $warehouseReceipt->is_fully_scanned
                ]);

                if (!$warehouseReceipt->is_fully_scanned) {
                    return [
                        'code' => 400,
                        'text' => 'Vui lòng quét đủ IMEI cho tất cả sản phẩm trước khi hoàn thành phiếu nhận hàng'
                    ];
                }
            }

            // Lấy ID kho tạm từ config hoặc từ phiếu chuyển kho
            $transitWarehouseId = $warehouseTransfer->transit_warehouse_id ?? config('warehouse.transit_warehouse_id');

            // Kiểm tra xem kho tạm có tồn tại không
            $transitWarehouse = \App\Models\Warehouse::find($transitWarehouseId);
            if (!$transitWarehouse) {
                return [
                    'code' => 400,
                    'text' => 'Kho tạm không tồn tại. Vui lòng kiểm tra cấu hình.'
                ];
            }

            $destinationWarehouseId = $warehouseTransfer->destination_warehouse_id;

            // Kiểm tra dữ liệu đầu vào
            if (!isset($data['items']) || !is_array($data['items']) || empty($data['items'])) {
                return [
                    'code' => 400,
                    'text' => 'Dữ liệu sản phẩm không hợp lệ'
                ];
            }

            // Cập nhật số lượng đã nhận cho từng sản phẩm
            foreach ($data['items'] as $itemData) {
                if (!isset($itemData['id'])) {
                    continue; // Bỏ qua nếu không có ID
                }

                $transferItem = WarehouseTransferItem::find($itemData['id']);

                if (!$transferItem) {
                    Log::warning('Không tìm thấy sản phẩm trong phiếu chuyển kho', ['item_id' => $itemData['id']]);
                    continue; // Bỏ qua nếu không tìm thấy sản phẩm
                }

                // Kiểm tra số lượng nhận không vượt quá số lượng chuyển
                $receivedQuantity = isset($itemData['received_quantity']) ?
                    min(floatval($itemData['received_quantity']), floatval($transferItem->quantity)) :
                    floatval($transferItem->quantity);

                // Ghi log để kiểm tra
                Log::info('Cập nhật số lượng đã nhận', [
                    'item_id' => $transferItem->id,
                    'product_id' => $transferItem->product_id,
                    'product_name' => $transferItem->product->name,
                    'original_quantity' => $transferItem->quantity,
                    'received_quantity' => $receivedQuantity
                ]);

                // Cập nhật số lượng đã nhận
                $transferItem->received_quantity = $receivedQuantity;
                $transferItem->save();

                // Giảm tồn kho tạm
                $this->updateInventory(
                    $transitWarehouseId,
                    null, // Không có khu vực cụ thể trong kho tạm
                    $transferItem->product_id,
                    -$receivedQuantity,
                    'Giảm tồn kho tạm do phiếu nhận hàng #' . $warehouseReceipt->id,
                    $warehouseReceipt
                );

                // Tăng tồn kho đích
                $this->updateInventory(
                    $destinationWarehouseId,
                    $transferItem->destination_warehouse_area_id,
                    $transferItem->product_id,
                    $receivedQuantity,
                    'Tăng tồn kho đích do phiếu nhận hàng #' . $warehouseReceipt->id,
                    $warehouseReceipt
                );
            }

            // Cập nhật trạng thái phiếu nhận hàng
            $warehouseReceipt->status = WarehouseReceipt::STATUS_COMPLETED;
            $warehouseReceipt->completed_by = $userId;
            $warehouseReceipt->completed_at = now();
            $warehouseReceipt->notes = isset($data['notes']) ? $data['notes'] : $warehouseReceipt->notes;
            $warehouseReceipt->save();

            // Cập nhật trạng thái phiếu chuyển kho
            $warehouseTransfer->status = WarehouseTransfer::STATUS_COMPLETED;
            $warehouseTransfer->save();

            DB::commit();

            return [
                'code' => 200,
                'text' => 'Phiếu nhận hàng đã được hoàn thành thành công',
                'data' => $warehouseReceipt,
                'redirect' => route('warehouses.receipts.show', $warehouseReceipt->id)
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi hoàn thành phiếu nhận hàng: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => 500,
                'text' => 'Có lỗi xảy ra khi hoàn thành phiếu nhận hàng: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Cập nhật tồn kho
     *
     * @param int $warehouseId ID kho hàng
     * @param int|null $warehouseAreaId ID khu vực kho (có thể null)
     * @param int $productId ID sản phẩm
     * @param float $quantity Số lượng (dương nếu tăng, âm nếu giảm)
     * @param string $notes Ghi chú
     * @param WarehouseReceipt $warehouseReceipt Phiếu nhận hàng liên quan
     * @return void
     */
    private function updateInventory($warehouseId, $warehouseAreaId, $productId, $quantity, $notes, $warehouseReceipt)
    {
        // Log thông tin đầu vào
        Log::info('Cập nhật tồn kho:', [
            'warehouse_id' => $warehouseId,
            'warehouse_area_id' => $warehouseAreaId,
            'product_id' => $productId,
            'quantity' => $quantity
        ]);

        try {
            // Sử dụng DB::transaction để đảm bảo tính toàn vẹn và tránh race condition
            DB::transaction(function () use ($warehouseId, $warehouseAreaId, $productId, $quantity, $notes, $warehouseReceipt) {
                // Truy vấn trực tiếp vào cơ sở dữ liệu để kiểm tra xem đã có bản ghi nào với warehouse_id và product_id này chưa
                $existingItem = DB::table('inventory_items')
                    ->where('warehouse_id', $warehouseId)
                    ->where('product_id', $productId)
                    ->lockForUpdate() // Khóa hàng để tránh race condition
                    ->first();

                if ($existingItem) {
                    // Nếu đã có bản ghi, cập nhật số lượng
                    Log::info('Tìm thấy bản ghi tồn kho hiện có:', ['id' => $existingItem->id]);

                    // Cập nhật trực tiếp vào cơ sở dữ liệu
                    DB::table('inventory_items')
                        ->where('id', $existingItem->id)
                        ->update([
                            'quantity' => DB::raw("quantity + {$quantity}"),
                            'available_quantity' => DB::raw("available_quantity + {$quantity}"),
                            'warehouse_area_id' => $warehouseAreaId, // Cập nhật warehouse_area_id nếu có
                            'updated_at' => now()
                        ]);

                    // Lấy bản ghi đã cập nhật
                    $updatedItem = DB::table('inventory_items')
                        ->where('id', $existingItem->id)
                        ->first();

                    // Tạo giao dịch tồn kho
                    InventoryTransaction::create([
                        'warehouse_id' => $warehouseId,
                        'warehouse_area_id' => $warehouseAreaId,
                        'product_id' => $productId,
                        'quantity' => $quantity,
                        'balance' => $updatedItem->quantity,
                        'transaction_type' => $quantity > 0 ? 'in' : 'out',
                        'reference_type' => WarehouseReceipt::class,
                        'reference_id' => $warehouseReceipt->id,
                        'notes' => $notes,
                        'transaction_date' => Carbon::now(),
                    ]);

                    Log::info('Cập nhật tồn kho thành công:', [
                        'inventory_item_id' => $existingItem->id,
                        'new_quantity' => $updatedItem->quantity
                    ]);
                } else {
                    // Nếu chưa có bản ghi, tạo mới
                    Log::info('Tạo mới bản ghi tồn kho');

                    // Tạo mới bản ghi tồn kho
                    $newItemId = DB::table('inventory_items')->insertGetId([
                        'warehouse_id' => $warehouseId,
                        'warehouse_area_id' => $warehouseAreaId,
                        'product_id' => $productId,
                        'quantity' => $quantity,
                        'available_quantity' => $quantity,
                        'reserved_quantity' => 0,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);

                    // Tạo giao dịch tồn kho
                    InventoryTransaction::create([
                        'warehouse_id' => $warehouseId,
                        'warehouse_area_id' => $warehouseAreaId,
                        'product_id' => $productId,
                        'quantity' => $quantity,
                        'balance' => $quantity,
                        'transaction_type' => $quantity > 0 ? 'in' : 'out',
                        'reference_type' => WarehouseReceipt::class,
                        'reference_id' => $warehouseReceipt->id,
                        'notes' => $notes,
                        'transaction_date' => Carbon::now(),
                    ]);

                    Log::info('Tạo mới tồn kho thành công:', [
                        'inventory_item_id' => $newItemId,
                        'quantity' => $quantity
                    ]);
                }
            }, 5); // Thử lại tối đa 5 lần nếu xảy ra lỗi
        } catch (\Exception $e) {
            Log::error('Lỗi khi cập nhật tồn kho: ' . $e->getMessage(), [
                'warehouse_id' => $warehouseId,
                'warehouse_area_id' => $warehouseAreaId,
                'product_id' => $productId,
                'quantity' => $quantity,
                'exception' => $e
            ]);

            // Nếu lỗi là do vi phạm ràng buộc unique, thử cập nhật lại
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                Log::info('Thử cập nhật lại tồn kho sau lỗi Duplicate entry');

                try {
                    // Cập nhật trực tiếp vào cơ sở dữ liệu
                    DB::table('inventory_items')
                        ->where('warehouse_id', $warehouseId)
                        ->where('product_id', $productId)
                        ->update([
                            'quantity' => DB::raw("quantity + {$quantity}"),
                            'available_quantity' => DB::raw("available_quantity + {$quantity}"),
                            'warehouse_area_id' => $warehouseAreaId,
                            'updated_at' => now()
                        ]);

                    // Lấy bản ghi đã cập nhật
                    $updatedItem = DB::table('inventory_items')
                        ->where('warehouse_id', $warehouseId)
                        ->where('product_id', $productId)
                        ->first();

                    // Tạo giao dịch tồn kho
                    InventoryTransaction::create([
                        'warehouse_id' => $warehouseId,
                        'warehouse_area_id' => $warehouseAreaId,
                        'product_id' => $productId,
                        'quantity' => $quantity,
                        'balance' => $updatedItem->quantity,
                        'transaction_type' => $quantity > 0 ? 'in' : 'out',
                        'reference_type' => WarehouseReceipt::class,
                        'reference_id' => $warehouseReceipt->id,
                        'notes' => $notes . ' (cập nhật lại sau lỗi)',
                        'transaction_date' => Carbon::now(),
                    ]);

                    Log::info('Cập nhật lại tồn kho thành công sau lỗi Duplicate entry');
                } catch (\Exception $retryException) {
                    Log::error('Lỗi khi thử cập nhật lại tồn kho: ' . $retryException->getMessage());
                    throw $retryException;
                }
            } else {
                // Nếu là lỗi khác, ném lại lỗi để xử lý ở mức cao hơn
                throw $e;
            }
        }
    }

    /**
     * Cancel a warehouse receipt
     *
     * @param int $id
     * @param int $userId
     * @param string|null $reason
     * @return array
     */
    public function cancelWarehouseReceipt($id, $userId, $reason = null)
    {
        try {
            DB::beginTransaction();

            $warehouseReceipt = $this->findById($id);

            // Kiểm tra trạng thái
            if ($warehouseReceipt->status !== WarehouseReceipt::STATUS_PENDING) {
                return [
                    'code' => 400,
                    'text' => 'Chỉ có thể hủy phiếu nhận hàng ở trạng thái chờ xác nhận'
                ];
            }

            // Cập nhật trạng thái
            $warehouseReceipt->status = WarehouseReceipt::STATUS_CANCELLED;
            $warehouseReceipt->cancelled_by = $userId;
            $warehouseReceipt->cancelled_at = now();
            $warehouseReceipt->notes = $warehouseReceipt->notes . "\n\nLý do hủy: " . ($reason ?? 'Không có lý do');
            $warehouseReceipt->save();

            // Cập nhật trạng thái phiếu chuyển kho
            $warehouseTransfer = $warehouseReceipt->warehouseTransfer;
            $warehouseTransfer->status = WarehouseTransfer::STATUS_SHIPPING;
            $warehouseTransfer->save();

            DB::commit();

            return [
                'code' => 200,
                'text' => 'Phiếu nhận hàng đã được hủy thành công',
                'data' => $warehouseReceipt,
                'redirect' => route('warehouses.receipts.show', $warehouseReceipt->id)
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi hủy phiếu nhận hàng: ' . $e->getMessage());

            return [
                'code' => 500,
                'text' => 'Có lỗi xảy ra khi hủy phiếu nhận hàng: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Complete a warehouse receipt without IMEI scanning
     *
     * @param int $id
     * @param int $userId
     * @param string $reason
     * @return array
     */
    public function completeWarehouseReceiptWithoutImei($id, $userId, $reason)
    {
        try {
            DB::beginTransaction();

            $warehouseReceipt = $this->findById($id);

            // Kiểm tra trạng thái
            if ($warehouseReceipt->status !== WarehouseReceipt::STATUS_PENDING) {
                return [
                    'code' => 400,
                    'text' => 'Chỉ có thể hoàn thành phiếu nhận hàng ở trạng thái chờ xác nhận'
                ];
            }

            $warehouseTransfer = $warehouseReceipt->warehouseTransfer;

            // Lấy ID kho tạm từ config hoặc từ phiếu chuyển kho
            $transitWarehouseId = $warehouseTransfer->transit_warehouse_id ?? config('warehouse.transit_warehouse_id');

            // Kiểm tra xem kho tạm có tồn tại không
            $transitWarehouse = \App\Models\Warehouse::find($transitWarehouseId);
            if (!$transitWarehouse) {
                return [
                    'code' => 400,
                    'text' => 'Kho tạm không tồn tại. Vui lòng kiểm tra cấu hình.'
                ];
            }

            $destinationWarehouseId = $warehouseTransfer->destination_warehouse_id;

            // Cập nhật số lượng đã nhận cho từng sản phẩm
            foreach ($warehouseTransfer->items as $transferItem) {
                // Cập nhật số lượng đã nhận bằng số lượng chuyển
                $receivedQuantity = floatval($transferItem->quantity);

                // Ghi log để kiểm tra
                Log::info('Cập nhật số lượng đã nhận (không quét IMEI)', [
                    'item_id' => $transferItem->id,
                    'product_id' => $transferItem->product_id,
                    'product_name' => $transferItem->product->name,
                    'quantity' => $receivedQuantity
                ]);

                $transferItem->received_quantity = $receivedQuantity;
                $transferItem->save();

                // Giảm tồn kho tạm
                $this->updateInventory(
                    $transitWarehouseId,
                    null, // Không có khu vực cụ thể trong kho tạm
                    $transferItem->product_id,
                    -$receivedQuantity,
                    'Giảm tồn kho tạm do phiếu nhận hàng #' . $warehouseReceipt->id . ' (không quét IMEI)',
                    $warehouseReceipt
                );

                // Tăng tồn kho đích
                $this->updateInventory(
                    $destinationWarehouseId,
                    $transferItem->destination_warehouse_area_id,
                    $transferItem->product_id,
                    $receivedQuantity,
                    'Tăng tồn kho đích do phiếu nhận hàng #' . $warehouseReceipt->id . ' (không quét IMEI)',
                    $warehouseReceipt
                );
            }

            // Cập nhật trạng thái phiếu nhận hàng
            $warehouseReceipt->status = WarehouseReceipt::STATUS_COMPLETED;
            $warehouseReceipt->completed_by = $userId;
            $warehouseReceipt->completed_at = now();
            $warehouseReceipt->notes = ($warehouseReceipt->notes ? $warehouseReceipt->notes . "\n\n" : '') . "Hoàn thành không quét IMEI. Lý do: " . $reason;
            $warehouseReceipt->save();

            // Cập nhật trạng thái phiếu chuyển kho
            $warehouseTransfer->status = WarehouseTransfer::STATUS_COMPLETED;
            $warehouseTransfer->save();

            DB::commit();

            return [
                'code' => 200,
                'text' => 'Phiếu nhận hàng đã được hoàn thành thành công',
                'data' => $warehouseReceipt,
                'redirect' => route('warehouses.receipts.show', $warehouseReceipt->id)
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi hoàn thành phiếu nhận hàng không quét IMEI: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => 500,
                'text' => 'Có lỗi xảy ra khi hoàn thành phiếu nhận hàng: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Kiểm tra IMEI giữa phiếu chuyển kho và phiếu nhận hàng
     *
     * @param int $id
     * @param int $itemId
     * @return array
     */
    public function checkImei($id, $itemId)
    {
        try {
            $warehouseReceipt = $this->findById($id);
            $warehouseTransfer = $warehouseReceipt->warehouseTransfer;

            // Kiểm tra xem item có thuộc về phiếu chuyển kho không
            $transferItem = $warehouseTransfer->items()->where('id', $itemId)->first();

            if (!$transferItem) {
                return [
                    'code' => 404,
                    'text' => 'Không tìm thấy sản phẩm trong phiếu chuyển kho'
                ];
            }

            // Kiểm tra xem sản phẩm có quản lý bằng IMEI không
            if ($transferItem->product->inventory_tracking_type !== 'serial') {
                return [
                    'code' => 400,
                    'text' => 'Sản phẩm này không quản lý bằng IMEI'
                ];
            }

            // Lấy danh sách IMEI đã quét trong phiếu chuyển kho
            $transferSerials = $transferItem->serials()->pluck('serial_number')->toArray();

            // Lấy danh sách IMEI đã quét trong phiếu nhận hàng
            $receiptSerials = $transferItem->receiptSerials()->pluck('serial_number')->toArray();

            // Kiểm tra xem các IMEI có khớp nhau không
            $matchStatus = 'unknown';

            if (count($transferSerials) > 0 && count($receiptSerials) > 0) {
                $missingSerials = array_diff($transferSerials, $receiptSerials);
                $extraSerials = array_diff($receiptSerials, $transferSerials);

                if (count($missingSerials) === 0 && count($extraSerials) === 0) {
                    $matchStatus = 'match'; // Tất cả IMEI khớp
                } elseif (count($missingSerials) > 0 && count($extraSerials) > 0) {
                    $matchStatus = 'mismatch'; // Có IMEI không khớp
                } else {
                    $matchStatus = 'partial'; // Một số IMEI khớp, một số không
                }
            }

            return [
                'code' => 200,
                'text' => 'Kiểm tra IMEI thành công',
                'transfer_serials' => $transferSerials,
                'receipt_serials' => $receiptSerials,
                'match_status' => $matchStatus
            ];
        } catch (\Exception $e) {
            Log::error('Lỗi khi kiểm tra IMEI: ' . $e->getMessage());

            return [
                'code' => 500,
                'text' => 'Có lỗi xảy ra khi kiểm tra IMEI: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Quét IMEI cho phiếu nhận hàng
     *
     * @param int $id
     * @param int $itemId
     * @param string $serialNumber
     * @param int $userId
     * @return array
     */
    public function scanImei($id, $itemId, $serialNumber, $userId)
    {
        try {
            $warehouseReceipt = $this->findById($id);
            $warehouseTransfer = $warehouseReceipt->warehouseTransfer;

            // Kiểm tra trạng thái phiếu nhận hàng
            if ($warehouseReceipt->status !== WarehouseReceipt::STATUS_PENDING) {
                return [
                    'code' => 400,
                    'text' => 'Chỉ có thể quét IMEI cho phiếu nhận hàng ở trạng thái chờ xác nhận'
                ];
            }

            // Kiểm tra xem item có thuộc về phiếu chuyển kho không
            $transferItem = $warehouseTransfer->items()->where('id', $itemId)->first();

            if (!$transferItem) {
                return [
                    'code' => 404,
                    'text' => 'Không tìm thấy sản phẩm trong phiếu chuyển kho'
                ];
            }

            // Kiểm tra xem sản phẩm có quản lý bằng IMEI không
            if ($transferItem->product->inventory_tracking_type !== 'serial') {
                return [
                    'code' => 400,
                    'text' => 'Sản phẩm này không quản lý bằng IMEI'
                ];
            }

            // Kiểm tra xem IMEI có tồn tại trong phiếu chuyển kho không
            $transferSerial = $transferItem->serials()->where('serial_number', $serialNumber)->first();

            if (!$transferSerial) {
                return [
                    'code' => 400,
                    'text' => 'IMEI không tồn tại trong phiếu chuyển kho'
                ];
            }

            // Kiểm tra xem IMEI đã được quét trong phiếu nhận hàng chưa
            $existingSerial = $transferItem->receiptSerials()->where('serial_number', $serialNumber)->first();

            if ($existingSerial) {
                return [
                    'code' => 400,
                    'text' => 'IMEI đã được quét trước đó'
                ];
            }

            // Kiểm tra xem đã quét đủ số lượng IMEI chưa
            $scannedCount = $transferItem->receiptSerials()->count();

            if ($scannedCount >= $transferItem->quantity) {
                return [
                    'code' => 400,
                    'text' => 'Đã quét đủ số lượng IMEI cho sản phẩm này'
                ];
            }

            // Tạo mới bản ghi IMEI
            $receiptSerial = new \App\Models\WarehouseReceiptSerial([
                'warehouse_transfer_item_id' => $transferItem->id,
                'serial_number' => $serialNumber,
                'scanned_by' => $userId,
                'scanned_at' => now()
            ]);

            $receiptSerial->save();

            // Kiểm tra xem đã quét đủ số lượng IMEI chưa (sau khi quét)
            $scannedCount = $transferItem->receiptSerials()->count();
            $isComplete = $scannedCount >= $transferItem->quantity;

            return [
                'code' => 200,
                'text' => 'Quét IMEI thành công',
                'serial' => $serialNumber,
                'scanned_count' => $scannedCount,
                'total_count' => $transferItem->quantity,
                'is_complete' => $isComplete
            ];
        } catch (\Exception $e) {
            Log::error('Lỗi khi quét IMEI: ' . $e->getMessage());

            return [
                'code' => 500,
                'text' => 'Có lỗi xảy ra khi quét IMEI: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get warehouse receipts for datatable
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWarehouseReceiptsForDatatable(Request $request)
    {
        // Sử dụng query builder trực tiếp để tối ưu hiệu suất
        $query = DB::table('warehouse_receipts')
            ->select(
                'warehouse_receipts.id',
                'warehouse_receipts.code',
                'warehouse_receipts.warehouse_transfer_id',
                'warehouse_receipts.warehouse_id',
                'warehouse_receipts.status',
                'warehouse_receipts.created_by',
                'warehouse_receipts.created_at',
                'warehouse_receipts.completed_at',
                'warehouses.name as warehouse_name',
                'warehouse_transfers.code as transfer_code',
                'users.name as created_by_name'
            )
            ->leftJoin('warehouses', 'warehouse_receipts.warehouse_id', '=', 'warehouses.id')
            ->leftJoin('warehouse_transfers', 'warehouse_receipts.warehouse_transfer_id', '=', 'warehouse_transfers.id')
            ->leftJoin('users', 'warehouse_receipts.created_by', '=', 'users.id')
            ->whereNull('warehouse_receipts.deleted_at');

        // Filter by warehouse
        if ($request->has('warehouse_id') && !empty($request->warehouse_id)) {
            $query->where('warehouse_receipts.warehouse_id', $request->warehouse_id);
        }

        // Filter by status
        if ($request->has('status') && !empty($request->status)) {
            $query->where('warehouse_receipts.status', $request->status);
        }

        // Filter by date range
        if ($request->has('date_from') && !empty($request->date_from)) {
            $query->whereDate('warehouse_receipts.created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $query->whereDate('warehouse_receipts.created_at', '<=', $request->date_to);
        }

        // Search
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $query->where(function ($q) use ($searchValue) {
                $q->where('warehouse_receipts.code', 'like', "{$searchValue}%")
                    ->orWhere('warehouse_transfers.code', 'like', "{$searchValue}%")
                    ->orWhere('warehouses.name', 'like', "{$searchValue}%");
            });
        }

        // Datatable order
        if ($request->has('order') && !empty($request->order)) {
            $columns = [
                1 => 'warehouse_receipts.code',
                2 => 'warehouse_transfers.code',
                3 => 'warehouses.name',
                4 => 'warehouse_receipts.status',
                5 => 'warehouse_receipts.created_at',
                6 => 'warehouse_receipts.completed_at'
            ];

            $orderColumn = $request->order[0]['column'];
            $orderDir = $request->order[0]['dir'];

            if (isset($columns[$orderColumn])) {
                $query->orderBy($columns[$orderColumn], $orderDir);
            }
        } else {
            $query->orderBy('warehouse_receipts.created_at', 'desc');
        }

        return DataTables::of($query)
            ->addColumn('status_text', function ($row) {
                $statusMap = [
                    'pending' => 'Chờ xác nhận',
                    'completed' => 'Đã nhận hàng',
                    'cancelled' => 'Đã hủy'
                ];

                return $statusMap[$row->status] ?? 'Không xác định';
            })
            ->addColumn('status_color', function ($row) {
                $colorMap = [
                    'pending' => 'warning',
                    'completed' => 'success',
                    'cancelled' => 'danger'
                ];

                return $colorMap[$row->status] ?? 'secondary';
            })
            ->addColumn('actions', function ($row) {
                $actions = '<div class="d-flex align-items-center">';

                if (auth()->user()->can('warehouses.receipts.view')) {
                    $actions .= '<a href="' . route('warehouses.receipts.show', $row->id) . '" class="btn btn-sm btn-icon me-2"><i class="ri-eye-line"></i></a>';
                }

                $actions .= '</div>';

                return $actions;
            })
            ->rawColumns(['actions'])
            ->make(true);
    }
}
