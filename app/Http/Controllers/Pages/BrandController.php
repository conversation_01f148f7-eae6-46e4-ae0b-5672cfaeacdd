<?php

namespace App\Http\Controllers\Pages;

use App\Http\Controllers\Controller;
use App\Http\Requests\BrandRequest;
use App\Models\Brand;
use App\Traits\PaginationTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BrandController extends Controller
{
    use PaginationTrait;
    /**
     * Display a listing of brands
     */
    public function index()
    {
        return view('content.pages.products.brands.index');
    }

    /**
     * Show the form for creating a new brand
     */
    public function create()
    {
        return view('content.pages.products.brands.create');
    }

    /**
     * Store a newly created brand
     */
    public function store(BrandRequest $request)
    {
        try {
            $data = $request->validated();

            // Handle logo upload
            if ($request->hasFile('logo')) {
                $file = $request->file('logo');
                $filename = Str::slug($data['name']) . '-' . time() . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs('brands', $filename, 'public');
                $data['logo'] = $path;
            }

            $brand = Brand::create($data);

            return response()->json([
                'title' => 'success',
                'text' => 'Thương hiệu đã được tạo thành công',
                'code' => 200
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi tạo thương hiệu: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Show the form for editing the specified brand
     */
    public function edit($id)
    {
        $brand = Brand::findOrFail($id);

        return view('content.pages.products.brands.edit', compact('brand'));
    }

    /**
     * Update the specified brand
     */
    public function update(BrandRequest $request, $id)
    {
        try {
            $brand = Brand::findOrFail($id);
            $data = $request->validated();

            // Handle logo upload
            if ($request->hasFile('logo')) {
                // Delete old logo if exists
                if ($brand->logo && Storage::disk('public')->exists($brand->logo)) {
                    Storage::disk('public')->delete($brand->logo);
                }

                $file = $request->file('logo');
                $filename = Str::slug($data['name']) . '-' . time() . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs('brands', $filename, 'public');
                $data['logo'] = $path;
            }

            $brand->update($data);

            return response()->json([
                'title' => 'success',
                'text' => 'Thương hiệu đã được cập nhật thành công',
                'code' => 200
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi cập nhật thương hiệu: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Remove the specified brand
     */
    public function delete($id)
    {
        try {
            $brand = Brand::findOrFail($id);

            // Check if brand has products
            if ($brand->products()->count() > 0) {
                return response()->json([
                    'title' => 'errors',
                    'text' => 'Không thể xóa thương hiệu có sản phẩm',
                    'code' => 400
                ]);
            }

            // Delete logo if exists
            if ($brand->logo && Storage::disk('public')->exists($brand->logo)) {
                Storage::disk('public')->delete($brand->logo);
            }

            $brand->delete();

            return response()->json([
                'title' => 'success',
                'text' => 'Thương hiệu đã được xóa thành công',
                'code' => 200
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi xóa thương hiệu: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Get brands for datatable
     */
    public function getDatatable(Request $request)
    {
        $query = Brand::query();

        // Filter by status
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('is_active', $request->status === 'active');
        }

        // Search
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $query->where(function ($q) use ($searchValue) {
                $q->where('name', 'like', "%{$searchValue}%")
                    ->orWhere('description', 'like', "%{$searchValue}%");
            });
        }

        // Define columns for sorting
        $columns = [];
        if ($request->has('columns')) {
            foreach ($request->columns as $column) {
                $columns[] = $column['data'];
            }
        } else {
            $columns = ['id', 'name', 'description', 'logo', 'is_active', 'actions'];
        }

        // Apply sorting
        $this->applySorting($query, $request, $columns, 'name', 'asc');

        // Get total count before pagination
        $recordsTotal = $query->count();

        // Apply pagination
        $this->applyPagination($query, $request);

        // Get results
        $brands = $query->get();

        $data = [];
        foreach ($brands as $brand) {
            $data[] = [
                'id' => $brand->id,
                'name' => $brand->name,
                'description' => $brand->description,
                'logo' => $brand->logo ? asset('storage/' . $brand->logo) : null,
                'is_active' => $brand->is_active,
                'actions' => '' // Will be rendered on the client side
            ];
        }

        // Return response with pagination data
        return $this->getPaginationResponse($request, $recordsTotal, $data);
    }

    /**
     * Upload brand logo
     */
    public function uploadLogo(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|image|max:2048',
            ]);

            $file = $request->file('file');
            $filename = 'brand_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('brands', $filename, 'public');

            return response()->json([
                'success' => true,
                'path' => $path
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
