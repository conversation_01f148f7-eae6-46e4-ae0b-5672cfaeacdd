<?php

namespace App\Http\Controllers\Pages;

use App\Contracts\WarehouseInterface;
use App\Http\Controllers\Controller;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use App\Models\Product;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class WarehouseInventoryController extends Controller
{
    protected $warehouses;

    public function __construct(WarehouseInterface $warehouses)
    {
        $this->warehouses = $warehouses;
    }

    /**
     * Display inventory items
     */
    public function index(Request $request)
    {
        $warehouses = $this->warehouses->getAllWarehouses();
        $selectedWarehouse = $request->warehouse_id ? $this->warehouses->getWarehouseById($request->warehouse_id) : null;

        return view('content.pages.warehouses.inventory.index', compact('warehouses', 'selectedWarehouse'));
    }

    /**
     * Show form to add inventory
     */
    public function add()
    {
        $warehouses = $this->warehouses->getAllWarehouses();
        $products = Product::active()->get();

        return view('content.pages.warehouses.inventory.add', compact('warehouses', 'products'));
    }

    /**
     * Process adding inventory
     */
    public function store(Request $request)
    {
        $request->validate([
            'warehouse_id' => 'required|exists:warehouses,id',
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|numeric|min:1',
            'batch_number' => 'nullable|string|max:50',
            'expiry_date' => 'nullable|date',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            // Generate reference number
            $referenceNumber = 'IN-' . date('YmdHis') . '-' . Str::random(5);

            // Create or update inventory item
            $inventoryItem = InventoryItem::firstOrNew([
                'warehouse_id' => $request->warehouse_id,
                'product_id' => $request->product_id,
            ]);

            $oldQuantity = $inventoryItem->quantity ?? 0;
            $inventoryItem->quantity = ($oldQuantity + $request->quantity);
            $inventoryItem->available_quantity = ($inventoryItem->available_quantity ?? 0) + $request->quantity;
            $inventoryItem->last_updated_by = Auth::id();
            $inventoryItem->save();

            // Create inventory transaction
            InventoryTransaction::create([
                'reference_number' => $referenceNumber,
                'warehouse_id' => $request->warehouse_id,
                'product_id' => $request->product_id,
                'quantity' => $request->quantity,
                'type' => 'in',
                'batch_number' => $request->batch_number,
                'expiry_date' => $request->expiry_date,
                'notes' => $request->notes,
                'created_by' => Auth::id(),
            ]);

            DB::commit();

            return response()->json([
                'title' => 'success',
                'text' => 'Nhập kho thành công',
                'code' => 200
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi nhập kho: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Show form to remove inventory
     */
    public function remove()
    {
        $warehouses = $this->warehouses->getAllWarehouses();

        return view('content.pages.warehouses.inventory.remove', compact('warehouses'));
    }

    /**
     * Process removing inventory
     */
    public function processRemove(Request $request)
    {
        $request->validate([
            'warehouse_id' => 'required|exists:warehouses,id',
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|numeric|min:1',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            // Check if inventory item exists and has enough quantity
            $inventoryItem = InventoryItem::where([
                'warehouse_id' => $request->warehouse_id,
                'product_id' => $request->product_id,
            ])->first();

            if (!$inventoryItem) {
                return response()->json([
                    'title' => 'errors',
                    'text' => 'Sản phẩm không tồn tại trong kho',
                    'code' => 400
                ]);
            }

            if ($inventoryItem->available_quantity < $request->quantity) {
                return response()->json([
                    'title' => 'errors',
                    'text' => 'Số lượng xuất kho vượt quá số lượng khả dụng',
                    'code' => 400
                ]);
            }

            // Generate reference number
            $referenceNumber = 'OUT-' . date('YmdHis') . '-' . Str::random(5);

            // Update inventory item
            $inventoryItem->quantity -= $request->quantity;
            $inventoryItem->available_quantity -= $request->quantity;
            $inventoryItem->last_updated_by = Auth::id();
            $inventoryItem->save();

            // Create inventory transaction
            InventoryTransaction::create([
                'reference_number' => $referenceNumber,
                'warehouse_id' => $request->warehouse_id,
                'product_id' => $request->product_id,
                'quantity' => $request->quantity,
                'type' => 'out',
                'notes' => $request->notes,
                'created_by' => Auth::id(),
            ]);

            DB::commit();

            return response()->json([
                'title' => 'success',
                'text' => 'Xuất kho thành công',
                'code' => 200
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi xuất kho: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Show form to transfer inventory
     */
    public function transfer()
    {
        $warehouses = $this->warehouses->getAllWarehouses();

        return view('content.pages.warehouses.inventory.transfer', compact('warehouses'));
    }

    /**
     * Process transferring inventory
     */
    public function processTransfer(Request $request)
    {
        $request->validate([
            'source_warehouse_id' => 'required|exists:warehouses,id',
            'destination_warehouse_id' => 'required|exists:warehouses,id|different:source_warehouse_id',
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|numeric|min:1',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            // Check if inventory item exists in source warehouse and has enough quantity
            $sourceInventoryItem = InventoryItem::where([
                'warehouse_id' => $request->source_warehouse_id,
                'product_id' => $request->product_id,
            ])->first();

            if (!$sourceInventoryItem) {
                return response()->json([
                    'title' => 'errors',
                    'text' => 'Sản phẩm không tồn tại trong kho nguồn',
                    'code' => 400
                ]);
            }

            if ($sourceInventoryItem->available_quantity < $request->quantity) {
                return response()->json([
                    'title' => 'errors',
                    'text' => 'Số lượng chuyển kho vượt quá số lượng khả dụng',
                    'code' => 400
                ]);
            }

            // Generate reference numbers
            $outReferenceNumber = 'TROUT-' . date('YmdHis') . '-' . Str::random(5);
            $inReferenceNumber = 'TRIN-' . date('YmdHis') . '-' . Str::random(5);

            // Update source inventory item
            $sourceInventoryItem->quantity -= $request->quantity;
            $sourceInventoryItem->available_quantity -= $request->quantity;
            $sourceInventoryItem->last_updated_by = Auth::id();
            $sourceInventoryItem->save();

            // Create or update destination inventory item
            $destinationInventoryItem = InventoryItem::firstOrNew([
                'warehouse_id' => $request->destination_warehouse_id,
                'product_id' => $request->product_id,
            ]);

            $oldQuantity = $destinationInventoryItem->quantity ?? 0;
            $destinationInventoryItem->quantity = ($oldQuantity + $request->quantity);
            $destinationInventoryItem->available_quantity = ($destinationInventoryItem->available_quantity ?? 0) + $request->quantity;
            $destinationInventoryItem->last_updated_by = Auth::id();
            $destinationInventoryItem->save();

            // Create inventory transactions
            // Out transaction from source warehouse
            InventoryTransaction::create([
                'reference_number' => $outReferenceNumber,
                'warehouse_id' => $request->source_warehouse_id,
                'product_id' => $request->product_id,
                'quantity' => $request->quantity,
                'type' => 'transfer_out',
                'related_warehouse_id' => $request->destination_warehouse_id,
                'notes' => $request->notes,
                'created_by' => Auth::id(),
            ]);

            // In transaction to destination warehouse
            InventoryTransaction::create([
                'reference_number' => $inReferenceNumber,
                'warehouse_id' => $request->destination_warehouse_id,
                'product_id' => $request->product_id,
                'quantity' => $request->quantity,
                'type' => 'transfer_in',
                'related_warehouse_id' => $request->source_warehouse_id,
                'notes' => $request->notes,
                'created_by' => Auth::id(),
            ]);

            DB::commit();

            return response()->json([
                'title' => 'success',
                'text' => 'Chuyển kho thành công',
                'code' => 200
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi chuyển kho: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Show form to adjust inventory
     */
    public function adjust()
    {
        $warehouses = $this->warehouses->getAllWarehouses();

        return view('content.pages.warehouses.inventory.adjust', compact('warehouses'));
    }

    /**
     * Process adjusting inventory
     */
    public function processAdjust(Request $request)
    {
        $request->validate([
            'warehouse_id' => 'required|exists:warehouses,id',
            'product_id' => 'required|exists:products,id',
            'new_quantity' => 'required|numeric|min:0',
            'reason' => 'required|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            // Find inventory item
            $inventoryItem = InventoryItem::firstOrNew([
                'warehouse_id' => $request->warehouse_id,
                'product_id' => $request->product_id,
            ]);

            $oldQuantity = $inventoryItem->quantity ?? 0;
            $quantityDifference = $request->new_quantity - $oldQuantity;

            // Generate reference number
            $referenceNumber = 'ADJ-' . date('YmdHis') . '-' . Str::random(5);

            // Update inventory item
            $inventoryItem->quantity = $request->new_quantity;
            $inventoryItem->available_quantity = $request->new_quantity - ($inventoryItem->reserved_quantity ?? 0);
            $inventoryItem->last_updated_by = Auth::id();
            $inventoryItem->save();

            // Create inventory transaction
            InventoryTransaction::create([
                'reference_number' => $referenceNumber,
                'warehouse_id' => $request->warehouse_id,
                'product_id' => $request->product_id,
                'quantity' => abs($quantityDifference),
                'type' => $quantityDifference >= 0 ? 'adjust_in' : 'adjust_out',
                'notes' => $request->reason,
                'created_by' => Auth::id(),
            ]);

            DB::commit();

            return response()->json([
                'title' => 'success',
                'text' => 'Điều chỉnh tồn kho thành công',
                'code' => 200
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi điều chỉnh tồn kho: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Get inventory items for datatable
     */
    public function getDatatable(Request $request)
    {
        $query = InventoryItem::with(['product', 'warehouse']);

        // Filter by warehouse
        if ($request->has('warehouse_id') && !empty($request->warehouse_id)) {
            $query->where('warehouse_id', $request->warehouse_id);
        }

        // Search
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $query->whereHas('product', function ($q) use ($searchValue) {
                $q->where('name', 'like', "%{$searchValue}%")
                  ->orWhere('code', 'like', "%{$searchValue}%");
            });
        }

        // Order
        if ($request->has('order') && !empty($request->order)) {
            $order = $request->order[0];
            $columnIndex = $order['column'];
            $columnName = $request->columns[$columnIndex]['data'];
            $columnDirection = $order['dir'];

            if ($columnName === 'product') {
                $query->join('products', 'inventory_items.product_id', '=', 'products.id')
                    ->orderBy('products.name', $columnDirection)
                    ->select('inventory_items.*');
            } else if ($columnName === 'warehouse') {
                $query->join('warehouses', 'inventory_items.warehouse_id', '=', 'warehouses.id')
                    ->orderBy('warehouses.name', $columnDirection)
                    ->select('inventory_items.*');
            } else if ($columnName !== 'actions') {
                $query->orderBy($columnName, $columnDirection);
            }
        } else {
            $query->orderBy('updated_at', 'desc');
        }

        // Pagination
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;

        $recordsTotal = $query->count();

        if ($length != -1) {
            $query->skip($start)->take($length);
        } else {
            // If length is -1, we want all records, but MySQL doesn't allow OFFSET without LIMIT
            // So we use a very large number as the limit
            $query->skip($start)->take(PHP_INT_MAX);
        }

        $inventoryItems = $query->get();

        $data = [];
        foreach ($inventoryItems as $item) {
            $data[] = [
                'id' => $item->id,
                'product' => [
                    'id' => $item->product->id,
                    'name' => $item->product->name,
                    'code' => $item->product->code,
                ],
                'warehouse' => [
                    'id' => $item->warehouse->id,
                    'name' => $item->warehouse->name,
                ],
                'quantity' => $item->quantity,
                'available_quantity' => $item->available_quantity,
                'reserved_quantity' => $item->reserved_quantity ?? 0,
                'min_stock_level' => $item->min_stock_level,
                'max_stock_level' => $item->max_stock_level,
                'updated_at' => $item->updated_at->format('d/m/Y H:i'),
                'actions' => '' // Will be rendered on the client side
            ];
        }

        return [
            'draw' => intval($request->draw),
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsTotal,
            'data' => $data
        ];
    }

    /**
     * Get products in a warehouse
     */
    public function getProductsInWarehouse(Request $request)
    {
        $warehouseId = $request->warehouse_id;

        $products = InventoryItem::where('warehouse_id', $warehouseId)
            ->where('available_quantity', '>', 0)
            ->with('product')
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->product_id,
                    'text' => $item->product->name . ' (' . $item->product->code . ') - Tồn kho: ' . $item->available_quantity,
                    'available_quantity' => $item->available_quantity
                ];
            });

        return response()->json($products);
    }

    /**
     * Get product details in a warehouse
     */
    public function getProductDetails(Request $request)
    {
        $warehouseId = $request->warehouse_id;
        $productId = $request->product_id;

        $inventoryItem = InventoryItem::where([
            'warehouse_id' => $warehouseId,
            'product_id' => $productId
        ])->with('product')->first();

        if (!$inventoryItem) {
            return response()->json([
                'success' => false,
                'message' => 'Sản phẩm không tồn tại trong kho'
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'product_name' => $inventoryItem->product->name,
                'product_code' => $inventoryItem->product->code,
                'quantity' => $inventoryItem->quantity,
                'available_quantity' => $inventoryItem->available_quantity,
                'reserved_quantity' => $inventoryItem->reserved_quantity ?? 0,
                'min_stock_level' => $inventoryItem->min_stock_level,
                'max_stock_level' => $inventoryItem->max_stock_level,
            ]
        ]);
    }
}
