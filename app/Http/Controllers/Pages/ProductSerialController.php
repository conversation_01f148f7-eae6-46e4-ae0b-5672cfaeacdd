<?php

namespace App\Http\Controllers\Pages;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductSerial;
use App\Models\GoodReceiptItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ProductSerialController extends Controller
{
    /**
     * Check if IMEI/Serial exists in the system
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkImei(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'imei' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $imei = $request->input('imei');

        // Kiểm tra IMEI chính xác hoặc IMEI đã được làm sạch (chỉ chứa số)
        $exists = ProductSerial::where('serial_number', $imei)
            ->orWhere('serial_number', 'LIKE', '%' . $imei . '%')
            ->exists();

        return response()->json([
            'success' => true,
            'exists' => $exists,
            'imei' => $imei
        ]);
    }

    /**
     * Check multiple IMEIs/Serials
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkMultipleImeis(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'imeis' => 'required|array',
            'imeis.*' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $imeis = $request->input('imeis');
        $existingImeis = [];

        // Kiểm tra từng IMEI
        foreach ($imeis as $imei) {
            // Làm sạch IMEI (chỉ giữ lại chữ và số, loại bỏ ký tự đặc biệt)
            $cleanImei = preg_replace('/[^a-zA-Z0-9]/', '', $imei);

            // Kiểm tra IMEI trong cơ sở dữ liệu
            $exists = ProductSerial::where('serial_number', $imei)
                ->orWhere('serial_number', 'LIKE', '%' . $cleanImei . '%')
                ->exists();

            if ($exists) {
                $existingImeis[] = $imei;
            }
        }

        return response()->json([
            'success' => true,
            'existing_imeis' => $existingImeis,
            'total_checked' => count($imeis),
            'total_existing' => count($existingImeis)
        ]);
    }

    /**
     * Get IMEIs/Serials for a purchase order item or good receipt item
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getImeis(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'purchase_order_item_id' => 'nullable|exists:purchase_order_items,id',
            'good_receipt_item_id' => 'nullable|exists:good_receipt_items,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $purchaseOrderItemId = $request->input('purchase_order_item_id');
        $goodReceiptItemId = $request->input('good_receipt_item_id');

        // Kiểm tra xem có ít nhất một tham số được cung cấp
        if (!$purchaseOrderItemId && !$goodReceiptItemId) {
            return response()->json([
                'success' => false,
                'message' => 'Phải cung cấp purchase_order_item_id hoặc good_receipt_item_id',
            ], 422);
        }

        // Kiểm tra các cột có tồn tại trong bảng product_serials không
        $columns = Schema::getColumnListing('product_serials');
        $query = ProductSerial::query();

        // Nếu có purchase_order_item_id và cột tồn tại
        if ($purchaseOrderItemId && in_array('purchase_order_item_id', $columns)) {
            $query->where('purchase_order_item_id', $purchaseOrderItemId);
        }

        // Nếu có good_receipt_item_id và cột tồn tại
        if ($goodReceiptItemId && in_array('good_receipt_item_id', $columns)) {
            $query->where('good_receipt_item_id', $goodReceiptItemId);
        }

        // Lấy danh sách IMEI
        $serials = $query->get(['id', 'serial_number', 'status']);

        return response()->json([
            'success' => true,
            'serials' => $serials,
            'count' => $serials->count()
        ]);
    }

    /**
     * Store multiple IMEIs/Serials for a product
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeImeis(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'purchase_order_item_id' => 'required|exists:purchase_order_items,id',
            'warehouse_id' => 'nullable|exists:warehouses,id',
            'warehouse_area_id' => 'nullable|exists:warehouse_areas,id',
            'imeis' => 'required|array',
            'imeis.*' => 'required|string|distinct',
            'good_receipt_item_id' => 'nullable|exists:good_receipt_items,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $product = Product::findOrFail($request->input('product_id'));

        // Kiểm tra xem sản phẩm có được quản lý bằng IMEI không
        if ($product->inventory_tracking_type !== 'serial') {
            return response()->json([
                'success' => false,
                'message' => 'Sản phẩm này không được quản lý bằng IMEI/Serial'
            ], 400);
        }

        $imeis = $request->input('imeis');
        $purchaseOrderItemId = $request->input('purchase_order_item_id');
        $goodReceiptItemId = $request->input('good_receipt_item_id');
        $warehouseId = $request->input('warehouse_id');
        $warehouseAreaId = $request->input('warehouse_area_id');

        // Kiểm tra IMEI đã tồn tại
        $existingImeis = [];

        foreach ($imeis as $imei) {
            // Làm sạch IMEI (chỉ giữ lại chữ và số, loại bỏ ký tự đặc biệt)
            $cleanImei = preg_replace('/[^a-zA-Z0-9]/', '', $imei);

            // Kiểm tra IMEI trong cơ sở dữ liệu
            $exists = ProductSerial::where('serial_number', $imei)
                ->orWhere('serial_number', 'LIKE', '%' . $cleanImei . '%')
                ->exists();

            if ($exists) {
                $existingImeis[] = $imei;
            }
        }

        if (count($existingImeis) > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Một số IMEI/Serial đã tồn tại trong hệ thống',
                'existing_imeis' => $existingImeis
            ], 400);
        }

        // Lưu các IMEI mới
        $savedImeis = [];
        foreach ($imeis as $imei) {
            // Kiểm tra cấu trúc bảng product_serials
            $columns = Schema::getColumnListing('product_serials');

            // Tạo mảng dữ liệu cơ bản
            $serialData = [
                'product_id' => $product->id,
                'serial_number' => $imei,
                'status' => 'available'
            ];

            // Thêm các trường khác nếu chúng tồn tại trong bảng
            if (in_array('purchase_order_item_id', $columns)) {
                $serialData['purchase_order_item_id'] = $purchaseOrderItemId;
            }

            // Thêm good_receipt_item_id nếu có
            if (in_array('good_receipt_item_id', $columns)) {
                if ($goodReceiptItemId) {
                    // Sử dụng good_receipt_item_id từ request nếu có
                    $serialData['good_receipt_item_id'] = $goodReceiptItemId;
                } else {
                    // Tìm good_receipt_item_id dựa trên purchase_order_item_id
                    $goodReceiptItem = GoodReceiptItem::where('purchase_order_item_id', $purchaseOrderItemId)->first();

                    if ($goodReceiptItem) {
                        $serialData['good_receipt_item_id'] = $goodReceiptItem->id;
                    } else {
                        // Tìm purchase order item
                        $purchaseOrderItem = \App\Models\PurchaseOrderItem::with('purchaseOrder')->find($purchaseOrderItemId);

                        if ($purchaseOrderItem && $purchaseOrderItem->purchaseOrder) {
                            // Tìm hoặc tạo good receipt
                            $goodReceipt = \App\Models\GoodReceipt::firstOrCreate(
                                ['purchase_order_id' => $purchaseOrderItem->purchaseOrder->id, 'status' => 'completed'],
                                [
                                    'code' => 'GR' . date('Ymd') . \Illuminate\Support\Str::random(5),
                                    'warehouse_id' => $purchaseOrderItem->purchaseOrder->warehouse_id,
                                    'created_by' => auth()->id() ?? 1,
                                    'notes' => 'Tạo tự động khi nhập IMEI'
                                ]
                            );

                            // Tạo good receipt item
                            $goodReceiptItem = \App\Models\GoodReceiptItem::firstOrCreate(
                                ['good_receipt_id' => $goodReceipt->id, 'purchase_order_item_id' => $purchaseOrderItemId],
                                [
                                    'product_id' => $purchaseOrderItem->product_id,
                                    'warehouse_area_id' => $warehouseAreaId,
                                    'quantity' => $purchaseOrderItem->quantity, // Mặc định là 1 vì chỉ đang nhập IMEI
                                    'unit_price' => $purchaseOrderItem->unit_price,
                                ]
                            );

                            $serialData['good_receipt_item_id'] = $goodReceiptItem->id;

                            // Log thông tin
                            \Illuminate\Support\Facades\Log::info('Created new GoodReceiptItem for IMEI', [
                                'good_receipt_id' => $goodReceipt->id,
                                'purchase_order_item_id' => $purchaseOrderItemId,
                                'good_receipt_item_id' => $goodReceiptItem->id
                            ]);
                        }
                    }
                }

                // Log để debug
                \Illuminate\Support\Facades\Log::info('Saving product serial with good_receipt_item_id', [
                    'serial_number' => $imei,
                    'purchase_order_item_id' => $purchaseOrderItemId,
                    'good_receipt_item_id' => $serialData['good_receipt_item_id'] ?? null
                ]);
            }

            if (in_array('warehouse_id', $columns) && $warehouseId) {
                $serialData['warehouse_id'] = $warehouseId;
            }

            if (in_array('warehouse_area_id', $columns) && $warehouseAreaId) {
                $serialData['warehouse_area_id'] = $warehouseAreaId;
            }

            // Tạo bản ghi mới
            $serial = ProductSerial::create($serialData);

            $savedImeis[] = $serial->serial_number;
        }

        // Lấy good_receipt_item_id để trả về cho client
        $goodReceiptItemId = null;
        if (isset($serialData['good_receipt_item_id'])) {
            $goodReceiptItemId = $serialData['good_receipt_item_id'];
        } else {
            // Tìm good_receipt_item_id dựa trên purchase_order_item_id
            $goodReceiptItem = GoodReceiptItem::where('purchase_order_item_id', $purchaseOrderItemId)->first();
            if ($goodReceiptItem) {
                $goodReceiptItemId = $goodReceiptItem->id;
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã lưu ' . count($savedImeis) . ' IMEI/Serial thành công',
            'saved_imeis' => $savedImeis,
            'good_receipt_item_id' => $goodReceiptItemId
        ]);
    }
}
