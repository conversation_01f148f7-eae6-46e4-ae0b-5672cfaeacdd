<?php

namespace App\Http\Controllers\Pages;

use App\Enums\Roles;
use App\Enums\Traits\Datatable;
use App\Http\Controllers\Controller;
use App\Http\Requests\UserRequest;
use App\Repositories\UsersRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class UsersController extends Controller
{
  use Datatable;

  protected $users;

  public function __construct(UsersRepository $users)
  {
    $this->users = $users;
  }

  public function list()
  {
    $users = $this->users->all();
    $totalUser = $users->count();
    $verified = $this->users->countVerify();
    $notVerified = $this->users->countNotVerify();
    $userDuplicates = $this->users->countDuplicates();
    $roles = Roles::cases();

    return view('content.pages.users.lists', compact('totalUser','verified','notVerified','userDuplicates','roles'));
  }

  public function index(Request $request): JsonResponse
  {
    $start = $request->input('start');
    $usersData = $this->users->getUsersDatatable($request);
    $users = $usersData['users'];
    $totalFiltered = $usersData['totalFiltered'];
    $totalData = $usersData['totalData'];

    $data = [];

    if (!empty($users)) {
      // providing a dummy id instead of database ids
      $ids = $start;
      foreach ($users as $user) {
        $nestedData['id'] = $user->id;
        $nestedData['fake_id'] = ++$ids;
        $nestedData['name'] = $user->name;
        $nestedData['email'] = $user->email;
        $nestedData['role'] = $user->getRoleNames()->first();
        $nestedData['email_verified_at'] = $user->email_verified_at;
        $data[] = $nestedData;
      }
    }

    if ($data) {
      return response()->json([
        'draw' => intval($request->input('draw')),
        'recordsTotal' => intval($totalData),
        'recordsFiltered' => intval($totalFiltered),
        'code' => 200,
        'data' => $data,
      ]);
    } else {
      return response()->json([
        'message' => 'Internal Server Error',
        'code' => 500,
        'data' => [],
      ]);
    }
  }

  /**
   * Store a newly created resource in storage.
   *
   * @param UserRequest $request
   * @return Response
   */
  public function store(UserRequest $request)
  {
    $createUser = $this->users->createUser($request);
    if ($createUser == "updated") {
      return response()->json(['title' => 'success', 'text' => __('common.update_success'), 'code' => 200]);
    } else {
      if ($createUser == "created") {
        return response()->json(['title' => 'success', 'text' => __('common.create_success'), 'code' => 200]);
      } else {
        return response()->json(['message' => "already exits"], 422);
      }
    }
  }

  /**
   * Show the form for editing the specified resource.
   *
   * @param int $id
   * @return \Illuminate\Http\Response
   */
  public function edit($id): JsonResponse
  {
    $user = $this->users->getById($id);
    return response()->json($user);
  }

  /**
   * Remove the specified resource from storage.
   *
   * @param int $id
   * @return \Illuminate\Http\Response
   */
  public function destroy($id)
  {
    $users = $this->users->where('id', $id)->delete();
  }
}
