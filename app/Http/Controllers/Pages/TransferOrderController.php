<?php

namespace App\Http\Controllers\Pages;

use App\Contracts\TransferOrderInterface;
use App\Contracts\TransferRequestInterface;
use App\Http\Controllers\Controller;
use App\Models\Warehouse;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class TransferOrderController extends Controller
{
    protected $transferOrderRepository;
    protected $transferRequestRepository;

    public function __construct(
        TransferOrderInterface $transferOrderRepository,
        TransferRequestInterface $transferRequestRepository
    ) {
        $this->transferOrderRepository = $transferOrderRepository;
        $this->transferRequestRepository = $transferRequestRepository;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $pageConfigs = ['myLayout' => 'vertical'];

        $warehouses = Warehouse::active()->get();

        return view('content.pages.transfers.transfer-orders.index', compact('pageConfigs', 'warehouses'));
    }

    /**
     * Get data for DataTable
     */
    public function datatable(Request $request)
    {
        try {
            $params = $request->all();
            $result = $this->transferOrderRepository->getForDatatable($params);

            $data = [];
            foreach ($result['data'] as $item) {
                $statusBadge = match($item->status) {
                    'draft' => '<span class="badge bg-label-secondary">Nháp</span>',
                    'pending' => '<span class="badge bg-label-warning">Chờ duyệt</span>',
                    'approved' => '<span class="badge bg-label-success">Đã duyệt</span>',
                    'rejected' => '<span class="badge bg-label-danger">Từ chối</span>',
                    'cancelled' => '<span class="badge bg-label-dark">Đã hủy</span>',
                    default => '<span class="badge bg-label-secondary">Không xác định</span>',
                };

                $actions = '<div class="dropdown">
                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                        <i class="ri-more-2-line"></i>
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="' . route('transfer-orders.show', $item->id) . '">
                            <i class="ri-eye-line me-1"></i> Xem chi tiết
                        </a>';

                if (in_array($item->status, ['draft', 'pending'])) {
                    $actions .= '<a class="dropdown-item" href="' . route('transfer-orders.edit', $item->id) . '">
                        <i class="ri-pencil-line me-1"></i> Chỉnh sửa
                    </a>';
                }

                if ($item->status === 'draft') {
                    $actions .= '<a class="dropdown-item submit-approval-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-send-plane-line me-1"></i> Gửi duyệt
                    </a>';
                }

                if ($item->status === 'pending') {
                    $actions .= '<a class="dropdown-item approve-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-check-line me-1"></i> Duyệt
                    </a>
                    <a class="dropdown-item reject-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-close-line me-1"></i> Từ chối
                    </a>';
                }

                if (in_array($item->status, ['draft', 'pending'])) {
                    $actions .= '<div class="dropdown-divider"></div>
                    <a class="dropdown-item text-danger cancel-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-delete-bin-7-line me-1"></i> Hủy
                    </a>';
                }

                $actions .= '</div></div>';

                $data[] = [
                    'id' => $item->id,
                    'code' => $item->code,
                    'transfer_request_code' => $item->transfer_request_code,
                    'from_warehouse' => $item->from_warehouse_name,
                    'to_warehouse' => $item->to_warehouse_name,
                    'transit_warehouse' => $item->transit_warehouse_name,
                    'status' => $statusBadge,
                    'created_by' => $item->created_by_name,
                    'created_at' => date('d/m/Y H:i', strtotime($item->created_at)),
                    'actions' => $actions
                ];
            }

            return response()->json([
                'draw' => intval($request->draw),
                'recordsTotal' => $result['recordsTotal'],
                'recordsFiltered' => $result['recordsFiltered'],
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('Error in transfer order datatable: ' . $e->getMessage());
            return response()->json(['error' => 'Có lỗi xảy ra khi tải dữ liệu'], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pageConfigs = ['myLayout' => 'vertical'];

        $warehouses = Warehouse::active()->get();
        $products = Product::where('is_active', true)->get();
        $approvedRequests = $this->transferRequestRepository->getApprovedWithoutTransferOrder();

        return view('content.pages.transfers.transfer-orders.create', compact('pageConfigs', 'warehouses', 'products', 'approvedRequests'));
    }

    /**
     * Create transfer order from approved transfer request
     */
    public function createFromRequest($requestId)
    {
        try {
            $pageConfigs = ['myLayout' => 'vertical'];

            $transferRequest = $this->transferRequestRepository->getById($requestId);

            if ($transferRequest->status !== 'approved') {
                return redirect()->route('transfer-orders.index')->with('error', 'Chỉ có thể tạo phiếu chuyển hàng từ yêu cầu đã được duyệt.');
            }

            if ($transferRequest->transferOrder) {
                return redirect()->route('transfer-orders.show', $transferRequest->transferOrder->id)->with('info', 'Phiếu chuyển hàng đã được tạo từ yêu cầu này.');
            }

            $warehouses = Warehouse::active()->get();

            return view('content.pages.transfers.transfer-orders.create-from-request', compact('pageConfigs', 'transferRequest', 'warehouses'));
        } catch (\Exception $e) {
            Log::error('Error creating transfer order from request: ' . $e->getMessage());
            return redirect()->route('transfer-orders.index')->with('error', 'Không tìm thấy yêu cầu chuyển kho.');
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if ($request->has('transfer_request_id')) {
            // Creating from transfer request
            $request->validate([
                'transfer_request_id' => 'required|exists:transfer_requests,id',
                'notes' => 'nullable|string',
            ]);

            try {
                $data = $request->only(['notes']);
                $data['created_by'] = Auth::id();

                $transferOrder = $this->transferOrderRepository->createFromTransferRequest($request->transfer_request_id, $data);

                return response()->json([
                    'success' => true,
                    'message' => 'Phiếu chuyển hàng đã được tạo thành công từ yêu cầu chuyển kho.',
                    'redirect' => route('transfer-orders.show', $transferOrder->id)
                ]);
            } catch (\Exception $e) {
                Log::error('Error creating transfer order from request: ' . $e->getMessage());
                return response()->json([
                    'success' => false,
                    'message' => 'Có lỗi xảy ra khi tạo phiếu chuyển hàng.'
                ], 500);
            }
        } else {
            // Creating manually
            $request->validate([
                'from_warehouse_id' => 'required|exists:warehouses,id',
                'to_warehouse_id' => 'required|exists:warehouses,id|different:from_warehouse_id',
                'notes' => 'nullable|string',
                'items' => 'required|array|min:1',
                'items.*.product_id' => 'required|exists:products,id',
                'items.*.quantity' => 'required|numeric|min:0.01',
                'items.*.notes' => 'nullable|string',
            ]);

            try {
                $data = $request->only(['from_warehouse_id', 'to_warehouse_id', 'notes']);
                $data['created_by'] = Auth::id();
                $data['items'] = $request->items;

                $transferOrder = $this->transferOrderRepository->create($data);

                return response()->json([
                    'success' => true,
                    'message' => 'Phiếu chuyển hàng đã được tạo thành công.',
                    'redirect' => route('transfer-orders.show', $transferOrder->id)
                ]);
            } catch (\Exception $e) {
                Log::error('Error creating transfer order: ' . $e->getMessage());
                return response()->json([
                    'success' => false,
                    'message' => 'Có lỗi xảy ra khi tạo phiếu chuyển hàng.'
                ], 500);
            }
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            $pageConfigs = ['myLayout' => 'vertical'];

            $transferOrder = $this->transferOrderRepository->getById($id);

            return view('content.pages.transfers.transfer-orders.show', compact('pageConfigs', 'transferOrder'));
        } catch (\Exception $e) {
            Log::error('Error showing transfer order: ' . $e->getMessage());
            return redirect()->route('transfer-orders.index')->with('error', 'Không tìm thấy phiếu chuyển hàng.');
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        try {
            $pageConfigs = ['myLayout' => 'vertical'];

            $transferOrder = $this->transferOrderRepository->getById($id);

            if (!$transferOrder->canBeEdited()) {
                return redirect()->route('transfer-orders.show', $id)->with('error', 'Phiếu chuyển hàng không thể chỉnh sửa.');
            }

            $warehouses = Warehouse::active()->get();
            $products = Product::where('is_active', true)->get();

            return view('content.pages.transfers.transfer-orders.edit', compact('pageConfigs', 'transferOrder', 'warehouses', 'products'));
        } catch (\Exception $e) {
            Log::error('Error editing transfer order: ' . $e->getMessage());
            return redirect()->route('transfer-orders.index')->with('error', 'Không tìm thấy phiếu chuyển hàng.');
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'from_warehouse_id' => 'required|exists:warehouses,id',
            'to_warehouse_id' => 'required|exists:warehouses,id|different:from_warehouse_id',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.notes' => 'nullable|string',
        ]);

        try {
            $transferOrder = $this->transferOrderRepository->getById($id);

            if (!$transferOrder->canBeEdited()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Phiếu chuyển hàng không thể chỉnh sửa.'
                ], 422);
            }

            $data = $request->only(['from_warehouse_id', 'to_warehouse_id', 'notes']);
            $data['items'] = $request->items;

            $this->transferOrderRepository->update($id, $data);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu chuyển hàng đã được cập nhật thành công.',
                'redirect' => route('transfer-orders.show', $id)
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating transfer order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật phiếu chuyển hàng.'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $transferOrder = $this->transferOrderRepository->getById($id);

            if (!$transferOrder->canBeEdited()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Phiếu chuyển hàng không thể xóa.'
                ], 422);
            }

            $this->transferOrderRepository->delete($id);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu chuyển hàng đã được xóa thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting transfer order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa phiếu chuyển hàng.'
            ], 500);
        }
    }

    /**
     * Submit transfer order for approval
     */
    public function submitForApproval(Request $request, $id)
    {
        try {
            $this->transferOrderRepository->submitForApproval($id);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu chuyển hàng đã được gửi duyệt thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error submitting transfer order for approval: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve transfer order
     */
    public function approve(Request $request, $id)
    {
        try {
            $this->transferOrderRepository->approve($id, Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'Phiếu chuyển hàng đã được duyệt thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error approving transfer order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject transfer order
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        try {
            $this->transferOrderRepository->reject($id, Auth::id(), $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu chuyển hàng đã được từ chối.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error rejecting transfer order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel transfer order
     */
    public function cancel(Request $request, $id)
    {
        try {
            $this->transferOrderRepository->cancel($id);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu chuyển hàng đã được hủy.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error cancelling transfer order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate IMEI availability for transfer order
     */
    public function validateImei(Request $request, $id)
    {
        $request->validate([
            'item_id' => 'required|exists:transfer_order_items,id',
            'imei' => 'required|string|max:255',
        ]);

        try {
            $result = $this->transferOrderRepository->validateImeiAvailability(
                $id,
                $request->item_id,
                $request->imei
            );

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Error validating IMEI for transfer order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Add IMEI to transfer order item
     */
    public function addImei(Request $request, $id)
    {
        $request->validate([
            'item_id' => 'required|exists:transfer_order_items,id',
            'imei' => 'required|string|max:255',
        ]);

        try {
            $imei = $this->transferOrderRepository->addImei(
                $id,
                $request->item_id,
                $request->imei,
                Auth::id()
            );

            return response()->json([
                'success' => true,
                'message' => 'IMEI đã được thêm thành công.',
                'data' => $imei
            ]);
        } catch (\Exception $e) {
            Log::error('Error adding IMEI to transfer order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Remove IMEI from transfer order item
     */
    public function removeImei(Request $request, $id, $imeiId)
    {
        try {
            $this->transferOrderRepository->removeImei($id, $imeiId);

            return response()->json([
                'success' => true,
                'message' => 'IMEI đã được xóa thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error removing IMEI from transfer order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Validate batch availability for transfer order
     */
    public function validateBatch(Request $request, $id)
    {
        $request->validate([
            'item_id' => 'required|exists:transfer_order_items,id',
            'batch_number' => 'required|string|max:255',
            'quantity' => 'required|numeric|min:0.01',
        ]);

        try {
            $result = $this->transferOrderRepository->validateBatchAvailability(
                $id,
                $request->item_id,
                $request->batch_number,
                $request->quantity
            );

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Error validating batch for transfer order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Add batch to transfer order item
     */
    public function addBatch(Request $request, $id)
    {
        $request->validate([
            'item_id' => 'required|exists:transfer_order_items,id',
            'batch_number' => 'required|string|max:255',
            'quantity' => 'required|numeric|min:0.01',
            'expiry_date' => 'nullable|date',
        ]);

        try {
            $batch = $this->transferOrderRepository->addBatch(
                $id,
                $request->item_id,
                $request->only(['batch_number', 'quantity', 'expiry_date'])
            );

            return response()->json([
                'success' => true,
                'message' => 'Batch đã được thêm thành công.',
                'data' => $batch
            ]);
        } catch (\Exception $e) {
            Log::error('Error adding batch to transfer order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Remove batch from transfer order item
     */
    public function removeBatch(Request $request, $id, $batchId)
    {
        try {
            $this->transferOrderRepository->removeBatch($id, $batchId);

            return response()->json([
                'success' => true,
                'message' => 'Batch đã được xóa thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error removing batch from transfer order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get IMEIs for transfer order item
     */
    public function getImeis($id, $itemId)
    {
        try {
            $transferOrder = $this->transferOrderRepository->getById($id);
            $item = $transferOrder->items()->with('imeis.scannedBy')->findOrFail($itemId);

            return response()->json([
                'success' => true,
                'data' => $item->imeis
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting IMEIs for transfer order item: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải danh sách IMEI.'
            ], 500);
        }
    }

    /**
     * Get batches for transfer order item
     */
    public function getBatches($id, $itemId)
    {
        try {
            $transferOrder = $this->transferOrderRepository->getById($id);
            $item = $transferOrder->items()->with('batches')->findOrFail($itemId);

            return response()->json([
                'success' => true,
                'data' => $item->batches
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting batches for transfer order item: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải danh sách batch.'
            ], 500);
        }
    }

    /**
     * Check if transfer order is ready for approval
     */
    public function checkReadyForApproval($id)
    {
        try {
            $isReady = $this->transferOrderRepository->isReadyForApproval($id);

            return response()->json([
                'success' => true,
                'is_ready' => $isReady,
                'message' => $isReady ? 'Phiếu chuyển hàng đã sẵn sàng để gửi duyệt.' : 'Vui lòng nhập đầy đủ IMEI/batch cho tất cả sản phẩm.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error checking transfer order readiness: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi kiểm tra trạng thái phiếu chuyển hàng.'
            ], 500);
        }
    }
}
