<?php

namespace App\Http\Controllers\Pages;

use App\Contracts\WarehouseInterface;
use App\Contracts\WarehouseTransferInterface;
use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\WarehouseTransfer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WarehouseTransferController extends Controller
{
    protected $warehouses;
    protected $warehouseTransfers;

    public function __construct(WarehouseInterface $warehouses, WarehouseTransferInterface $warehouseTransfers)
    {
        $this->warehouses = $warehouses;
        $this->warehouseTransfers = $warehouseTransfers;
    }

    /**
     * Hi<PERSON>n thị danh sách phiếu chuyển kho
     */
    public function index(Request $request)
    {
        $warehouses = $this->warehouses->getAllWarehouses();
        $statuses = [
            WarehouseTransfer::STATUS_DRAFT => 'Nháp',
            WarehouseTransfer::STATUS_PENDING => 'Chờ duyệt',
            WarehouseTransfer::STATUS_APPROVED => 'Đã duyệt',
            WarehouseTransfer::STATUS_SHIPPING => 'Đang vận chuyển',
            WarehouseTransfer::STATUS_COMPLETED => 'Hoàn thành',
            WarehouseTransfer::STATUS_CANCELLED => 'Đã hủy'
        ];

        return view('content.pages.warehouses.transfers.index', compact('warehouses', 'statuses'));
    }

    // Phiếu chuyển kho chỉ được tạo từ phiếu yêu cầu chuyển hàng đã được duyệt

    /**
     * Hiển thị chi tiết phiếu chuyển kho
     */
    public function show($id)
    {
        $warehouseTransfer = $this->warehouseTransfers->findById($id);

        // Log trạng thái thực tế của phiếu chuyển kho
        \Illuminate\Support\Facades\Log::info('Trạng thái phiếu chuyển kho ' . $id . ': ' . $warehouseTransfer->status);
        \Illuminate\Support\Facades\Log::info('Trạng thái hiển thị: ' . $warehouseTransfer->statusText);

        return view('content.pages.warehouses.transfers.show', compact('warehouseTransfer'));
    }

    // Đã xóa phương thức edit và update

    /**
     * Gửi phiếu chuyển kho để duyệt
     */
    public function submit($id)
    {
        $result = $this->warehouseTransfers->submitForApproval($id);

        if ($result['success']) {
            return response()->json([
                'title' => 'success',
                'text' => $result['message'],
                'code' => 200,
                'redirect' => route('warehouses.transfers.show', $id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['message'],
                'code' => 400
            ]);
        }
    }

    /**
     * Duyệt phiếu chuyển kho
     */
    public function approve($id)
    {
        $result = $this->warehouseTransfers->approveWarehouseTransfer($id, Auth::id());

        if ($result['code'] === 200) {
            return response()->json([
                'title' => 'success',
                'text' => $result['text'],
                'code' => 200,
                'redirect' => route('warehouses.transfers.show', $id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['text'],
                'code' => $result['code'] ?? 400
            ]);
        }
    }

    /**
     * Hủy phiếu chuyển kho
     */
    public function cancel(Request $request, $id)
    {
        $request->validate([
            'reason' => 'nullable|string'
        ]);

        $result = $this->warehouseTransfers->cancelWarehouseTransfer($id, Auth::id(), $request->reason);

        if ($result['code'] == 200) {
            return response()->json([
                'title' => 'success',
                'text' => $result['text'],
                'code' => 200,
                'redirect' => route('warehouses.transfers.show', $id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['text'],
                'code' => $result['code'] ?? 400
            ]);
        }
    }

    /**
     * Lấy dữ liệu cho datatable
     */
    public function getDatatable(Request $request)
    {
        return $this->warehouseTransfers->getWarehouseTransfersForDatatable($request);
    }

    /**
     * Lấy danh sách sản phẩm trong kho
     */
    public function getProductsInWarehouse(Request $request)
    {
        $warehouseId = $request->warehouse_id;

        if (!$warehouseId) {
            return response()->json([]);
        }

        $products = Product::select('products.id', 'products.name', 'products.code', 'inventory_items.quantity', 'inventory_items.available_quantity')
            ->join('inventory_items', 'products.id', '=', 'inventory_items.product_id')
            ->where('inventory_items.warehouse_id', $warehouseId)
            ->where('inventory_items.available_quantity', '>', 0)
            ->where('products.is_active', true)
            ->get();

        return response()->json($products);
    }

    /**
     * Lấy thông tin chi tiết sản phẩm
     */
    public function getProductDetails(Request $request)
    {
        $productId = $request->product_id;
        $warehouseId = $request->warehouse_id;

        if (!$productId || !$warehouseId) {
            return response()->json([]);
        }

        $product = Product::with(['inventoryItems' => function ($query) use ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }])->find($productId);

        if (!$product) {
            return response()->json([]);
        }

        $warehouseAreas = $this->warehouses->getWarehouseAreas($warehouseId);

        return response()->json([
            'product' => $product,
            'warehouse_areas' => $warehouseAreas
        ]);
    }

    /**
     * Lấy danh sách khu vực kho
     */
    public function getWarehouseAreas(Request $request)
    {
        $warehouseId = $request->warehouse_id;

        if (!$warehouseId) {
            return response()->json([]);
        }

        $warehouseAreas = $this->warehouses->getWarehouseAreas($warehouseId);

        return response()->json($warehouseAreas);
    }

    /**
     * Tạo phiếu vận chuyển cho phiếu chuyển kho
     */
    public function createShipping(Request $request, $id)
    {
        $request->validate([
            'shipping_date' => 'required|date',
            'shipping_notes' => 'nullable|string|max:1000',
        ]);

        $result = $this->warehouseTransfers->createShippingDocument($id, Auth::id(), $request->all());

        if (isset($result['code']) && $result['code'] === 200) {
            return response()->json([
                'title' => 'success',
                'text' => $result['text'],
                'code' => 200,
                'redirect' => route('warehouses.transfers.show', $id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['text'] ?? 'Có lỗi xảy ra khi tạo phiếu vận chuyển',
                'code' => $result['code'] ?? 400
            ]);
        }
    }

    /**
     * Tạo phiếu nhận hàng cho phiếu chuyển kho
     */
    public function createReceipt(Request $request, $id)
    {
        $request->validate([
            'receipt_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        $result = $this->warehouseTransfers->createReceipt($id, Auth::id(), $request->all());

        if (isset($result['code']) && $result['code'] === 200) {
            return response()->json([
                'title' => 'success',
                'text' => $result['text'],
                'code' => 200,
                'redirect' => $result['redirect'] ?? route('warehouses.receipts.show', $result['data']->id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['text'] ?? 'Có lỗi xảy ra khi tạo phiếu nhận hàng',
                'code' => $result['code'] ?? 400
            ]);
        }
    }
}
