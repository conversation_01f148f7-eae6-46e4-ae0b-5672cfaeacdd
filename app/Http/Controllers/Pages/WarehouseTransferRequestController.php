<?php

namespace App\Http\Controllers\Pages;

use App\Contracts\WarehouseInterface;
use App\Contracts\WarehouseTransferRequestInterface;
use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\WarehouseTransferRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WarehouseTransferRequestController extends Controller
{
    protected $warehouses;
    protected $warehouseTransferRequests;

    public function __construct(WarehouseInterface $warehouses, WarehouseTransferRequestInterface $warehouseTransferRequests)
    {
        $this->warehouses = $warehouses;
        $this->warehouseTransferRequests = $warehouseTransferRequests;
    }

    /**
     * Hiển thị danh sách yêu cầu chuyển hàng
     */
    public function index(Request $request)
    {
        $warehouses = $this->warehouses->getAllWarehouses();
        $statuses = [
            WarehouseTransferRequest::STATUS_DRAFT => 'Nháp',
            WarehouseTransferRequest::STATUS_PENDING => 'Chờ duyệt',
            WarehouseTransferRequest::STATUS_APPROVED => 'Đã duyệt',
            WarehouseTransferRequest::STATUS_REJECTED => 'Đã từ chối',
            WarehouseTransferRequest::STATUS_CANCELLED => 'Đã hủy',
            WarehouseTransferRequest::STATUS_COMPLETED => 'Hoàn thành'
        ];
        $priorities = [
            WarehouseTransferRequest::PRIORITY_LOW => 'Thấp',
            WarehouseTransferRequest::PRIORITY_NORMAL => 'Bình thường',
            WarehouseTransferRequest::PRIORITY_HIGH => 'Cao',
            WarehouseTransferRequest::PRIORITY_URGENT => 'Khẩn cấp'
        ];

        return view('content.pages.warehouses.transfer-requests.index', compact('warehouses', 'statuses', 'priorities'));
    }

    /**
     * Hiển thị form tạo yêu cầu chuyển hàng
     */
    public function create()
    {
        $warehouses = $this->warehouses->getAllWarehouses();
        $priorities = [
            WarehouseTransferRequest::PRIORITY_LOW => 'Thấp',
            WarehouseTransferRequest::PRIORITY_NORMAL => 'Bình thường',
            WarehouseTransferRequest::PRIORITY_HIGH => 'Cao',
            WarehouseTransferRequest::PRIORITY_URGENT => 'Khẩn cấp'
        ];

        return view('content.pages.warehouses.transfer-requests.create', compact('warehouses', 'priorities'));
    }

    /**
     * Lưu yêu cầu chuyển hàng mới
     */
    public function store(Request $request)
    {
        $request->validate([
            'source_warehouse_id' => 'required|exists:warehouses,id',
            'destination_warehouse_id' => 'required|exists:warehouses,id|different:source_warehouse_id',
            'priority' => 'required|in:low,normal,high,urgent',
            'requested_date' => 'nullable|date',
            'expected_date' => 'nullable|date',
            'reason' => 'nullable|string',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.batch_number' => 'nullable|string',
            'items.*.expiry_date' => 'nullable|date',
            'items.*.notes' => 'nullable|string',
        ]);

        $result = $this->warehouseTransferRequests->createWarehouseTransferRequest($request->all());

        if ($result['success']) {
            return response()->json([
                'title' => 'success',
                'text' => $result['message'],
                'code' => 200,
                'redirect' => route('warehouses.transfer-requests.show', $result['data']->id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['message'],
                'code' => 400
            ]);
        }
    }

    /**
     * Hiển thị chi tiết yêu cầu chuyển hàng
     */
    public function show($id)
    {
        $warehouseTransferRequest = $this->warehouseTransferRequests->findById($id);

        return view('content.pages.warehouses.transfer-requests.show', compact('warehouseTransferRequest'));
    }

    /**
     * Hiển thị form chỉnh sửa yêu cầu chuyển hàng
     */
    public function edit($id)
    {
        $warehouseTransferRequest = $this->warehouseTransferRequests->findById($id);

        if ($warehouseTransferRequest->status !== WarehouseTransferRequest::STATUS_DRAFT) {
            return redirect()->route('warehouses.transfer-requests.show', $id)
                ->with('error', 'Chỉ có thể chỉnh sửa yêu cầu chuyển hàng ở trạng thái nháp');
        }

        $warehouses = $this->warehouses->getAllWarehouses();
        $priorities = [
            WarehouseTransferRequest::PRIORITY_LOW => 'Thấp',
            WarehouseTransferRequest::PRIORITY_NORMAL => 'Bình thường',
            WarehouseTransferRequest::PRIORITY_HIGH => 'Cao',
            WarehouseTransferRequest::PRIORITY_URGENT => 'Khẩn cấp'
        ];

        return view('content.pages.warehouses.transfer-requests.edit', compact('warehouseTransferRequest', 'warehouses', 'priorities'));
    }

    /**
     * Cập nhật yêu cầu chuyển hàng
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'source_warehouse_id' => 'required|exists:warehouses,id',
            'destination_warehouse_id' => 'required|exists:warehouses,id|different:source_warehouse_id',
            'priority' => 'required|in:low,normal,high,urgent',
            'requested_date' => 'nullable|date',
            'expected_date' => 'nullable|date',
            'reason' => 'nullable|string',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.batch_number' => 'nullable|string',
            'items.*.expiry_date' => 'nullable|date',
            'items.*.notes' => 'nullable|string',
        ]);

        $result = $this->warehouseTransferRequests->updateWarehouseTransferRequest($id, $request->all());

        if ($result['success']) {
            return response()->json([
                'title' => 'success',
                'text' => $result['message'],
                'code' => 200,
                'redirect' => route('warehouses.transfer-requests.show', $id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['message'],
                'code' => 400
            ]);
        }
    }

    /**
     * Gửi yêu cầu chuyển hàng để duyệt
     */
    public function submit($id)
    {
        $result = $this->warehouseTransferRequests->submitForApproval($id);

        if ($result['success']) {
            return response()->json([
                'title' => 'success',
                'text' => $result['message'],
                'code' => 200,
                'redirect' => route('warehouses.transfer-requests.show', $id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['message'],
                'code' => 400
            ]);
        }
    }

    /**
     * Hiển thị form duyệt yêu cầu chuyển hàng
     */
    public function showApprove($id)
    {
        $warehouseTransferRequest = $this->warehouseTransferRequests->findById($id);

        if ($warehouseTransferRequest->status !== WarehouseTransferRequest::STATUS_PENDING) {
            return redirect()->route('warehouses.transfer-requests.show', $id)
                ->with('error', 'Chỉ có thể duyệt yêu cầu chuyển hàng ở trạng thái chờ duyệt');
        }

        return view('content.pages.warehouses.transfer-requests.approve', compact('warehouseTransferRequest'));
    }

    /**
     * Duyệt yêu cầu chuyển hàng
     */
    public function approve(Request $request, $id)
    {
        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.id' => 'required|exists:warehouse_transfer_request_items,id',
            'items.*.approved_quantity' => 'required|numeric|min:0',
            'notes' => 'nullable|string'
        ]);

        $result = $this->warehouseTransferRequests->approveWarehouseTransferRequest($id, Auth::id(), $request->all());

        if ($result['success']) {
            return response()->json([
                'title' => 'success',
                'text' => $result['message'],
                'code' => 200,
                'redirect' => route('warehouses.transfer-requests.show', $id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['message'],
                'code' => 400
            ]);
        }
    }

    /**
     * Từ chối yêu cầu chuyển hàng
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string'
        ]);

        $result = $this->warehouseTransferRequests->rejectWarehouseTransferRequest($id, Auth::id(), $request->reason);

        if ($result['success']) {
            return response()->json([
                'title' => 'success',
                'text' => $result['message'],
                'code' => 200,
                'redirect' => route('warehouses.transfer-requests.show', $id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['message'],
                'code' => 400
            ]);
        }
    }

    /**
     * Hủy yêu cầu chuyển hàng
     */
    public function cancel(Request $request, $id)
    {
        $request->validate([
            'reason' => 'nullable|string'
        ]);

        $result = $this->warehouseTransferRequests->cancelWarehouseTransferRequest($id, Auth::id(), $request->reason);

        if ($result['success']) {
            return response()->json([
                'title' => 'success',
                'text' => $result['message'],
                'code' => 200,
                'redirect' => route('warehouses.transfer-requests.show', $id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['message'],
                'code' => 400
            ]);
        }
    }

    /**
     * Tạo phiếu chuyển kho từ yêu cầu
     */
    public function createTransfer($id)
    {
        $result = $this->warehouseTransferRequests->createWarehouseTransferFromRequest($id, Auth::id());

        if ($result['success']) {
            return response()->json([
                'title' => 'success',
                'text' => $result['message'],
                'code' => 200,
                'redirect' => route('warehouses.transfers.show', $result['data']['warehouseTransfer']->id)
            ]);
        } else {
            return response()->json([
                'title' => 'errors',
                'text' => $result['message'],
                'code' => 400
            ]);
        }
    }

    /**
     * Lấy dữ liệu cho datatable
     */
    public function getDatatable(Request $request)
    {
        return $this->warehouseTransferRequests->getWarehouseTransferRequestsForDatatable($request);
    }

    /**
     * Lấy danh sách sản phẩm trong kho
     */
    public function getProductsInWarehouse(Request $request, $warehouseId = null)
    {
        if (!$warehouseId) {
            $warehouseId = $request->warehouse_id;
        }

        if (!$warehouseId) {
            return response()->json(['results' => []]);
        }

        $search = $request->input('q');
        $page = $request->input('page', 1);
        $perPage = 30; // Số sản phẩm mỗi trang

        $query = Product::select('products.id', 'products.name', 'products.code', 'products.unit', 'products.image', 'inventory_items.quantity', 'inventory_items.available_quantity')
            ->join('inventory_items', 'products.id', '=', 'inventory_items.product_id')
            ->where('inventory_items.warehouse_id', $warehouseId)
            ->where('inventory_items.available_quantity', '>', 0)
            ->where('products.is_active', true);

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('products.name', 'like', "%{$search}%")
                  ->orWhere('products.code', 'like', "%{$search}%");
            });
        }

        // Đếm tổng số sản phẩm phù hợp
        $total = $query->count();

        // Lấy sản phẩm theo trang
        $products = $query->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get()
            ->map(function($product) {
                return [
                    'id' => $product->id,
                    'text' => $product->name . ' (' . $product->code . ')',
                    'code' => $product->code,
                    'name' => $product->name,
                    'unit' => $product->unit,
                    'image' => $product->image ? asset('storage/' . $product->image) : null,
                    'available_quantity' => $product->available_quantity
                ];
            });

        return response()->json([
            'results' => $products,
            'pagination' => [
                'more' => ($page * $perPage) < $total
            ],
            'total_count' => $total
        ]);
    }

    /**
     * Lấy thông tin chi tiết sản phẩm
     */
    public function getProductDetails(Request $request)
    {
        $productId = $request->product_id;
        $warehouseId = $request->warehouse_id;

        if (!$productId || !$warehouseId) {
            return response()->json([]);
        }

        $product = Product::with(['inventoryItems' => function ($query) use ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }])->find($productId);

        if (!$product) {
            return response()->json([]);
        }

        return response()->json([
            'product' => $product
        ]);
    }
}
