<?php

namespace App\Http\Controllers\Pages;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductSerial;
use App\Models\WarehouseTransfer;
use App\Models\WarehouseTransferItem;
use App\Models\WarehouseTransferSerial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class WarehouseTransferSerialController extends Controller
{
    /**
     * Hiển thị trang quét IMEI
     */
    public function showScanImei($id)
    {
        // Kiểm tra quyền truy cập
        $this->authorize('view', WarehouseTransfer::class);

        // Lấy thông tin phiếu chuyển kho
        $transfer = WarehouseTransfer::with([
            'items.product',
            'items.serials',
            'sourceWarehouse',
            'destinationWarehouse',
            'transitWarehouse',
        ])->findOrFail($id);

        // Kiểm tra trạng thái phiếu chuyển kho
        if ($transfer->status !== WarehouseTransfer::STATUS_APPROVED && $transfer->status !== WarehouseTransfer::STATUS_DRAFT) {
            return redirect()->route('warehouses.transfers.show', $id)
                ->with('error', 'Chỉ có thể quét IMEI cho phiếu chuyển kho ở trạng thái nháp hoặc đã được duyệt');
        }

        // Kiểm tra xem có sản phẩm nào quản lý bằng IMEI không
        $hasSerialItems = $transfer->has_serial_items;

        if (!$hasSerialItems) {
            return redirect()->route('warehouses.transfers.show', $id)
                ->with('error', 'Không có sản phẩm nào quản lý bằng IMEI trong phiếu chuyển kho này');
        }

        // Lấy danh sách sản phẩm quản lý bằng IMEI
        $serialItems = $transfer->items()->whereHas('product', function ($query) {
            $query->where('inventory_tracking_type', 'serial');
        })->with('product')->get();

        return view('content.pages.warehouses.transfers.scan-imei', compact('transfer', 'serialItems'));
    }

    /**
     * Lấy danh sách sản phẩm quản lý bằng IMEI
     */
    public function getSerialItems($id)
    {
        // Kiểm tra quyền truy cập
        $this->authorize('view', WarehouseTransfer::class);

        // Lấy thông tin phiếu chuyển kho
        $transfer = WarehouseTransfer::findOrFail($id);

        // Kiểm tra trạng thái phiếu chuyển kho
        if ($transfer->status !== WarehouseTransfer::STATUS_APPROVED && $transfer->status !== WarehouseTransfer::STATUS_DRAFT) {
            return response()->json([
                'code' => 400,
                'text' => 'Chỉ có thể quét IMEI cho phiếu chuyển kho ở trạng thái nháp hoặc đã được duyệt'
            ], 400);
        }

        // Lấy danh sách sản phẩm quản lý bằng IMEI
        $serialItems = $transfer->items()
            ->whereHas('product', function ($query) {
                $query->where('inventory_tracking_type', 'serial');
            })
            ->with(['product', 'serials'])
            ->withCount(['serials as scanned_serials_count'])
            ->get();

        return response()->json([
            'code' => 200,
            'data' => $serialItems
        ]);
    }

    /**
     * Lấy danh sách IMEI đã quét
     */
    public function getScannedImeis($id)
    {
        // Kiểm tra quyền truy cập
        $this->authorize('view', WarehouseTransfer::class);

        // Lấy thông tin phiếu chuyển kho
        $transfer = WarehouseTransfer::findOrFail($id);

        // Lấy danh sách IMEI đã quét
        $scannedImeis = WarehouseTransferSerial::where('warehouse_transfer_id', $id)
            ->with(['product', 'warehouseTransferItem'])
            ->get();

        return response()->json([
            'code' => 200,
            'data' => $scannedImeis
        ]);
    }

    /**
     * Lấy danh sách IMEI có sẵn trong kho nguồn
     */
    public function getAvailableImeis(Request $request, $id)
    {
        // Kiểm tra quyền truy cập
        $this->authorize('view', WarehouseTransfer::class);

        // Validate dữ liệu đầu vào
        $validator = Validator::make($request->all(), [
            'item_id' => 'required|exists:warehouse_transfer_items,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'errors' => $validator->errors()
            ], 422);
        }

        // Lấy thông tin phiếu chuyển kho
        $transfer = WarehouseTransfer::findOrFail($id);

        // Kiểm tra trạng thái phiếu chuyển kho
        if ($transfer->status !== WarehouseTransfer::STATUS_APPROVED && $transfer->status !== WarehouseTransfer::STATUS_DRAFT) {
            return response()->json([
                'code' => 400,
                'text' => 'Chỉ có thể quét IMEI cho phiếu chuyển kho ở trạng thái nháp hoặc đã được duyệt'
            ], 400);
        }

        // Lấy thông tin chi tiết phiếu chuyển kho
        $item = WarehouseTransferItem::where('warehouse_transfer_id', $id)
            ->where('id', $request->item_id)
            ->with('product')
            ->firstOrFail();

        // Kiểm tra xem sản phẩm có quản lý bằng IMEI không
        if ($item->product->inventory_tracking_type !== 'serial') {
            return response()->json([
                'code' => 400,
                'text' => 'Sản phẩm này không quản lý bằng IMEI'
            ], 400);
        }

        // Lấy danh sách IMEI đã quét cho sản phẩm này
        $scannedSerialNumbers = WarehouseTransferSerial::where('warehouse_transfer_id', $id)
            ->where('warehouse_transfer_item_id', $item->id)
            ->pluck('serial_number')
            ->toArray();

        // Lấy danh sách IMEI có sẵn trong kho nguồn
        $availableImeis = ProductSerial::where('product_id', $item->product_id)
            ->where('warehouse_id', $transfer->source_warehouse_id)
            ->where('status', 'available')
            ->whereNotIn('serial_number', $scannedSerialNumbers)
            ->with('warehouseArea')
            ->get();

        return response()->json([
            'code' => 200,
            'data' => $availableImeis
        ]);
    }

    /**
     * Quét IMEI
     */
    public function scanImei(Request $request, $id)
    {
        // Kiểm tra quyền truy cập
        $this->authorize('update', WarehouseTransfer::class);

        // Validate dữ liệu đầu vào
        $validator = Validator::make($request->all(), [
            'serial_number' => 'required|string',
            'item_id' => 'required|exists:warehouse_transfer_items,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'errors' => $validator->errors()
            ], 422);
        }

        // Lấy thông tin phiếu chuyển kho
        $transfer = WarehouseTransfer::findOrFail($id);

        // Kiểm tra trạng thái phiếu chuyển kho
        if ($transfer->status !== WarehouseTransfer::STATUS_APPROVED && $transfer->status !== WarehouseTransfer::STATUS_DRAFT) {
            return response()->json([
                'code' => 400,
                'text' => 'Chỉ có thể quét IMEI cho phiếu chuyển kho ở trạng thái nháp hoặc đã được duyệt'
            ], 400);
        }

        // Lấy thông tin chi tiết phiếu chuyển kho
        $item = WarehouseTransferItem::where('warehouse_transfer_id', $id)
            ->where('id', $request->item_id)
            ->with('product')
            ->firstOrFail();

        // Kiểm tra xem sản phẩm có quản lý bằng IMEI không
        if ($item->product->inventory_tracking_type !== 'serial') {
            return response()->json([
                'code' => 400,
                'text' => 'Sản phẩm này không quản lý bằng IMEI'
            ], 400);
        }

        // Kiểm tra xem đã quét đủ số lượng IMEI chưa
        $scannedCount = WarehouseTransferSerial::where('warehouse_transfer_id', $id)
            ->where('warehouse_transfer_item_id', $item->id)
            ->count();

        if ($scannedCount >= $item->quantity) {
            return response()->json([
                'code' => 400,
                'text' => 'Đã quét đủ số lượng IMEI cho sản phẩm này'
            ], 400);
        }

        // Kiểm tra xem IMEI đã được quét chưa
        $existingSerial = WarehouseTransferSerial::where('warehouse_transfer_id', $id)
            ->where('serial_number', $request->serial_number)
            ->first();

        if ($existingSerial) {
            return response()->json([
                'code' => 400,
                'text' => 'IMEI này đã được quét'
            ], 400);
        }

        // Kiểm tra xem IMEI có tồn tại trong kho nguồn không
        $productSerial = ProductSerial::where('serial_number', $request->serial_number)
            ->where('product_id', $item->product_id)
            ->where('warehouse_id', $transfer->source_warehouse_id)
            ->where('status', 'available')
            ->first();

        if (!$productSerial) {
            return response()->json([
                'code' => 400,
                'text' => 'IMEI không tồn tại hoặc không có sẵn trong kho nguồn'
            ], 400);
        }

        try {
            DB::beginTransaction();

            // Tạo bản ghi mới
            $transferSerial = new WarehouseTransferSerial();
            $transferSerial->warehouse_transfer_id = $id;
            $transferSerial->warehouse_transfer_item_id = $item->id;
            $transferSerial->product_id = $item->product_id;
            $transferSerial->product_serial_id = $productSerial->id;
            $transferSerial->serial_number = $request->serial_number;
            $transferSerial->status = WarehouseTransferSerial::STATUS_PENDING;
            $transferSerial->save();

            DB::commit();

            return response()->json([
                'code' => 200,
                'text' => 'Quét IMEI thành công',
                'data' => $transferSerial
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi quét IMEI: ' . $e->getMessage());

            return response()->json([
                'code' => 500,
                'text' => 'Đã xảy ra lỗi khi quét IMEI'
            ], 500);
        }
    }

    /**
     * Xóa IMEI đã quét
     */
    public function deleteImei(Request $request, $id, $serialId)
    {
        // Kiểm tra quyền truy cập
        $this->authorize('update', WarehouseTransfer::class);

        // Lấy thông tin phiếu chuyển kho
        $transfer = WarehouseTransfer::findOrFail($id);

        // Kiểm tra trạng thái phiếu chuyển kho
        if ($transfer->status !== WarehouseTransfer::STATUS_APPROVED && $transfer->status !== WarehouseTransfer::STATUS_DRAFT) {
            return response()->json([
                'code' => 400,
                'text' => 'Chỉ có thể xóa IMEI cho phiếu chuyển kho ở trạng thái nháp hoặc đã được duyệt'
            ], 400);
        }

        // Lấy thông tin IMEI
        $transferSerial = WarehouseTransferSerial::where('warehouse_transfer_id', $id)
            ->where('id', $serialId)
            ->firstOrFail();

        try {
            DB::beginTransaction();

            // Xóa bản ghi
            $transferSerial->delete();

            DB::commit();

            return response()->json([
                'code' => 200,
                'text' => 'Xóa IMEI thành công'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi xóa IMEI: ' . $e->getMessage());

            return response()->json([
                'code' => 500,
                'text' => 'Đã xảy ra lỗi khi xóa IMEI'
            ], 500);
        }
    }

    /**
     * Hoàn thành quét IMEI
     */
    public function completeImeiScan(Request $request, $id)
    {
        // Kiểm tra quyền truy cập
        $this->authorize('update', WarehouseTransfer::class);

        // Lấy thông tin phiếu chuyển kho
        $transfer = WarehouseTransfer::with(['items.product', 'items.serials'])->findOrFail($id);

        // Kiểm tra trạng thái phiếu chuyển kho
        if ($transfer->status !== WarehouseTransfer::STATUS_APPROVED && $transfer->status !== WarehouseTransfer::STATUS_DRAFT) {
            return response()->json([
                'code' => 400,
                'text' => 'Chỉ có thể hoàn thành quét IMEI cho phiếu chuyển kho ở trạng thái nháp hoặc đã được duyệt'
            ], 400);
        }

        // Kiểm tra xem đã quét đủ IMEI cho tất cả sản phẩm chưa
        if (!$transfer->is_fully_scanned) {
            return response()->json([
                'code' => 400,
                'text' => 'Vui lòng quét đủ IMEI cho tất cả sản phẩm'
            ], 400);
        }

        try {
            DB::beginTransaction();

            // Nếu phiếu ở trạng thái nháp, gửi duyệt trước
            if ($transfer->status === WarehouseTransfer::STATUS_DRAFT) {
                $transfer->status = WarehouseTransfer::STATUS_PENDING;
                $transfer->save();

                DB::commit();

                return response()->json([
                    'code' => 200,
                    'text' => 'Quét IMEI thành công. Phiếu chuyển kho đã được gửi duyệt.',
                    'redirect' => route('warehouses.transfers.show', $id)
                ]);
            }

            // Nếu phiếu đã được duyệt, chuyển sang trạng thái đang vận chuyển
            if ($transfer->status === WarehouseTransfer::STATUS_APPROVED) {
                // Cập nhật trạng thái phiếu chuyển kho
                $transfer->status = WarehouseTransfer::STATUS_SHIPPING;
                $transfer->save();

                // Cập nhật trạng thái các IMEI
                WarehouseTransferSerial::where('warehouse_transfer_id', $id)
                    ->update(['status' => WarehouseTransferSerial::STATUS_TRANSFERRED]);

                // Cập nhật trạng thái các IMEI trong kho
                $serials = WarehouseTransferSerial::where('warehouse_transfer_id', $id)->get();
                foreach ($serials as $serial) {
                    ProductSerial::where('id', $serial->product_serial_id)
                        ->update([
                            'status' => 'reserved',
                            'warehouse_id' => $transfer->transit_warehouse_id
                        ]);
                }
            }

            DB::commit();

            return response()->json([
                'code' => 200,
                'text' => 'Hoàn thành quét IMEI thành công',
                'redirect' => route('warehouses.transfers.show', $id)
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi khi hoàn thành quét IMEI: ' . $e->getMessage());

            return response()->json([
                'code' => 500,
                'text' => 'Đã xảy ra lỗi khi hoàn thành quét IMEI'
            ], 500);
        }
    }

    /**
     * Lấy danh sách IMEI cho một sản phẩm cụ thể
     */
    public function getItemSerials($id, $itemId)
    {
        try {
            // Kiểm tra quyền truy cập
            $this->authorize('view', WarehouseTransfer::class);

            // Lấy thông tin phiếu chuyển kho
            $transfer = WarehouseTransfer::findOrFail($id);

            // Lấy thông tin sản phẩm
            $item = WarehouseTransferItem::where('warehouse_transfer_id', $id)
                ->where('id', $itemId)
                ->firstOrFail();

            // Lấy danh sách IMEI
            $serials = WarehouseTransferSerial::where('warehouse_transfer_item_id', $itemId)
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'code' => 200,
                'data' => $serials
            ]);
        } catch (\Exception $e) {
            Log::error('Lỗi khi lấy danh sách IMEI: ' . $e->getMessage());

            return response()->json([
                'code' => 500,
                'text' => 'Đã xảy ra lỗi khi lấy danh sách IMEI'
            ], 500);
        }
    }
}
