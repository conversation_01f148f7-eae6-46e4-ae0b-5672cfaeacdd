<?php

namespace App\Http\Controllers\Pages;

use App\Contracts\WarehouseInterface;
use App\Http\Controllers\Controller;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WarehouseReportController extends Controller
{
    protected $warehouses;

    public function __construct(WarehouseInterface $warehouses)
    {
        $this->warehouses = $warehouses;
    }

    /**
     * Display warehouse reports
     */
    public function index(Request $request)
    {
        $warehouses = $this->warehouses->getAllWarehouses();
        $reportType = $request->report_type ?? 'stock';

        // Get report data based on type
        $data = $this->getReportData($request);

        return view('content.pages.warehouses.reports.index', compact('warehouses', 'reportType', 'data'));
    }

    /**
     * Export warehouse report
     */
    public function export(Request $request)
    {
        $reportType = $request->report_type ?? 'stock';
        $data = $this->getReportData($request);

        // Generate report file name
        $fileName = 'warehouse_report_' . $reportType . '_' . date('YmdHis') . '.csv';

        // Generate CSV content
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $callback = function () use ($data, $reportType) {
            $file = fopen('php://output', 'w');

            // Add headers based on report type
            switch ($reportType) {
                case 'stock':
                    fputcsv($file, ['Mã sản phẩm', 'Tên sản phẩm', 'Kho hàng', 'Số lượng tồn', 'Số lượng khả dụng', 'Số lượng đặt trước', 'Mức tồn tối thiểu', 'Mức tồn tối đa', 'Trạng thái']);
                    break;
                case 'transactions':
                    fputcsv($file, ['Mã giao dịch', 'Loại giao dịch', 'Sản phẩm', 'Kho hàng', 'Số lượng', 'Người tạo', 'Ngày tạo', 'Ghi chú']);
                    break;
                case 'low_stock':
                    fputcsv($file, ['Mã sản phẩm', 'Tên sản phẩm', 'Kho hàng', 'Số lượng tồn', 'Mức tồn tối thiểu', 'Cần nhập thêm']);
                    break;
                case 'expiry':
                    fputcsv($file, ['Mã sản phẩm', 'Tên sản phẩm', 'Kho hàng', 'Số lô', 'Số lượng', 'Ngày hết hạn', 'Ngày còn lại']);
                    break;
            }

            // Add data rows
            foreach ($data as $row) {
                switch ($reportType) {
                    case 'stock':
                        fputcsv($file, [
                            $row->product->code,
                            $row->product->name,
                            $row->warehouse->name,
                            $row->quantity,
                            $row->available_quantity,
                            $row->reserved_quantity ?? 0,
                            $row->min_stock_level ?? 'N/A',
                            $row->max_stock_level ?? 'N/A',
                            $row->stock_status_text
                        ]);
                        break;
                    case 'transactions':
                        fputcsv($file, [
                            $row->reference_number,
                            $row->type_text,
                            $row->product->name,
                            $row->warehouse->name,
                            $row->quantity,
                            $row->createdBy->name ?? 'N/A',
                            $row->created_at->format('d/m/Y H:i'),
                            $row->notes ?? 'N/A'
                        ]);
                        break;
                    case 'low_stock':
                        fputcsv($file, [
                            $row->product->code,
                            $row->product->name,
                            $row->warehouse->name,
                            $row->quantity,
                            $row->min_stock_level,
                            $row->min_stock_level - $row->quantity
                        ]);
                        break;
                    case 'expiry':
                        $daysLeft = $row->expiry_date->diffInDays(now());
                        fputcsv($file, [
                            $row->product->code,
                            $row->product->name,
                            $row->warehouse->name,
                            $row->batch_number ?? 'N/A',
                            $row->quantity,
                            $row->expiry_date->format('d/m/Y'),
                            $daysLeft . ' ngày'
                        ]);
                        break;
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get report data based on type and filters
     */
    private function getReportData(Request $request)
    {
        $reportType = $request->report_type ?? 'stock';
        $warehouseId = $request->warehouse_id;
        $dateFrom = $request->date_from ? Carbon::parse($request->date_from)->startOfDay() : null;
        $dateTo = $request->date_to ? Carbon::parse($request->date_to)->endOfDay() : null;

        switch ($reportType) {
            case 'stock':
                return $this->getStockReport($warehouseId);
            case 'transactions':
                return $this->getTransactionsReport($warehouseId, $dateFrom, $dateTo);
            case 'low_stock':
                return $this->getLowStockReport($warehouseId);
            case 'expiry':
                return $this->getExpiryReport($warehouseId, $dateFrom, $dateTo);
            default:
                return collect();
        }
    }

    /**
     * Get stock report
     */
    private function getStockReport($warehouseId)
    {
        $query = InventoryItem::with(['product', 'warehouse'])
            ->where('quantity', '>', 0);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        return $query->orderBy('updated_at', 'desc')->get();
    }

    /**
     * Get transactions report
     */
    private function getTransactionsReport($warehouseId, $dateFrom, $dateTo)
    {
        $query = InventoryTransaction::with(['product', 'warehouse', 'createdBy']);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        if ($dateFrom) {
            $query->where('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->where('created_at', '<=', $dateTo);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get low stock report
     */
    private function getLowStockReport($warehouseId)
    {
        $query = InventoryItem::with(['product', 'warehouse'])
            ->whereNotNull('min_stock_level')
            ->whereRaw('quantity <= min_stock_level');

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        return $query->orderBy('quantity', 'asc')->get();
    }

    /**
     * Get expiry report
     */
    private function getExpiryReport($warehouseId, $dateFrom, $dateTo)
    {
        $query = InventoryTransaction::with(['product', 'warehouse'])
            ->whereNotNull('expiry_date')
            ->where('quantity', '>', 0);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        // Default to next 90 days if no date range provided
        if (!$dateFrom && !$dateTo) {
            $dateFrom = now();
            $dateTo = now()->addDays(90);
        }

        if ($dateFrom) {
            $query->where('expiry_date', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->where('expiry_date', '<=', $dateTo);
        }

        // Group by product, warehouse, batch_number, expiry_date and sum quantities
        $results = $query->select(
            'product_id',
            'warehouse_id',
            'batch_number',
            'expiry_date',
            DB::raw('SUM(quantity) as quantity')
        )
        ->groupBy('product_id', 'warehouse_id', 'batch_number', 'expiry_date')
        ->orderBy('expiry_date', 'asc')
        ->get();

        return $results;
    }
}
