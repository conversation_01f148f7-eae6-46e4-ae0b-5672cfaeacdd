<?php

namespace App\Http\Controllers\Pages;

use App\Contracts\GoodReceiptInterface;
use App\Contracts\WarehouseInterface;
use App\Http\Controllers\Controller;
use App\Models\GoodReceipt;
use App\Models\PurchaseOrder;
use App\Models\ProductSerial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;

class GoodReceiptController extends Controller
{
    protected $goodReceiptRepository;
    protected $warehouses;

    public function __construct(GoodReceiptInterface $goodReceiptRepository, WarehouseInterface $warehouses)
    {
        $this->goodReceiptRepository = $goodReceiptRepository;
        $this->warehouses = $warehouses;
    }

    /**
     * Display a listing of the good receipts.
     */
    public function index()
    {
        $warehouses = $this->warehouses->getAllWarehouses();

        return view('content.pages.warehouses.good-receipts.index', compact('warehouses'));
    }

    /**
     * Display the specified good receipt.
     */
    public function show($id)
    {
        try {
            $goodReceipt = $this->goodReceiptRepository->findById($id);

            return view('content.pages.warehouses.good-receipts.show', compact('goodReceipt'));
        } catch (\Exception $e) {
            return redirect()->route('warehouses.good-receipts.index')
                ->with('error', 'Không tìm thấy phiếu nhập kho.');
        }
    }

    /**
     * Get good receipts for datatable.
     */
    public function getDatatable(Request $request)
    {
        try {
            return $this->goodReceiptRepository->getGoodReceiptsForDatatable($request);
        } catch (\Exception $e) {
            // Log lỗi và trả về dữ liệu trống
            \Log::error('Error in getDatatable: ' . $e->getMessage());
            return [
                'draw' => intval($request->draw),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => []
            ];
        }
    }

    /**
     * Get good receipt items.
     */
    public function getItems($id)
    {
        try {
            $items = $this->goodReceiptRepository->getGoodReceiptItems($id);

            return response()->json([
                'success' => true,
                'data' => $items
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lấy danh sách sản phẩm: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Cancel good receipt.
     */
    public function cancel(Request $request, $id)
    {
        try {
            $request->validate([
                'reason' => 'required|string|max:255',
            ]);

            $goodReceipt = $this->goodReceiptRepository->cancel($id, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu nhập kho đã được hủy thành công.',
                'redirect' => route('warehouses.good-receipts.show', $goodReceipt->id)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi hủy phiếu nhập kho: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Print good receipt.
     */
    public function print($id)
    {
        try {
            $goodReceipt = $this->goodReceiptRepository->findById($id);

            $pdf = PDF::loadView('content.pages.warehouses.good-receipts.print', compact('goodReceipt'));

            return $pdf->stream('phieu-nhap-kho-' . $goodReceipt->code . '.pdf');
        } catch (\Exception $e) {
            return redirect()->route('warehouses.good-receipts.index')
                ->with('error', 'Không tìm thấy phiếu nhập kho.');
        }
    }

    /**
     * Get serials for good receipt item.
     */
    public function getSerials($id, $itemId)
    {
        try {
            $goodReceipt = $this->goodReceiptRepository->findById($id);
            $item = $goodReceipt->items()->findOrFail($itemId);
            $serials = $item->serials;

            return response()->json([
                'success' => true,
                'data' => $serials
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lấy danh sách serial: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update invoice information.
     */
    public function updateInvoice(Request $request, $id)
    {
        try {
            $request->validate([
                'invoice_number' => 'required|string|max:50',
                'invoice_series' => 'nullable|string|max:50',
                'invoice_template' => 'nullable|string|max:50',
                'invoice_date' => 'required|date',
                'invoice_files' => 'nullable|array',
                'invoice_files.*' => 'file|max:10240', // Max 10MB
            ]);

            $goodReceipt = $this->goodReceiptRepository->findById($id);

            // Kiểm tra trạng thái phiếu nhập kho
            if (!in_array($goodReceipt->status, [GoodReceipt::STATUS_DRAFT, GoodReceipt::STATUS_PENDING])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không thể cập nhật thông tin hóa đơn cho phiếu nhập kho này.'
                ], 400);
            }

            // Cập nhật thông tin hóa đơn
            $updateData = [
                'invoice_number' => $request->invoice_number,
                'invoice_series' => $request->invoice_series,
                'invoice_template' => $request->invoice_template,
                'invoice_date' => $request->invoice_date,
                'has_documents' => true,
            ];

            $goodReceipt = $this->goodReceiptRepository->update($id, $updateData);

            // Xử lý các file đính kèm nếu có
            if ($request->hasFile('invoice_files')) {
                $files = $request->file('invoice_files');
                $uploadedFiles = [];

                foreach ($files as $file) {
                    $result = $this->goodReceiptRepository->uploadAttachment($id, [
                        'file' => $file,
                        'description' => 'Hóa đơn: ' . $request->invoice_number,
                        'uploaded_by' => Auth::id()
                    ]);

                    if ($result['success']) {
                        $uploadedFiles[] = $result['data'];
                    }
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Cập nhật thông tin hóa đơn thành công.',
                    'data' => $goodReceipt,
                    'files' => $uploadedFiles
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Cập nhật thông tin hóa đơn thành công.',
                'data' => $goodReceipt
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật thông tin hóa đơn: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save good receipt as draft.
     */
    public function saveDraft(Request $request, $id)
    {
        try {
            $goodReceipt = $this->goodReceiptRepository->findById($id);

            // Kiểm tra trạng thái phiếu nhập kho
            if ($goodReceipt->status !== GoodReceipt::STATUS_DRAFT) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chỉ có thể lưu phiếu nhập kho ở trạng thái nháp.'
                ], 400);
            }

            // Lấy dữ liệu từ request
            $importData = $request->input('import_data', []);

            // Lưu dữ liệu vào database
            $result = $this->goodReceiptRepository->saveImportData($id, $importData);

            // Lấy danh sách các item đã được lưu thành công
            $savedItems = array_keys($result['processed_items'] ?? []);

            return response()->json([
                'success' => true,
                'message' => 'Đã lưu dữ liệu phiếu nhập kho thành công.',
                'data' => $result,
                'saved_items' => $savedItems
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lưu dữ liệu phiếu nhập kho: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get import data for good receipt.
     */
    public function getImportData($id)
    {
        try {
            $goodReceipt = $this->goodReceiptRepository->findById($id);

            // Lấy danh sách các item của phiếu nhập kho
            $items = $goodReceipt->items;

            $result = [
                'serials' => [],
                'batches' => []
            ];

            foreach ($items as $item) {
                // Lấy danh sách serial của item
                if ($item->product->inventory_tracking_type === 'serial') {
                    $serials = $item->serials->pluck('serial_number')->toArray();
                    if (count($serials) > 0) {
                        $result['serials'][] = [
                            'item_id' => $item->id,
                            'product_id' => $item->product_id,
                            'serials' => $serials,
                            'warehouse_area_id' => $item->warehouse_area_id
                        ];
                    }
                }

                // Lấy danh sách batch của item
                if ($item->product->inventory_tracking_type === 'batch') {
                    $batches = $item->batches;
                    if ($batches->count() > 0) {
                        $batchesData = [];
                        foreach ($batches as $batch) {
                            $batchesData[] = [
                                'batch_number' => $batch->batch_number,
                                'expiry_date' => $batch->expiry_date,
                                'manufacturing_date' => $batch->manufacturing_date,
                                'quantity' => $batch->quantity
                            ];
                        }

                        $result['batches'][] = [
                            'item_id' => $item->id,
                            'product_id' => $item->product_id,
                            'batches' => $batchesData,
                            'warehouse_area_id' => $item->warehouse_area_id
                        ];
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lấy dữ liệu nhập kho: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Submit good receipt for approval.
     */
    public function submit($id)
    {
        try {
            $goodReceipt = $this->goodReceiptRepository->submit($id);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu nhập kho đã được gửi yêu cầu duyệt.',
                'redirect' => route('warehouses.good-receipts.show', $goodReceipt->id)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi gửi yêu cầu duyệt phiếu nhập kho: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve good receipt.
     */
    public function approve($id)
    {
        try {
            $goodReceipt = $this->goodReceiptRepository->approve($id, Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'Phiếu nhập kho đã được duyệt và cập nhật tồn kho.',
                'redirect' => route('warehouses.good-receipts.show', $goodReceipt->id)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi duyệt phiếu nhập kho: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Scan IMEI for good receipt item.
     */
    public function scanImei(Request $request, $id, $itemId)
    {
        try {
            $request->validate([
                'serial_number' => 'required|string|max:50',
            ]);

            $goodReceipt = $this->goodReceiptRepository->findById($id);

            // Kiểm tra trạng thái phiếu nhập kho
            if ($goodReceipt->status !== GoodReceipt::STATUS_DRAFT) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chỉ có thể quét IMEI cho phiếu nhập kho ở trạng thái nháp.'
                ], 400);
            }

            $item = $goodReceipt->items()->findOrFail($itemId);

            // Kiểm tra xem sản phẩm có quản lý bằng IMEI không
            if ($item->product->inventory_tracking_type !== 'serial') {
                return response()->json([
                    'success' => false,
                    'message' => 'Sản phẩm này không quản lý bằng IMEI.'
                ], 400);
            }

            // Kiểm tra IMEI đã tồn tại trong hệ thống chưa
            $existingSerial = \App\Models\ProductSerial::where('serial_number', $request->serial_number)
                ->where('status', 'in_stock')
                ->first();

            if ($existingSerial) {
                return response()->json([
                    'success' => false,
                    'message' => 'IMEI này đã tồn tại trong hệ thống.'
                ], 400);
            }

            // Kiểm tra IMEI đã được quét cho phiếu nhập kho này chưa
            $existingItemSerial = \App\Models\ProductSerial::where('serial_number', $request->serial_number)
                ->where('good_receipt_item_id', $itemId)
                ->first();

            if ($existingItemSerial) {
                return response()->json([
                    'success' => false,
                    'message' => 'IMEI này đã được quét cho phiếu nhập kho này.'
                ], 400);
            }

            // Tạo mới serial
            $productSerial = new \App\Models\ProductSerial([
                'product_id' => $item->product_id,
                'serial_number' => $request->serial_number,
                'warehouse_id' => $goodReceipt->warehouse_id,
                'warehouse_area_id' => $item->warehouse_area_id,
                'purchase_order_item_id' => $item->purchase_order_item_id,
                'good_receipt_item_id' => $item->id,
                'status' => 'pending', // Chỉ chuyển thành in_stock sau khi duyệt
                'notes' => 'Nhập kho từ phiếu nhập hàng',
            ]);
            $productSerial->save();

            // Lấy danh sách IMEI đã quét
            $scannedSerials = \App\Models\ProductSerial::where('good_receipt_item_id', $itemId)->get();

            return response()->json([
                'success' => true,
                'message' => 'Quét IMEI thành công.',
                'data' => [
                    'serial' => $productSerial,
                    'scanned_count' => $scannedSerials->count(),
                    'scanned_serials' => $scannedSerials
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi quét IMEI: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if serial exists.
     */
    public function checkSerialExists(Request $request)
    {
        try {
            $request->validate([
                'serial_number' => 'required|string|max:255',
            ]);

            $exists = ProductSerial::where('serial_number', $request->serial_number)->exists();

            return response()->json([
                'success' => true,
                'exists' => $exists
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi kiểm tra IMEI/Serial: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if multiple serials exist.
     */
    public function checkSerialsExist(Request $request)
    {
        try {
            $request->validate([
                'serials' => 'required|array',
                'serials.*' => 'required|string|max:255',
            ]);

            $serials = $request->serials;
            $existingSerials = ProductSerial::whereIn('serial_number', $serials)
                ->pluck('serial_number')
                ->toArray();

            return response()->json([
                'success' => true,
                'existing_serials' => $existingSerials
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi kiểm tra danh sách IMEI/Serial: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get attachments for good receipt.
     */
    public function getAttachments($id)
    {
        try {
            $attachments = $this->goodReceiptRepository->getGoodReceiptAttachments($id);

            return response()->json([
                'success' => true,
                'data' => $attachments
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lấy danh sách file đính kèm: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload attachment for good receipt.
     */
    public function uploadAttachment(Request $request, $id)
    {
        try {
            $request->validate([
                'file' => 'required|file|max:10240', // Max 10MB
                'description' => 'nullable|string|max:255',
            ]);

            $result = $this->goodReceiptRepository->uploadAttachment($id, [
                'file' => $request->file('file'),
                'description' => $request->description,
                'uploaded_by' => Auth::id()
            ]);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['data']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải lên file đính kèm: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete attachment for good receipt.
     */
    public function deleteAttachment($id, $attachmentId)
    {
        try {
            $result = $this->goodReceiptRepository->deleteAttachment($id, $attachmentId);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Xóa file đính kèm thành công'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Không thể xóa file đính kèm này'
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa file đính kèm: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get batches for a good receipt item.
     */
    public function getBatches($id, $itemId)
    {
        try {
            $goodReceipt = $this->goodReceiptRepository->findById($id);
            $item = $goodReceipt->items()->findOrFail($itemId);

            // Lấy danh sách batch từ bảng product_batches
            $batches = \App\Models\ProductBatch::where('good_receipt_item_id', $itemId)
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'batches' => $batches
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lấy danh sách batch: ' . $e->getMessage()
            ], 500);
        }
    }
}
