<?php

namespace App\Http\Controllers\Pages;

use App\Contracts\TransferReceiptInterface;
use App\Contracts\TransferOrderInterface;
use App\Http\Controllers\Controller;
use App\Models\Warehouse;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class TransferReceiptController extends Controller
{
    protected $transferReceiptRepository;
    protected $transferOrderRepository;

    public function __construct(
        TransferReceiptInterface $transferReceiptRepository,
        TransferOrderInterface $transferOrderRepository
    ) {
        $this->transferReceiptRepository = $transferReceiptRepository;
        $this->transferOrderRepository = $transferOrderRepository;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $pageConfigs = ['myLayout' => 'vertical'];

        $warehouses = Warehouse::active()->get();

        return view('content.pages.transfers.transfer-receipts.index', compact('pageConfigs', 'warehouses'));
    }

    /**
     * Get data for DataTable
     */
    public function datatable(Request $request)
    {
        try {
            $params = $request->all();
            $result = $this->transferReceiptRepository->getForDatatable($params);

            $data = [];
            foreach ($result['data'] as $item) {
                $statusBadge = match($item->status) {
                    'draft' => '<span class="badge bg-label-secondary">Nháp</span>',
                    'pending' => '<span class="badge bg-label-warning">Chờ duyệt</span>',
                    'approved' => '<span class="badge bg-label-success">Đã duyệt</span>',
                    'rejected' => '<span class="badge bg-label-danger">Từ chối</span>',
                    'cancelled' => '<span class="badge bg-label-dark">Đã hủy</span>',
                    default => '<span class="badge bg-label-secondary">Không xác định</span>',
                };

                $actions = '<div class="dropdown">
                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                        <i class="ri-more-2-line"></i>
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="' . route('transfer-receipts.show', $item->id) . '">
                            <i class="ri-eye-line me-1"></i> Xem chi tiết
                        </a>';

                if (in_array($item->status, ['draft', 'pending'])) {
                    $actions .= '<a class="dropdown-item" href="' . route('transfer-receipts.edit', $item->id) . '">
                        <i class="ri-pencil-line me-1"></i> Chỉnh sửa
                    </a>';
                }

                if ($item->status === 'draft') {
                    $actions .= '<a class="dropdown-item submit-approval-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-send-plane-line me-1"></i> Gửi duyệt
                    </a>';
                }

                if ($item->status === 'pending') {
                    $actions .= '<a class="dropdown-item approve-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-check-line me-1"></i> Duyệt
                    </a>
                    <a class="dropdown-item reject-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-close-line me-1"></i> Từ chối
                    </a>';
                }

                if (in_array($item->status, ['draft', 'pending'])) {
                    $actions .= '<div class="dropdown-divider"></div>
                    <a class="dropdown-item text-danger cancel-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-delete-bin-7-line me-1"></i> Hủy
                    </a>';
                }

                $actions .= '</div></div>';

                $data[] = [
                    'id' => $item->id,
                    'code' => $item->code,
                    'transfer_order_code' => $item->transfer_order_code,
                    'from_warehouse' => $item->from_warehouse_name,
                    'to_warehouse' => $item->to_warehouse_name,
                    'transit_warehouse' => $item->transit_warehouse_name,
                    'status' => $statusBadge,
                    'created_by' => $item->created_by_name,
                    'created_at' => date('d/m/Y H:i', strtotime($item->created_at)),
                    'actions' => $actions
                ];
            }

            return response()->json([
                'draw' => intval($request->draw),
                'recordsTotal' => $result['recordsTotal'],
                'recordsFiltered' => $result['recordsFiltered'],
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('Error in transfer receipt datatable: ' . $e->getMessage());
            return response()->json(['error' => 'Có lỗi xảy ra khi tải dữ liệu'], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pageConfigs = ['myLayout' => 'vertical'];

        $warehouses = Warehouse::active()->get();
        $products = Product::where('is_active', true)->get();
        $approvedOrders = $this->transferOrderRepository->getApprovedWithoutTransferReceipt();

        return view('content.pages.transfers.transfer-receipts.create', compact('pageConfigs', 'warehouses', 'products', 'approvedOrders'));
    }

    /**
     * Create transfer receipt from approved transfer order
     */
    public function createFromOrder($orderId)
    {
        try {
            $pageConfigs = ['myLayout' => 'vertical'];

            $transferOrder = $this->transferOrderRepository->getById($orderId);

            if ($transferOrder->status !== 'approved') {
                return redirect()->route('transfer-receipts.index')->with('error', 'Chỉ có thể tạo phiếu nhận hàng từ phiếu chuyển hàng đã được duyệt.');
            }

            if ($transferOrder->transferReceipt) {
                return redirect()->route('transfer-receipts.show', $transferOrder->transferReceipt->id)->with('info', 'Phiếu nhận hàng đã được tạo từ phiếu chuyển hàng này.');
            }

            $warehouses = Warehouse::active()->get();

            return view('content.pages.transfers.transfer-receipts.create-from-order', compact('pageConfigs', 'transferOrder', 'warehouses'));
        } catch (\Exception $e) {
            Log::error('Error creating transfer receipt from order: ' . $e->getMessage());
            return redirect()->route('transfer-receipts.index')->with('error', 'Không tìm thấy phiếu chuyển hàng.');
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if ($request->has('transfer_order_id')) {
            // Creating from transfer order
            $request->validate([
                'transfer_order_id' => 'required|exists:transfer_orders,id',
                'notes' => 'nullable|string',
            ]);

            try {
                $data = $request->only(['notes']);
                $data['created_by'] = Auth::id();

                $transferReceipt = $this->transferReceiptRepository->createFromTransferOrder($request->transfer_order_id, $data);

                return response()->json([
                    'success' => true,
                    'message' => 'Phiếu nhận hàng đã được tạo thành công từ phiếu chuyển hàng.',
                    'redirect' => route('transfer-receipts.show', $transferReceipt->id)
                ]);
            } catch (\Exception $e) {
                Log::error('Error creating transfer receipt from order: ' . $e->getMessage());
                return response()->json([
                    'success' => false,
                    'message' => 'Có lỗi xảy ra khi tạo phiếu nhận hàng.'
                ], 500);
            }
        } else {
            // Creating manually
            $request->validate([
                'from_warehouse_id' => 'required|exists:warehouses,id',
                'to_warehouse_id' => 'required|exists:warehouses,id|different:from_warehouse_id',
                'transit_warehouse_id' => 'nullable|exists:warehouses,id',
                'notes' => 'nullable|string',
                'items' => 'required|array|min:1',
                'items.*.product_id' => 'required|exists:products,id',
                'items.*.quantity' => 'required|numeric|min:0.01',
                'items.*.notes' => 'nullable|string',
            ]);

            try {
                $data = $request->only(['from_warehouse_id', 'to_warehouse_id', 'transit_warehouse_id', 'notes']);
                $data['created_by'] = Auth::id();
                $data['items'] = $request->items;

                $transferReceipt = $this->transferReceiptRepository->create($data);

                return response()->json([
                    'success' => true,
                    'message' => 'Phiếu nhận hàng đã được tạo thành công.',
                    'redirect' => route('transfer-receipts.show', $transferReceipt->id)
                ]);
            } catch (\Exception $e) {
                Log::error('Error creating transfer receipt: ' . $e->getMessage());
                return response()->json([
                    'success' => false,
                    'message' => 'Có lỗi xảy ra khi tạo phiếu nhận hàng.'
                ], 500);
            }
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            $pageConfigs = ['myLayout' => 'vertical'];

            $transferReceipt = $this->transferReceiptRepository->getById($id);

            return view('content.pages.transfers.transfer-receipts.show', compact('pageConfigs', 'transferReceipt'));
        } catch (\Exception $e) {
            Log::error('Error showing transfer receipt: ' . $e->getMessage());
            return redirect()->route('transfer-receipts.index')->with('error', 'Không tìm thấy phiếu nhận hàng.');
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        try {
            $pageConfigs = ['myLayout' => 'vertical'];

            $transferReceipt = $this->transferReceiptRepository->getById($id);

            if (!$transferReceipt->canBeEdited()) {
                return redirect()->route('transfer-receipts.show', $id)->with('error', 'Phiếu nhận hàng không thể chỉnh sửa.');
            }

            $warehouses = Warehouse::active()->get();
            $products = Product::where('is_active', true)->get();

            return view('content.pages.transfers.transfer-receipts.edit', compact('pageConfigs', 'transferReceipt', 'warehouses', 'products'));
        } catch (\Exception $e) {
            Log::error('Error editing transfer receipt: ' . $e->getMessage());
            return redirect()->route('transfer-receipts.index')->with('error', 'Không tìm thấy phiếu nhận hàng.');
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'from_warehouse_id' => 'required|exists:warehouses,id',
            'to_warehouse_id' => 'required|exists:warehouses,id|different:from_warehouse_id',
            'transit_warehouse_id' => 'nullable|exists:warehouses,id',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.notes' => 'nullable|string',
        ]);

        try {
            $transferReceipt = $this->transferReceiptRepository->getById($id);

            if (!$transferReceipt->canBeEdited()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Phiếu nhận hàng không thể chỉnh sửa.'
                ], 422);
            }

            $data = $request->only(['from_warehouse_id', 'to_warehouse_id', 'transit_warehouse_id', 'notes']);
            $data['items'] = $request->items;

            $this->transferReceiptRepository->update($id, $data);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu nhận hàng đã được cập nhật thành công.',
                'redirect' => route('transfer-receipts.show', $id)
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating transfer receipt: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật phiếu nhận hàng.'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $transferReceipt = $this->transferReceiptRepository->getById($id);

            if (!$transferReceipt->canBeEdited()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Phiếu nhận hàng không thể xóa.'
                ], 422);
            }

            $this->transferReceiptRepository->delete($id);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu nhận hàng đã được xóa thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting transfer receipt: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa phiếu nhận hàng.'
            ], 500);
        }
    }

    /**
     * Submit transfer receipt for approval
     */
    public function submitForApproval(Request $request, $id)
    {
        try {
            $this->transferReceiptRepository->submitForApproval($id);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu nhận hàng đã được gửi duyệt thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error submitting transfer receipt for approval: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve transfer receipt
     */
    public function approve(Request $request, $id)
    {
        try {
            $this->transferReceiptRepository->approve($id, Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'Phiếu nhận hàng đã được duyệt thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error approving transfer receipt: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject transfer receipt
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        try {
            $this->transferReceiptRepository->reject($id, Auth::id(), $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu nhận hàng đã được từ chối.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error rejecting transfer receipt: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel transfer receipt
     */
    public function cancel(Request $request, $id)
    {
        try {
            $this->transferReceiptRepository->cancel($id);

            return response()->json([
                'success' => true,
                'message' => 'Phiếu nhận hàng đã được hủy.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error cancelling transfer receipt: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate IMEI availability for transfer receipt
     */
    public function validateImei(Request $request, $id)
    {
        $request->validate([
            'item_id' => 'required|exists:transfer_receipt_items,id',
            'imei' => 'required|string|max:255',
        ]);

        try {
            $result = $this->transferReceiptRepository->validateImeiAvailability(
                $id,
                $request->item_id,
                $request->imei
            );

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Error validating IMEI for transfer receipt: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Add IMEI to transfer receipt item
     */
    public function addImei(Request $request, $id)
    {
        $request->validate([
            'item_id' => 'required|exists:transfer_receipt_items,id',
            'imei' => 'required|string|max:255',
        ]);

        try {
            $imei = $this->transferReceiptRepository->addImei(
                $id,
                $request->item_id,
                $request->imei,
                Auth::id()
            );

            return response()->json([
                'success' => true,
                'message' => 'IMEI đã được thêm thành công.',
                'data' => $imei
            ]);
        } catch (\Exception $e) {
            Log::error('Error adding IMEI to transfer receipt: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Remove IMEI from transfer receipt item
     */
    public function removeImei(Request $request, $id, $imeiId)
    {
        try {
            $this->transferReceiptRepository->removeImei($id, $imeiId);

            return response()->json([
                'success' => true,
                'message' => 'IMEI đã được xóa thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error removing IMEI from transfer receipt: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Validate batch availability for transfer receipt
     */
    public function validateBatch(Request $request, $id)
    {
        $request->validate([
            'item_id' => 'required|exists:transfer_receipt_items,id',
            'batch_number' => 'required|string|max:255',
            'quantity' => 'required|numeric|min:0.01',
        ]);

        try {
            $result = $this->transferReceiptRepository->validateBatchAvailability(
                $id,
                $request->item_id,
                $request->batch_number,
                $request->quantity
            );

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Error validating batch for transfer receipt: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Add batch to transfer receipt item
     */
    public function addBatch(Request $request, $id)
    {
        $request->validate([
            'item_id' => 'required|exists:transfer_receipt_items,id',
            'batch_number' => 'required|string|max:255',
            'quantity' => 'required|numeric|min:0.01',
            'expiry_date' => 'nullable|date',
        ]);

        try {
            $batch = $this->transferReceiptRepository->addBatch(
                $id,
                $request->item_id,
                $request->only(['batch_number', 'quantity', 'expiry_date'])
            );

            return response()->json([
                'success' => true,
                'message' => 'Batch đã được thêm thành công.',
                'data' => $batch
            ]);
        } catch (\Exception $e) {
            Log::error('Error adding batch to transfer receipt: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Remove batch from transfer receipt item
     */
    public function removeBatch(Request $request, $id, $batchId)
    {
        try {
            $this->transferReceiptRepository->removeBatch($id, $batchId);

            return response()->json([
                'success' => true,
                'message' => 'Batch đã được xóa thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error removing batch from transfer receipt: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get IMEIs for transfer receipt item
     */
    public function getImeis($id, $itemId)
    {
        try {
            $transferReceipt = $this->transferReceiptRepository->getById($id);
            $item = $transferReceipt->items()->with('imeis.scannedBy')->findOrFail($itemId);

            return response()->json([
                'success' => true,
                'data' => $item->imeis
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting IMEIs for transfer receipt item: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải danh sách IMEI.'
            ], 500);
        }
    }

    /**
     * Get batches for transfer receipt item
     */
    public function getBatches($id, $itemId)
    {
        try {
            $transferReceipt = $this->transferReceiptRepository->getById($id);
            $item = $transferReceipt->items()->with('batches')->findOrFail($itemId);

            return response()->json([
                'success' => true,
                'data' => $item->batches
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting batches for transfer receipt item: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải danh sách batch.'
            ], 500);
        }
    }
}
