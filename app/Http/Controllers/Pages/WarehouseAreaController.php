<?php

namespace App\Http\Controllers\Pages;

use App\Contracts\WarehouseInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\WarehouseAreaRequest;
use App\Models\WarehouseArea;
use Illuminate\Http\Request;

class WarehouseAreaController extends Controller
{
    protected $warehouses;

    public function __construct(WarehouseInterface $warehouses)
    {
        $this->warehouses = $warehouses;
    }

    /**
     * Display a listing of warehouse areas
     */
    public function index()
    {
        $warehouses = $this->warehouses->getAllWarehouses();
        return view('content.pages.warehouses.areas.index', compact('warehouses'));
    }

    /**
     * Show the form for creating a new warehouse area
     */
    public function create()
    {
        $warehouses = $this->warehouses->getAllWarehouses();
        return view('content.pages.warehouses.areas.create', compact('warehouses'));
    }

    /**
     * Store a newly created warehouse area
     */
    public function store(WarehouseAreaRequest $request)
    {
        try {
            $data = $request->validated();
            $result = $this->warehouses->createWarehouseArea($data['warehouse_id'], $data);

            if ($result['success']) {
                return response()->json([
                    'title' => 'success',
                    'text' => $result['message'],
                    'code' => 200
                ]);
            } else {
                return response()->json([
                    'title' => 'errors',
                    'text' => $result['message'],
                    'code' => 400
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi tạo khu vực kho: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Show the form for editing the specified warehouse area
     */
    public function edit($id)
    {
        $area = WarehouseArea::findOrFail($id);
        $warehouses = $this->warehouses->getAllWarehouses();

        return view('content.pages.warehouses.areas.edit', compact('area', 'warehouses'));
    }

    /**
     * Update the specified warehouse area
     */
    public function update(WarehouseAreaRequest $request, $id)
    {
        try {
            $data = $request->validated();
            $area = WarehouseArea::findOrFail($id);
            $result = $this->warehouses->updateWarehouseArea($data['warehouse_id'], $id, $data);

            if ($result['success']) {
                return response()->json([
                    'title' => 'success',
                    'text' => $result['message'],
                    'code' => 200
                ]);
            } else {
                return response()->json([
                    'title' => 'errors',
                    'text' => $result['message'],
                    'code' => 400
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi cập nhật khu vực kho: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Remove the specified warehouse area
     */
    public function delete($id)
    {
        try {
            $area = WarehouseArea::findOrFail($id);
            $result = $this->warehouses->deleteWarehouseArea($area->warehouse_id, $id);

            if ($result['success']) {
                return response()->json([
                    'title' => 'success',
                    'text' => $result['message'],
                    'code' => 200
                ]);
            } else {
                return response()->json([
                    'title' => 'errors',
                    'text' => $result['message'],
                    'code' => 400
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi xóa khu vực kho: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Get warehouse areas for datatable
     */
    public function getDatatable(Request $request)
    {
        $query = WarehouseArea::with('warehouse');

        // Filter by warehouse
        if ($request->has('warehouse_id') && !empty($request->warehouse_id)) {
            $query->where('warehouse_id', $request->warehouse_id);
        }

        // Search
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $query->where(function ($q) use ($searchValue) {
                $q->where('name', 'like', "%{$searchValue}%")
                    ->orWhere('code', 'like', "%{$searchValue}%")
                    ->orWhere('description', 'like', "%{$searchValue}%");
            });
        }

        // Order
        if ($request->has('order') && !empty($request->order)) {
            $order = $request->order[0];
            $columnIndex = $order['column'];
            $columnName = $request->columns[$columnIndex]['data'];
            $columnDirection = $order['dir'];

            if ($columnName !== 'actions' && $columnName !== 'warehouse') {
                $query->orderBy($columnName, $columnDirection);
            } else if ($columnName === 'warehouse') {
                $query->join('warehouses', 'warehouse_areas.warehouse_id', '=', 'warehouses.id')
                    ->orderBy('warehouses.name', $columnDirection)
                    ->select('warehouse_areas.*');
            }
        } else {
            $query->orderBy('name', 'asc');
        }

        // Pagination
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;

        $recordsTotal = $query->count();

        if ($length != -1) {
            $query->skip($start)->take($length);
        } else {
            // If length is -1, we want all records, but MySQL doesn't allow OFFSET without LIMIT
            // So we use a very large number as the limit
            $query->skip($start)->take(PHP_INT_MAX);
        }

        $areas = $query->get();

        $data = [];
        foreach ($areas as $area) {
            $data[] = [
                'id' => $area->id,
                'warehouse' => $area->warehouse->name,
                'code' => $area->code,
                'name' => $area->name,
                'description' => $area->description,
                'is_active' => $area->is_active,
                'actions' => '' // Will be rendered on the client side
            ];
        }

        return [
            'draw' => intval($request->draw),
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsTotal,
            'data' => $data
        ];
    }
}
