<?php

namespace App\Http\Controllers\Pages;

use App\Events\TestBroadcast;
use App\Helpers\Classes\Helper;
use App\Http\Controllers\Controller;
use App\Http\Requests\SettingsAsusRequest;
use App\Http\Requests\SettingsGeneralRequest;
use App\Repositories\SettingsRepository;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Spatie\Health\Commands\RunHealthChecksCommand;
use Spatie\Health\ResultStores\ResultStore;
use Spatie\Permission\PermissionRegistrar;
use Throwable;

class SettingsController extends Controller
{
  protected SettingsRepository $settings;

  public function __construct(SettingsRepository $settings)
  {
    $this->settings = $settings;
  }

  public function index()
  {
    $general = $this->settings->getGeneral();
    $smtp = $this->settings->getSMTP();
    $notification = $this->settings->getNotification();
    $vnpt = $this->settings->getVnpt();
    $nhattin = $this->settings->getNhattin();
    $sap = $this->settings->getSAP();
    $zalo = $this->settings->getZalo();
    $asus = $this->settings->getAsus();
    $product = $this->settings->getProductSettings();
    return view('content.pages.settings.index', compact('general', 'smtp', 'notification', 'vnpt', 'nhattin', 'sap', 'zalo', 'asus', 'product'));
  }

  public function platformAuthorize()
  {
    return view('content.pages.settings.authorize');
  }

  public function cachePermissionClear()
  {
    try {
      app()->make(PermissionRegistrar::class)->forgetCachedPermissions();
      return response()->json(['status' => 'success', 'message' => 'Clear cache successfully!', 'title' => "Success"]);
    } catch (Throwable $th) {
      return response()->json(['status' => 'error', 'message' => $th->getMessage(), 'title' => "Error"]);
    }
  }

  public function cacheClear()
  {
    try {
      Artisan::call('optimize:clear');
      Artisan::call('route:trans:cache');
      return response()->json(['status' => 'success', 'message' => 'Clear cache successfully!', 'title' => "Success"]);
    } catch (Throwable $th) {
      return response()->json(['status' => 'error', 'message' => $th->getMessage(), 'title' => "Error"]);
    }
  }

  public function health()
  {
    $resultStore = App::make(ResultStore::class);
    $checkResults = $resultStore->latestResults();
    $serverInfo = [
      ['key' => 'version_php', 'value' => phpversion(), 'icon' => "ri-command-line"],
      ['key' => 'version_laravel', 'value' => app()->version(), 'icon' => "ri-terminal-fill"],
      ['key' => 'version_app', 'value' => get_settings('script_version'), 'icon' => "ri-terminal-fill"],
      ['key' => 'memory_limit', 'value' => ini_get('memory_limit'), 'icon' => "ri-ram-line"],
      ['key' => 'max_input_vars', 'value' => ini_get('max_input_vars'), 'icon' => "ri-information-line"],
      ['key' => 'post_max_size', 'value' => ini_get('post_max_size'), 'icon' => "ri-information-line"],
      ['key' => 'os', 'value' => php_uname(), 'icon' => "ri-macbook-line"],
      ['key' => 'server', 'value' => $_SERVER['SERVER_SOFTWARE'], 'icon' => "ri-computer-line"],
      ['key' => 'server_ip', 'value' => $_SERVER['SERVER_ADDR'], 'icon' => "ri-server-line"],
      ['key' => 'app_status', 'value' => env('APP_DEBUG'), 'icon' => "ri-information-line"],
      ['key' => 'app_log_level', 'value' => env('APP_LOG_LEVEL'), 'icon' => "ri-information-line"],
      ['key' => 'app_env', 'value' => env('APP_ENV'), 'icon' => "ri-information-line"],
    ];
    debug($serverInfo);
    // call new status when visit the page
    Artisan::call(RunHealthChecksCommand::class);
    return view('content.pages.settings.health', compact('checkResults', 'serverInfo'));
  }

  public function updateGeneral(SettingsGeneralRequest $request)
  {
    $data = $request->only([
      'site_name',
      'site_url',
      'site_email',
      'site_meta_title',
      'site_meta_description',
      'recaptcha_login',
      'recaptcha_register',
      'recaptcha_sitekey',
      'recaptcha_secretkey',
      'login_with_otp'
    ]);

    if ($request->file('logo')) {
      $logo = Helper::saveFile($request->file('logo'), 'logo', 'updateGeneral', 'logo', true);
      $data['site_logo'] = $logo;
      $data['logo_path'] = "/storage/logo/" . $logo;
    }

    if ($request->file('favicon')) {
      $favicon = Helper::saveFile($request->file('favicon'), 'favicon', 'updateGeneral', 'favicon', true);
      $data['site_favicon'] = $favicon;
      $data['favicon_path'] = "/storage/favicon/" . $favicon;
    }

    $updated = $this->settings->updateGeneral($data);
    if ($updated)
      return response()->json(['status' => 'success', 'message' => __('common.update_success'), 'title' => "Success"]);
    return response()->json(['status' => 'error', 'message' => __('common.update_fail'), 'title' => "Error"]);
  }

  public function updateSMTP(SettingsAsusRequest $request)
  {
    $data = $request->only([
      'smtp_host',
      'smtp_port',
      'smtp_encryption',
      'smtp_username',
      'smtp_password',
      'smtp_sender_email',
      'smtp_sender_name',
    ]);
    $updated = $this->settings->updateSMTP($data);
    if ($updated)
      return response()->json(['status' => 'success', 'message' => __('common.update_success'), 'title' => "Success"]);
    return response()->json(['status' => 'error', 'message' => __('common.update_fail'), 'title' => "Error"]);
  }

  public function testSmtp(SettingsAsusRequest $request)
  {
    try {
      $email = $request->only([
        'smtp_email_test'
      ]);

      $mailConfig = config('mail');
      $data = ['message' => 'Đây là email test để kiểm tra cấu hình SMTP.'];
      Mail::raw($data['message'], function ($message) use ($email) {
        $message->to($email['smtp_email_test'])
          ->subject('Test Email');
      });

      return response()->json([
        'message' => 'Email sent!',
        'status' => 'success',
        'title' => "Success",
        'config' => [
          'host' => $mailConfig['mailers']['smtp']['host'],
          'port' => $mailConfig['mailers']['smtp']['port'],
          'username' => $mailConfig['mailers']['smtp']['username'],
          'encryption' => $mailConfig['mailers']['smtp']['encryption'],
          'from_address' => $mailConfig['from']['address'],
          'from_name' => $mailConfig['from']['name'],
        ],
      ]);

    } catch (\Exception $e) {
      Log::error('Email sending failed:', [
        'error' => $e->getMessage(),
      ]);

      return response()->json([
        'message' => $e->getMessage(),
        'Title' => 'Failed to send email.',
        'status' => 'error'
      ], 400);
    }
  }

  public function updateNotification(SettingsAsusRequest $request)
  {
    $data = $request->only([
      'push_notification',
      'push_app_id',
      'push_api_key',
      'push_secret_key',
      'push_cluster',
      'reverb_notification',
      'reverb_app_id',
      'reverb_api_key',
      'reverb_secret_key',
      'reverb_host',
      'reverb_port',
      'reverb_scheme',
    ]);

    $updated = $this->settings->updateNotification($data);
    if ($updated)
      return response()->json(['status' => 'success', 'message' => __('common.update_success'), 'title' => "Success"]);
    return response()->json(['status' => 'error', 'message' => __('common.update_fail'), 'title' => "Error"]);
  }

  public function testBroadcast()
  {
    try {
      broadcast(new TestBroadcast());
      return response()->json(['status' => 'success', 'message' => __('common.update_success'), 'title' => "Success"]);
    } catch (\Exception $e) {
      return response()->json(['status' => 'error', 'message' => $e->getMessage(), 'title' => "Error"]);
    }
  }

  public function updateVNPT(SettingsAsusRequest $request)
  {
    $data = $request->only([
      'vnpt_public_service_endpoint',
      'vnpt_portal_service_endpoint',
      'vnpt_biz_service_endpoint',
      'vnpt_username',
      'vnpt_password',
      'vnpt_account_username',
      'vnpt_account_password',
      'vnpt_pattern',
      'vnpt_serial',
      'vnpt_sandbox_public_service_endpoint',
      'vnpt_sandbox_portal_service_endpoint',
      'vnpt_sandbox_biz_service_endpoint',
      'vnpt_sandbox_username',
      'vnpt_sandbox_password',
      'vnpt_sandbox_account_username',
      'vnpt_sandbox_account_password',
      'vnpt_sandbox_pattern',
      'vnpt_sandbox_serial',
      'vnpt_active',
      'vnpt_sandbox_active',
    ]);

    $updated = $this->settings->updateVnpt($data);
    if ($updated)
      return response()->json(['status' => 'success', 'message' => __('common.update_success'), 'title' => "Success"]);
    return response()->json(['status' => 'error', 'message' => __('common.update_fail'), 'title' => "Error"]);
  }

  public function updateNhattin(SettingsAsusRequest $request)
  {
    $data = $request->only([
      'nhattin_host',
      'nhattin_parner_id',
      'nhattin_username',
      'nhattin_password',
      'nhattin_service_id',
      'nhattin_payment_method',
      'nhattin_cod',
      'nhattin_cargo_type',
      'nhattin_sandbox_host',
      'nhattin_sandbox_parner_id',
      'nhattin_sandbox_username',
      'nhattin_sandbox_password',
      'nhattin_sandbox_service_id',
      'nhattin_sandbox_payment_method',
      'nhattin_sandbox_cod',
      'nhattin_sandbox_cargo_type',
      'nhattin_sender_name',
      'nhattin_sender_phone',
      'nhattin_sender_address',
      'nhattin_ufm_source',
      'nhattin_active',
      'nhattin_sandbox_active',
    ]);

    $updated = $this->settings->updateNhattin($data);
    if ($updated)
      return response()->json(['status' => 'success', 'message' => __('common.update_success'), 'title' => "Success"]);
    return response()->json(['status' => 'error', 'message' => __('common.update_fail'), 'title' => "Error"]);
  }

  public function updateSAP(SettingsAsusRequest $request)
  {
    $data = $request->only([
      'sap_active',
      'sap_server',
      'sap_database',
      'sap_username',
      'sap_password',
      'sap_sandbox_active',
      'sap_sandbox_server',
      'sap_sandbox_database',
      'sap_sandbox_username',
      'sap_sandbox_password',
    ]);

    $updated = $this->settings->updateSAP($data);
    if ($updated)
      return response()->json(['status' => 'success', 'message' => __('common.update_success'), 'title' => "Success"]);
    return response()->json(['status' => 'error', 'message' => __('common.update_fail'), 'title' => "Error"]);
  }

  public function updateZALO(SettingsAsusRequest $request)
  {
    $data = $request->only([
      'zalo_active',
      'zalo_host',
      'zalo_redirect_url',
      'zalo_app_id',
      'zalo_app_secret',
      'zalo_code_verifier',
    ]);

    $updated = $this->settings->updateZalo($data);
    if ($updated)
      return response()->json(['status' => 'success', 'message' => __('common.update_success'), 'title' => "Success"]);
    return response()->json(['status' => 'error', 'message' => __('common.update_fail'), 'title' => "Error"]);
  }

  public function updateASUS(SettingsAsusRequest $request)
  {
    $data = $request->only([
      'asus_active',
      'asus_host',
      'asus_token',
      'asus_sandbox_active',
      'asus_sandbox_host',
      'asus_sandbox_token',
    ]);

    $updated = $this->settings->updateAsus($data);
    if ($updated)
      return response()->json(['status' => 'success', 'message' => __('common.update_success'), 'title' => "Success"]);
    return response()->json(['status' => 'error', 'message' => __('common.update_fail'), 'title' => "Error"]);
  }

  public function updateProduct(SettingsAsusRequest $request)
  {
    $data = $request->only([
      'product_default_form',
    ]);

    $updated = $this->settings->updateProductSettings($data);
    if ($updated)
      return response()->json(['status' => 'success', 'message' => __('common.update_success'), 'title' => "Success"]);
    return response()->json(['status' => 'error', 'message' => __('common.update_fail'), 'title' => "Error"]);
  }

}
