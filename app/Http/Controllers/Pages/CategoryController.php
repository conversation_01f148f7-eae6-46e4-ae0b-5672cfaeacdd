<?php

namespace App\Http\Controllers\Pages;

use App\Http\Controllers\Controller;
use App\Http\Requests\CategoryRequest;
use App\Models\Category;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    /**
     * Display a listing of categories
     */
    public function index()
    {
        $parentCategories = Category::whereNull('parent_id')->active()->get();

        return view('content.pages.products.categories.index', compact('parentCategories'));
    }

    /**
     * Show the form for creating a new category
     */
    public function create()
    {
        $parentCategories = Category::whereNull('parent_id')->active()->get();

        return view('content.pages.products.categories.create', compact('parentCategories'));
    }

    /**
     * Store a newly created category
     */
    public function store(CategoryRequest $request)
    {
        try {
            // Validate parent_id if provided
            if ($request->has('parent_id') && !empty($request->parent_id)) {
                // Check if parent category exists
                $parentCategory = Category::find($request->parent_id);
                if (!$parentCategory) {
                    return response()->json([
                        'title' => 'error',
                        'text' => '<PERSON>h mục cha không tồn tại',
                    ], 422);
                }
            }

            $data = $request->validated();

            // Handle empty parent_id
            if (isset($data['parent_id']) && empty($data['parent_id'])) {
                $data['parent_id'] = null;
            }

            // Handle is_active checkbox
            $data['is_active'] = $request->has('is_active');

            $category = Category::create($data);

            return response()->json([
                'title' => 'success',
                'text' => 'Danh mục đã được tạo thành công',
                'redirect' => route('categories.list')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'title' => 'error',
                'text' => 'Có lỗi xảy ra khi tạo danh mục: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified category
     */
    public function edit($id)
    {
        $category = Category::findOrFail($id);
        $parentCategories = Category::whereNull('parent_id')
            ->where('id', '!=', $id)
            ->active()
            ->get();

        return view('content.pages.products.categories.edit', compact('category', 'parentCategories'));
    }

    /**
     * Update the specified category
     */
    public function update(CategoryRequest $request, $id)
    {
        try {
            $category = Category::findOrFail($id);

            // Check if trying to set as parent of itself or its children
            if ($request->parent_id && $this->isChildOf($id, $request->parent_id)) {
                return response()->json([
                    'title' => 'errors',
                    'text' => 'Không thể đặt danh mục này làm danh mục con của chính nó hoặc của danh mục con của nó',
                    'code' => 400
                ]);
            }

            $category->update($request->validated());

            return response()->json([
                'title' => 'success',
                'text' => 'Danh mục đã được cập nhật thành công',
                'code' => 200
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi cập nhật danh mục: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Remove the specified category
     */
    public function delete($id)
    {
        try {
            $category = Category::findOrFail($id);

            // Check if category has children
            if ($category->children()->count() > 0) {
                return response()->json([
                    'title' => 'errors',
                    'text' => 'Không thể xóa danh mục có danh mục con',
                    'code' => 400
                ]);
            }

            // Check if category has products
            if ($category->products()->count() > 0) {
                return response()->json([
                    'title' => 'errors',
                    'text' => 'Không thể xóa danh mục có sản phẩm',
                    'code' => 400
                ]);
            }

            $category->delete();

            return response()->json([
                'title' => 'success',
                'text' => 'Danh mục đã được xóa thành công',
                'code' => 200
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'title' => 'errors',
                'text' => 'Có lỗi xảy ra khi xóa danh mục: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }

    /**
     * Get categories for datatable
     */
    public function getDatatable(Request $request)
    {
        $query = Category::with('parent');

        // Filter by parent
        if ($request->has('parent_id') && $request->parent_id !== 'all') {
            if ($request->parent_id === 'null') {
                $query->whereNull('parent_id');
            } else {
                $query->where('parent_id', $request->parent_id);
            }
        }

        // Filter by status
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('is_active', $request->status === 'active');
        }

        // Search
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $query->where(function ($q) use ($searchValue) {
                $q->where('name', 'like', "%{$searchValue}%")
                    ->orWhere('description', 'like', "%{$searchValue}%");
            });
        }

        // Order
        if ($request->has('order') && !empty($request->order)) {
            $order = $request->order[0];
            $columnIndex = $order['column'];
            $columnName = $request->columns[$columnIndex]['data'];
            $columnDirection = $order['dir'];

            if ($columnName === 'parent') {
                $query->leftJoin('categories as parent_categories', 'categories.parent_id', '=', 'parent_categories.id')
                    ->orderBy('parent_categories.name', $columnDirection)
                    ->select('categories.*');
            } else if ($columnName !== 'actions') {
                $query->orderBy($columnName, $columnDirection);
            }
        } else {
            $query->orderBy('name', 'asc');
        }

        // Pagination
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;

        $recordsTotal = $query->count();

        if ($length != -1) {
            $query->skip($start)->take($length);
        } else {
            // If length is -1, we want all records, but MySQL doesn't allow OFFSET without LIMIT
            // So we use a very large number as the limit
            $query->skip($start)->take(PHP_INT_MAX);
        }

        $categories = $query->get();

        $data = [];
        foreach ($categories as $category) {
            $data[] = [
                'id' => $category->id,
                'name' => $category->name,
                'parent' => $category->parent ? $category->parent->name : 'N/A',
                'description' => $category->description,
                'is_active' => $category->is_active,
                'actions' => '' // Will be rendered on the client side
            ];
        }

        return [
            'draw' => intval($request->draw),
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsTotal,
            'data' => $data
        ];
    }

    /**
     * Check if a category is a child of another category
     */
    private function isChildOf($categoryId, $parentId)
    {
        if ($categoryId == $parentId) {
            return true;
        }

        $parent = Category::find($parentId);

        if (!$parent) {
            return false;
        }

        if ($parent->parent_id) {
            return $this->isChildOf($categoryId, $parent->parent_id);
        }

        return false;
    }
}
