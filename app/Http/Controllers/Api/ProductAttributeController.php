<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Repositories\ProductAttributeRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProductAttributeController extends Controller
{
    protected $repository;

    public function __construct(ProductAttributeRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Get all attributes for a product
     *
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductAttributes($productId)
    {
        $result = $this->repository->getProductAttributes($productId);

        if ($result['success']) {
            return response()->json([
                'status' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ]);
        } else {
            return response()->json([
                'status' => false,
                'message' => $result['message'],
                'data' => []
            ], 500);
        }
    }

    /**
     * Add an attribute to a product
     *
     * @param \Illuminate\Http\Request $request
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function addProductAttribute(Request $request, $productId)
    {
        $validator = Validator::make($request->all(), [
            'attribute_id' => 'required|exists:attributes,id',
            'attribute_value_id' => 'nullable|exists:attribute_values,id',
            'text_value' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->repository->addProductAttribute($productId, $request->all());

        if ($result['success']) {
            return response()->json([
                'status' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ]);
        } else {
            return response()->json([
                'status' => false,
                'message' => $result['message'],
                'data' => []
            ], 422);
        }
    }

    /**
     * Update a product attribute
     *
     * @param \Illuminate\Http\Request $request
     * @param int $productId
     * @param int $attributeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProductAttribute(Request $request, $productId, $attributeId)
    {
        $validator = Validator::make($request->all(), [
            'attribute_value_id' => 'nullable|exists:attribute_values,id',
            'text_value' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->repository->updateProductAttribute($productId, $attributeId, $request->all());

        if ($result['success']) {
            return response()->json([
                'status' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ]);
        } else {
            $statusCode = strpos($result['message'], 'not found') !== false ? 404 : 422;
            return response()->json([
                'status' => false,
                'message' => $result['message'],
                'data' => []
            ], $statusCode);
        }
    }

    /**
     * Delete a product attribute
     *
     * @param int $productId
     * @param int $attributeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteProductAttribute($productId, $attributeId)
    {
        $result = $this->repository->deleteProductAttribute($productId, $attributeId);

        if ($result['success']) {
            return response()->json([
                'status' => true,
                'message' => $result['message'],
                'data' => []
            ]);
        } else {
            $statusCode = strpos($result['message'], 'not found') !== false ? 404 : 500;
            return response()->json([
                'status' => false,
                'message' => $result['message'],
                'data' => []
            ], $statusCode);
        }
    }

    /**
     * Get all available attributes that can be assigned to products
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableAttributes()
    {
        $result = $this->repository->getAvailableAttributes();

        if ($result['success']) {
            return response()->json([
                'status' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ]);
        } else {
            return response()->json([
                'status' => false,
                'message' => $result['message'],
                'data' => []
            ], 500);
        }
    }

    /**
     * Batch update product attributes
     *
     * @param \Illuminate\Http\Request $request
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchUpdateProductAttributes(Request $request, $productId)
    {
        $validator = Validator::make($request->all(), [
            'attributes' => 'required|array',
            'attributes.*.attribute_id' => 'required|exists:attributes,id',
            'attributes.*.attribute_value_id' => 'nullable|exists:attribute_values,id',
            'attributes.*.text_value' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->repository->batchUpdateProductAttributes($productId, $request->attributes);

        if ($result['success']) {
            return response()->json([
                'status' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ]);
        } else {
            return response()->json([
                'status' => false,
                'message' => $result['message'],
                'data' => []
            ], 422);
        }
    }
}
