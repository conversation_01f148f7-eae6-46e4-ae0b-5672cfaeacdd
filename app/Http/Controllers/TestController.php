<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TestController extends Controller
{
    public function testCreateProduct(Request $request)
    {
        try {
            // Tạo sản phẩm với dữ liệu tối thiểu
            $product = new Product();
            $product->name = 'Test Product ' . time();
            $product->code = 'TEST-' . time();
            $product->description = 'Test description';
            $product->category_id = 1; // Giả sử ID 1 tồn tại
            $product->brand_id = 1; // Giả sử ID 1 tồn tại
            $product->unit = 'cái';
            $product->price = 100000;
            $product->cost = 80000;
            $product->tax = 10;
            $product->is_active = true;
            $product->image = null;
            $product->barcode = null;
            $product->weight = 1;
            $product->dimensions = json_encode(['length' => 10, 'width' => 10, 'height' => 10]);
            
            // <PERSON><PERSON><PERSON> sản phẩm
            $product->save();
            
            return response()->json([
                'success' => true,
                'message' => 'Sản phẩm đã được tạo thành công',
                'product' => $product
            ]);
        } catch (\Exception $e) {
            Log::error('Error in testCreateProduct: ' . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }
}
