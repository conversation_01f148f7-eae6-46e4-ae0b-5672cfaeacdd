<?php

namespace App\Http\Controllers\Installation;

use App\Enums\Roles;
use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Models\Settings;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Exception;
use Illuminate\Support\Str;

class InstallationController extends Controller
{
  public function envFileEditor()
  {
    try {
      DB::connection()->getPdo();
      $db_set = 1;
    } catch (Exception $e) {
      $db_set = 2;
    }
    if ($db_set == 1) {
      if (Schema::hasTable('migrations')) {
        $db_ready = 1;
      }
    } else {
      $db_ready = 0;
    }

    if ($db_ready == 0) {
      return view('vendor.installer.env_file_editor');
    }
  }

  public function envFileEditorSave(Request $request)
  {

    $envFileData =
      'APP_NAME="' . $request->app_name . '"' . "\n" .
      'APP_ENV=' . $request->environment . "\n" .
      'APP_KEY=' . 'base64:' . base64_encode(Str::random(32)) . "\n" .
      'APP_DEBUG=' . $request->app_debug . "\n" .
      'APP_URL=' . $request->app_url . "\n\n" .
      'DB_CONNECTION=' . 'mysql' . "\n" .
      'DB_HOST=' . $request->database_hostname . "\n" .
      'DB_PORT=' . '3306' . "\n" .
      'DB_DATABASE=' . $request->database_name . "\n" .
      'DB_USERNAME=' . $request->database_username . "\n" .
      'DB_PASSWORD="' . $request->database_password . '"' . "\n\n" .
      'BROADCAST_DRIVER=' . 'pusher' . "\n" .
      'CACHE_DRIVER=' . 'file' . "\n" .
      'SESSION_DRIVER=' . 'file' . "\n" .
      'QUEUE_DRIVER=' . 'sync' . "\n\n" .
      'QUEUE_CONNECTION=' . 'database' . "\n\n" .
      'REDIS_HOST=' . '127.0.0.1' . "\n" .
      'REDIS_PASSWORD=' . 'null' . "\n" .
      'REDIS_PORT=' . '6379' . "\n\n" .
      'MAIL_DRIVER=' . $request->mail_driver . "\n" .
      'MAIL_HOST=' . $request->mail_host . "\n" .
      'MAIL_PORT=' . $request->mail_port . "\n" .
      'MAIL_USERNAME=' . $request->mail_username . "\n" .
      'MAIL_PASSWORD=' . $request->mail_password . "\n" .
      'MAIL_ENCRYPTION=' . $request->mail_encryption . "\n\n" .
      'MAIL_FROM_ADDRESS=' . $request->mail_from_address . "\n\n" .
      'MAIL_FROM_NAME=' . $request->mail_from_name . "\n\n" .
      'PUSHER_APP_ID=' . '' . "\n" .
      'PUSHER_APP_KEY=' . '' . "\n" .
      'PUSHER_APP_SECRET=' . '';

    try {
      $envPath = base_path('.env');
      file_put_contents($envPath, $envFileData);
      $request->flash();

      return redirect()->route('installer.install');
    } catch (Exception $e) {
      echo 'Cannot update .env file. Please update file manually in order to run this script. Need help? Call 0772852353';
    }
  }

  public function install(Request $request)
  {
    try {
      $dbconnect = DB::connection()->getPDO();
      $dbname = DB::connection()->getDatabaseName();
    } catch (Exception $e) {
      return redirect()->route('installer.envEditor');
    }

    if (!Schema::hasTable('migrations')) {
      Artisan::call('migrate', [
        '--force' => true,
      ]);
      Artisan::call('db:seed', [
        '--force' => true,
      ]);
    } else {
      return redirect()->route('index');
    }

    if (!Schema::hasTable('activity')) {
      return 'You are using Plesk for WMS-1Digital. It requires MariaDB 10.X or Mysql 5.6,5.7. Please check your mariaDB or Mysql version. After upgrade your mariadb or mysql please reset the table.';
    }

    //First Startup of Script
    $settings = Settings::first();
    if ($settings == null) {
      $settings = new Setting;
      $settings->save();
    }

    $adminUser = User::where('type', Roles::SUPER_ADMIN->value)->first();
    if ($adminUser == null) {
      $adminUser = new User;
      $adminUser->name = 'Admin';
      $adminUser->surname = 'Admin';
      $adminUser->email = '<EMAIL>';
      $adminUser->phone = '5555555555';
      $adminUser->password = '$2y$10$XptdAOeFTxl7Yx2KmyfEluWY9Im6wpMIHoJ9V5yB96DgQgTafzzs6';
      $adminUser->status = 1;
      $adminUser->save();
    }

    Auth::login($adminUser);

    return redirect()->route('dashboard.admin.settings.general');
  }
}
