<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class RolesRequest extends FormRequest
{
  use RequestTrait;
  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
   */
  public function rules(): array
  {
    return [
      'name' => 'required|string|max:255|unique:roles,name,'.$this->role_id,
      'permissions' => 'required|array',
    ];
  }

  public function messages()
  {
    return [
      'name.required' => __('validation.custom.role.required'),
      'name.unique' => __('validation.custom.role.unique'),
      'permissions.required' => __('validation.custom.permission.required'),
      'permissions.array' => __('validation.custom.permission.array'),
    ];
  }
}
