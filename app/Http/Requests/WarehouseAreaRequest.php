<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WarehouseAreaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $areaId = $this->route('id');
        
        return [
            'warehouse_id' => ['required', 'exists:warehouses,id'],
            'name' => ['required', 'string', 'max:255'],
            'code' => [
                'required', 
                'string', 
                'max:50',
                Rule::unique('warehouse_areas')->where(function ($query) {
                    return $query->where('warehouse_id', $this->warehouse_id);
                })->ignore($areaId)
            ],
            'description' => ['nullable', 'string'],
            'is_active' => ['boolean'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'warehouse_id.required' => '<PERSON>ho hàng là bắt buộc',
            'warehouse_id.exists' => 'Kho hàng không tồn tại',
            'name.required' => 'Tên khu vực là bắt buộc',
            'name.max' => 'Tên khu vực không được vượt quá 255 ký tự',
            'code.required' => 'Mã khu vực là bắt buộc',
            'code.max' => 'Mã khu vực không được vượt quá 50 ký tự',
            'code.unique' => 'Mã khu vực đã tồn tại trong kho hàng này',
        ];
    }
}
