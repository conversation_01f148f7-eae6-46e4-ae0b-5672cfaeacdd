<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SettingsAsusRequest extends FormRequest
{
  use RequestTrait;

  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
   */
  public function rules(): array
  {
    return [
      'asus_host' => 'nullable|url|max:255',
      'asus_token' => 'nullable|max:255',
      'asus_sandbox_host' => 'nullable|url|max:255',
      'asus_sandbox_token' => 'nullable|max:255',
    ];
  }

  public function messages()
  {
    return [
      'asus_host.max' => __('validation.custom.asus_host.max'),
      'asus_host.url' => __('validation.custom.asus_host.url'),
      'asus_token.max' => __('validation.custom.asus_token.max'),
      'asus_sandbox_host.max' => __('validation.custom.asus_sandbox_host.max'),
      'asus_sandbox_host.url' => __('validation.custom.asus_sandbox_host.url'),
      'asus_sandbox_token.max' => __('validation.custom.asus_sandbox_token.max'),
    ];
  }
}