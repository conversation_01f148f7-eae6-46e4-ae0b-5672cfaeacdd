<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SettingsSmtpRequest extends FormRequest
{
  use RequestTrait;

  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
   */
  public function rules(): array
  {
    return [
      'smtp_host' => 'nullable|max:255',
      'smtp_port' => 'nullable|integer',
      'smtp_username' => 'nullable|max:255',
      'smtp_password' => 'nullable||max:255',
      'smtp_sender_email' => 'nullable|email|max:255',
      'smtp_sender_name' => 'nullable|max:255',
      'smtp_encryption' => 'nullable|max:255',
      'smtp_email_test' => 'required|email'
    ];
  }

  public function messages()
  {
    return [
      'smtp_host.max' => __('validation.custom.smtp_host.max'),
      'smtp_port.integer' => __('validation.custom.smtp_port.integer'),
      'smtp_username.max' => __('validation.custom.smtp_username.max'),
      'smtp_password.max' => __('validation.custom.smtp_password.max'),
      'smtp_sender_email.email' => __('validation.custom.smtp_sender_email.email'),
      'smtp_sender_email.max' => __('validation.custom.smtp_sender_email.max'),
      'smtp_sender_name.max' => __('validation.custom.smtp_sender_name.max'),
      'smtp_encryption.max' => __('validation.custom.smtp_encryption.max'),
      'smtp_email_test.email' => __('validation.custom.smtp_email_test.email'),
      'smtp_email_test.required' => __('validation.custom.smtp_email_test.required'),
    ];
  }
}