<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SettingsVnptRequest extends FormRequest
{
  use RequestTrait;

  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
   */
  public function rules(): array
  {
    return [
      'vnpt_public_service_endpoint' => 'nullable|max:255',
      'vnpt_portal_service_endpoint' => 'nullable|max:255',
      'vnpt_biz_service_endpoint' => 'nullable||max:255',
      'vnpt_username' => 'nullable|max:255',
      'vnpt_password' => 'nullable|max:255',
      'vnpt_account_username' => 'nullable|max:255',
      'vnpt_account_password' => 'nullable|max:255',
      'vnpt_pattern' => 'nullable|max:20',
      'vnpt_serial' => 'nullable|max:20',
      'vnpt_sandbox_public_service_endpoint' => 'nullable|max:255',
      'vnpt_sandbox_portal_service_endpoint' => 'nullable|max:255',
      'vnpt_sandbox_biz_service_endpoint' => 'nullable||max:255',
      'vnpt_sandbox_username' => 'nullable|max:255',
      'vnpt_sandbox_password' => 'nullable|max:255',
      'vnpt_sandbox_account_username' => 'nullable|max:255',
      'vnpt_sandbox_account_password' => 'nullable|max:255',
      'vnpt_sandbox_pattern' => 'nullable|max:20',
      'vnpt_sandbox_serial' => 'nullable|max:20',
    ];
  }

  public function messages()
  {
    return [
      'vnpt_public_service_endpoint.max' => __('validation.custom.vnpt_public_service_endpoint.max'),
      'vnpt_portal_service_endpoint.max' => __('validation.custom.vnpt_portal_service_endpoint.max'),
      'vnpt_biz_service_endpoint.max' => __('validation.custom.vnpt_biz_service_endpoint.max'),
      'vnpt_username.max' => __('validation.custom.vnpt_username.max'),
      'vnpt_password.max' => __('validation.custom.vnpt_password.max'),
      'vnpt_account_username.max' => __('validation.custom.vnpt_account_username.max'),
      'vnpt_account_password.max' => __('validation.custom.vnpt_account_password.max'),
      'vnpt_pattern.max' => __('validation.custom.vnpt_pattern.max'),
      'vnpt_serial.max' => __('validation.custom.vnpt_serial.max'),
      'vnpt_sandbox_public_service_endpoint.max' => __('validation.custom.vnpt_sandbox_public_service_endpoint.max'),
      'vnpt_sandbox_portal_service_endpoint.max' => __('validation.custom.vnpt_sandbox_portal_service_endpoint.max'),
      'vnpt_sandbox_biz_service_endpoint.max' => __('validation.custom.vnpt_sandbox_biz_service_endpoint.max'),
      'vnpt_sandbox_username.max' => __('validation.custom.vnpt_sandbox_username.max'),
      'vnpt_sandbox_password.max' => __('validation.custom.vnpt_sandbox_password.max'),
      'vnpt_sandbox_account_username.max' => __('validation.vnpt_sandbox_account_username.push_cluster.max'),
      'vnpt_sandbox_account_password.max' => __('validation.vnpt_sandbox_account_password.push_cluster.max'),
      'vnpt_sandbox_pattern.max' => __('validation.custom.vnpt_sandbox_pattern.max'),
      'vnpt_sandbox_serial.max' => __('validation.custom.vnpt_sandbox_serial.max'),
    ];
  }
}