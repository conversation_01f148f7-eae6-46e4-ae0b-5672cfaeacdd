<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SettingsZaloRequest extends FormRequest
{
  use RequestTrait;

  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
   */
  public function rules(): array
  {
    return [
      'zalo_host' => 'nullable|url|max:255',
      'zalo_redirect_url' => 'nullable|url|max:255',
      'zalo_app_id' => 'nullable||max:255',
      'zalo_app_secret' => 'nullable|max:255',
      'zalo_code_verifier' => 'nullable|max:255',
    ];
  }

  public function messages()
  {
    return [
      'zalo_host.max' => __('validation.custom.zalo_host.max'),
      'zalo_host.url' => __('validation.custom.zalo_host.url'),
      'zalo_redirect_url.max' => __('validation.custom.zalo_redirect_url.max'),
      'zalo_redirect_url.url' => __('validation.custom.zalo_redirect_url.url'),
      'zalo_app_id.max' => __('validation.custom.zalo_app_id.max'),
      'zalo_app_secret.max' => __('validation.custom.zalo_app_secret.max'),
      'zalo_code_verifier.max' => __('validation.custom.zalo_code_verifier.max'),
    ];
  }
}