<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Product extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'description',
        'category_id',
        'brand_id',
        'unit',
        'price',
        'cost',
        'tax',
        'is_active',
        'image',
        'barcode',
        'inventory_tracking_type',
        'weight',
        'dimensions',
    ];

    /**
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * @var bool
     */
    public $incrementing = true;
    /**
     * Ensure timestamps are automatically handled
     */
    public $timestamps = true;

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'float',
        'cost' => 'float',
        'tax' => 'float',
        'is_active' => 'boolean',
        'weight' => 'float',
        'dimensions' => 'array',
        'inventory_tracking_type' => 'string',
    ];

    /**
     * Get the activity log options for the model.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            // Chỉ log các trường cơ bản, tránh gọi các phương thức có thể gây ra lỗi
            ->logOnly(['id', 'name', 'code', 'description', 'category_id', 'brand_id', 'unit', 'price', 'cost', 'tax', 'is_active', 'inventory_tracking_type'])
            ->logOnlyDirty()
            ->dontLogIfAttributesChangedOnly(['updated_at'])
            ->dontSubmitEmptyLogs();
    }


    /**
     * Get the category that owns the product.
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the brand that owns the product.
     */
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the inventory items for the product.
     */
    public function inventoryItems()
    {
        return $this->hasMany(InventoryItem::class);
    }

    /**
     * Get the inventory transactions for the product.
     */
    public function inventoryTransactions()
    {
        return $this->hasMany(InventoryTransaction::class);
    }

    /**
     * Get the product attributes for the product.
     */
    public function productAttributes()
    {
        return $this->hasMany(ProductAttribute::class)->withoutGlobalScopes();
    }

    /**
     * Get the attributes for the product.
     */
    public function attributes()
    {
        return $this->belongsToMany(Attribute::class, 'product_attributes')
            ->withPivot('attribute_value_id', 'text_value')
            ->withTimestamps();
    }

    /**
     * Get the attribute values for the product.
     */
    public function attributeValues()
    {
        return $this->belongsToMany(AttributeValue::class, 'product_attributes', 'product_id', 'attribute_value_id')
            ->withPivot('attribute_id')
            ->withTimestamps();
    }

    /**
     * Get the serials for the product.
     */
    public function serials()
    {
        return $this->hasMany(ProductSerial::class);
    }

    /**
     * Scope a query to only include active products.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Check if the product is tracked by serial.
     */
    public function isTrackedBySerial()
    {
        return $this->inventory_tracking_type === 'serial';
    }

    /**
     * Check if the product is tracked by batch.
     */
    public function isTrackedByBatch()
    {
        return $this->inventory_tracking_type === 'batch';
    }

    /**
     * Check if the product is tracked by quantity only.
     */
    public function isTrackedByQuantity()
    {
        return $this->inventory_tracking_type === 'quantity';
    }

    /**
     * Get the batches for the product.
     */
    public function batches()
    {
        return $this->hasMany(ProductBatch::class);
    }

    /**
     * Get active batches for the product.
     */
    public function activeBatches()
    {
        return $this->batches()->where('status', 'active');
    }
}
