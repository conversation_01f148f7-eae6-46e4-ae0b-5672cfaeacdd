<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class PurchaseOrder extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'warehouse_id',
        'supplier_id',
        'expected_date',
        'currency',
        'status',
        'notes',
        'invoice_number',
        'invoice_series',
        'invoice_template',
        'invoice_date',
        'has_documents',
        'total_amount',
        'created_by',
        'approved_by',
        'approved_at',
        'received_by',
        'received_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expected_date' => 'date',
        'invoice_date' => 'date',
        'approved_at' => 'datetime',
        'received_at' => 'datetime',
        'total_amount' => 'float',
        'has_documents' => 'boolean',
    ];

    /**
     * Status constants
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_PARTIALLY_RECEIVED = 'partially_received';
    const STATUS_RECEIVED = 'received';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Get the activity log options for the model.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['code', 'warehouse_id', 'supplier_id', 'expected_date', 'status', 'notes', 'total_amount'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the warehouse that owns the purchase order.
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * Get the supplier that owns the purchase order.
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the user who created the purchase order.
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who approved the purchase order.
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the user who received the purchase order.
     */
    public function receivedBy()
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    /**
     * Get the items for the purchase order.
     */
    public function items()
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    /**
     * Get the attachments for the purchase order.
     */
    public function attachments()
    {
        return $this->hasMany(PurchaseOrderAttachment::class);
    }

    /**
     * Get the good receipts for the purchase order.
     */
    public function goodReceipts()
    {
        return $this->hasMany(GoodReceipt::class);
    }

    /**
     * Get the status text attribute.
     */
    public function getStatusTextAttribute()
    {
        switch ($this->status) {
            case self::STATUS_DRAFT:
                return 'Nháp';
            case self::STATUS_PENDING:
                return 'Chờ duyệt';
            case self::STATUS_APPROVED:
                return 'Đã duyệt';
            case self::STATUS_REJECTED:
                return 'Từ chối';
            case self::STATUS_PARTIALLY_RECEIVED:
                return 'Nhập một phần';
            case self::STATUS_RECEIVED:
                return 'Đã nhập kho';
            case self::STATUS_CANCELLED:
                return 'Đã hủy';
            default:
                return 'Không xác định';
        }
    }

    /**
     * Get the status color attribute.
     */
    public function getStatusColorAttribute()
    {
        switch ($this->status) {
            case self::STATUS_DRAFT:
                return 'secondary';
            case self::STATUS_PENDING:
                return 'warning';
            case self::STATUS_APPROVED:
                return 'info';
            case self::STATUS_REJECTED:
                return 'danger';
            case self::STATUS_PARTIALLY_RECEIVED:
                return 'primary';
            case self::STATUS_RECEIVED:
                return 'success';
            case self::STATUS_CANCELLED:
                return 'dark';
            default:
                return 'secondary';
        }
    }

    /**
     * Scope a query to only include draft purchase orders.
     */
    public function scopeDraft($query)
    {
        return $query->where('status', self::STATUS_DRAFT);
    }

    /**
     * Scope a query to only include pending purchase orders.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope a query to only include approved purchase orders.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope a query to only include rejected purchase orders.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', self::STATUS_REJECTED);
    }

    /**
     * Scope a query to only include partially received purchase orders.
     */
    public function scopePartiallyReceived($query)
    {
        return $query->where('status', self::STATUS_PARTIALLY_RECEIVED);
    }

    /**
     * Scope a query to only include received purchase orders.
     */
    public function scopeReceived($query)
    {
        return $query->where('status', self::STATUS_RECEIVED);
    }

    /**
     * Scope a query to only include cancelled purchase orders.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', self::STATUS_CANCELLED);
    }

    /**
     * Check if the purchase order can be edited.
     */
    public function canBeEdited()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PENDING]);
    }

    /**
     * Check if the purchase order can be submitted for approval.
     */
    public function canBeSubmitted()
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * Check if the purchase order can be approved.
     */
    public function canBeApproved()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the purchase order can be rejected.
     */
    public function canBeRejected()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the purchase order can be cancelled.
     */
    public function canBeCancelled()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PENDING, self::STATUS_APPROVED]);
    }

    /**
     * Check if the purchase order can be received.
     */
    public function canBeReceived()
    {
        return in_array($this->status, [self::STATUS_APPROVED, self::STATUS_PARTIALLY_RECEIVED]);
    }

    /**
     * Check if the purchase order has invoice and documents.
     */
    public function hasInvoiceAndDocuments()
    {
        return $this->has_documents;
    }

    /**
     * Check if the purchase order can manage attachments.
     */
    public function canManageAttachments()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PENDING, self::STATUS_APPROVED]);
    }

    /**
     * Submit the purchase order for approval.
     */
    public function submit()
    {
        if ($this->status !== self::STATUS_DRAFT) {
            throw new \Exception('Chỉ có thể gửi phiếu nhập hàng ở trạng thái nháp.');
        }

        $this->status = self::STATUS_PENDING;
        $this->save();

        return $this;
    }

    /**
     * Approve the purchase order.
     */
    public function approve($userId)
    {
        if (!$this->canBeApproved()) {
            throw new \Exception('Không thể duyệt phiếu nhập hàng này.');
        }

        $this->status = self::STATUS_APPROVED;
        $this->approved_by = $userId;
        $this->approved_at = now();
        $this->save();

        return $this;
    }

    /**
     * Reject the purchase order.
     */
    public function reject($userId, $reason = null)
    {
        if (!$this->canBeRejected()) {
            throw new \Exception('Không thể từ chối phiếu nhập hàng này.');
        }

        $this->status = self::STATUS_REJECTED;
        $this->notes = $reason ? ($this->notes ? $this->notes . "\n\nLý do từ chối: " . $reason : "Lý do từ chối: " . $reason) : $this->notes;
        $this->save();

        return $this;
    }

    /**
     * Cancel the purchase order.
     */
    public function cancel($reason = null)
    {
        if (!$this->canBeCancelled()) {
            throw new \Exception('Không thể hủy phiếu nhập hàng này.');
        }

        $this->status = self::STATUS_CANCELLED;
        $this->notes = $reason ? ($this->notes ? $this->notes . "\n\nLý do hủy: " . $reason : "Lý do hủy: " . $reason) : $this->notes;
        $this->save();

        return $this;
    }

    /**
     * Update the purchase order status based on received items.
     */
    public function updateStatusBasedOnReceivedItems()
    {
        $items = $this->items;
        $totalQuantity = $items->sum('quantity');
        $receivedQuantity = $items->sum('received_quantity');

        if ($receivedQuantity === 0) {
            return $this;
        }

        if ($receivedQuantity < $totalQuantity) {
            $this->status = self::STATUS_PARTIALLY_RECEIVED;
        } else {
            $this->status = self::STATUS_RECEIVED;
        }

        $this->save();

        return $this;
    }

    /**
     * Calculate the total amount of the purchase order.
     */
    public function calculateTotalAmount()
    {
        $totalAmount = $this->items->sum(function ($item) {
            $subtotalBeforeVAT = $item->quantity * $item->unit_price;
            $vatAmount = $subtotalBeforeVAT * ($item->tax_rate / 100);
            $subtotalAfterVAT = $subtotalBeforeVAT + $vatAmount;
            $finalSubtotal = $subtotalAfterVAT - $item->discount;
            return $finalSubtotal;
        });

        $this->total_amount = $totalAmount;
        $this->save();

        return $this;
    }
}
