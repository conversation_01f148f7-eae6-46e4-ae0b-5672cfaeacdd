<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class PurchaseOrderItem extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'purchase_order_id',
        'product_id',
        'quantity',
        'received_quantity',
        'unit_price',
        'tax_rate',
        'unit_price_after_tax',
        'discount',
        'warehouse_area_id',
        'batch_number',
        'expiry_date',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'float',
        'received_quantity' => 'float',
        'unit_price' => 'float',
        'tax_rate' => 'float',
        'unit_price_after_tax' => 'float',
        'discount' => 'float',
        'expiry_date' => 'date',
    ];

    /**
     * Get the activity log options for the model.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['purchase_order_id', 'product_id', 'quantity', 'received_quantity', 'unit_price', 'warehouse_area_id', 'batch_number', 'expiry_date', 'notes'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the purchase order that owns the item.
     */
    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    /**
     * Get the product that owns the item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the warehouse area that owns the item.
     */
    public function warehouseArea()
    {
        return $this->belongsTo(WarehouseArea::class);
    }

    /**
     * Get the serials for the purchase order item.
     */
    public function serials()
    {
        return $this->hasMany(ProductSerial::class);
    }

    /**
     * Get the good receipt item for the purchase order item.
     */
    public function goodReceiptItem()
    {
        return $this->hasOne(GoodReceiptItem::class, 'purchase_order_item_id');
    }

    /**
     * Get the subtotal attribute.
     */
    public function getSubtotalAttribute()
    {
        $subtotalBeforeVAT = $this->quantity * $this->unit_price;
        $vatAmount = $subtotalBeforeVAT * (($this->tax_rate ?? 0) / 100);
        $subtotalAfterVAT = $subtotalBeforeVAT + $vatAmount;
        $itemDiscount = $this->quantity * ($this->discount ?? 0);
        return $subtotalAfterVAT - $itemDiscount;
    }

    /**
     * Get the received subtotal attribute.
     */
    public function getReceivedSubtotalAttribute()
    {
        return $this->received_quantity * $this->unit_price;
    }

    /**
     * Get the remaining quantity attribute.
     */
    public function getRemainingQuantityAttribute()
    {
        return $this->quantity - $this->received_quantity;
    }

    /**
     * Get the status attribute.
     */
    public function getStatusAttribute()
    {
        if ($this->received_quantity === 0) {
            return 'pending';
        } elseif ($this->received_quantity < $this->quantity) {
            return 'partially_received';
        } else {
            return 'received';
        }
    }

    /**
     * Get the status text attribute.
     */
    public function getStatusTextAttribute()
    {
        switch ($this->status) {
            case 'pending':
                return 'Chờ nhập';
            case 'partially_received':
                return 'Nhập một phần';
            case 'received':
                return 'Đã nhập đủ';
            default:
                return 'Không xác định';
        }
    }

    /**
     * Get the status color attribute.
     */
    public function getStatusColorAttribute()
    {
        switch ($this->status) {
            case 'pending':
                return 'warning';
            case 'partially_received':
                return 'info';
            case 'received':
                return 'success';
            default:
                return 'secondary';
        }
    }

    /**
     * Check if the item can be received.
     */
    public function canBeReceived()
    {
        return $this->purchaseOrder->canBeReceived() && $this->received_quantity < $this->quantity;
    }

    /**
     * Receive the item.
     */
    public function receive($quantity, $warehouseAreaId = null, $batchNumber = null, $expiryDate = null)
    {
        if (!$this->canBeReceived()) {
            throw new \Exception('Không thể nhập sản phẩm này.');
        }

        if ($quantity <= 0) {
            throw new \Exception('Số lượng nhập phải lớn hơn 0.');
        }

        if ($this->received_quantity + $quantity > $this->quantity) {
            throw new \Exception('Số lượng nhập vượt quá số lượng đặt hàng.');
        }

        $this->received_quantity += $quantity;

        if ($warehouseAreaId) {
            $this->warehouse_area_id = $warehouseAreaId;
        }

        if ($batchNumber) {
            $this->batch_number = $batchNumber;
        }

        if ($expiryDate) {
            $this->expiry_date = $expiryDate;
        }

        $this->save();

        return $this;
    }
}
