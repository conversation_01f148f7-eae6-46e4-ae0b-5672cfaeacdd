<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class WarehouseReceiptSerial extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'warehouse_transfer_item_id',
        'serial_number',
        'scanned_by',
        'scanned_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'scanned_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Tự động gán người quét và thời gian quét
        static::creating(function ($serial) {
            if (empty($serial->scanned_by) && auth()->check()) {
                $serial->scanned_by = auth()->id();
            }

            if (empty($serial->scanned_at)) {
                $serial->scanned_at = now();
            }
        });
    }

    /**
     * Get the activity log options for the model.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['warehouse_transfer_item_id', 'serial_number', 'scanned_by', 'scanned_at'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the warehouse transfer item that owns the serial.
     */
    public function warehouseTransferItem()
    {
        return $this->belongsTo(WarehouseTransferItem::class);
    }

    /**
     * Get the user who scanned the serial.
     */
    public function scannedBy()
    {
        return $this->belongsTo(User::class, 'scanned_by');
    }
}
