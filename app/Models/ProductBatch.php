<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductBatch extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'batch_number',
        'product_id',
        'warehouse_id',
        'warehouse_area_id',
        'quantity',
        'initial_quantity',
        'manufacturing_date',
        'expiry_date',
        'cost_price',
        'good_receipt_item_id',
        'status',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'manufacturing_date' => 'date',
        'expiry_date' => 'date',
        'quantity' => 'integer',
        'initial_quantity' => 'integer',
        'cost_price' => 'decimal:4',
    ];

    /**
     * Get the product that owns the batch.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the good receipt item that owns the batch.
     */
    public function goodReceiptItem(): BelongsTo
    {
        return $this->belongsTo(GoodReceiptItem::class);
    }

    /**
     * Get the warehouse that owns the batch.
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * Get the warehouse area that owns the batch.
     */
    public function warehouseArea(): BelongsTo
    {
        return $this->belongsTo(WarehouseArea::class);
    }

    /**
     * Get the inventory transactions for the batch.
     */
    public function inventoryTransactions(): HasMany
    {
        return $this->hasMany(InventoryTransaction::class);
    }

    /**
     * Check if the batch is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the batch is expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' ||
               ($this->expiry_date && $this->expiry_date->isPast());
    }

    /**
     * Check if the batch is depleted.
     */
    public function isDepleted(): bool
    {
        return $this->status === 'depleted' || $this->quantity <= 0;
    }

    /**
     * Format the expiry date.
     */
    public function getFormattedExpiryDateAttribute(): string
    {
        return $this->expiry_date ? $this->expiry_date->format('d/m/Y') : '';
    }

    /**
     * Format the manufacturing date.
     */
    public function getFormattedManufacturingDateAttribute(): string
    {
        return $this->manufacturing_date ? $this->manufacturing_date->format('d/m/Y') : '';
    }
}
