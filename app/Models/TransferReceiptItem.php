<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransferReceiptItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'transfer_receipt_id',
        'product_id',
        'quantity',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'float',
    ];

    /**
     * Get the transfer receipt that owns the item.
     */
    public function transferReceipt()
    {
        return $this->belongsTo(TransferReceipt::class);
    }

    /**
     * Get the product that owns the item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the IMEIs for the transfer receipt item.
     */
    public function imeis()
    {
        return $this->hasMany(TransferReceiptImei::class);
    }

    /**
     * Get the batches for the transfer receipt item.
     */
    public function batches()
    {
        return $this->hasMany(TransferReceiptBatch::class);
    }

    /**
     * Get the total scanned IMEI count.
     */
    public function getScannedImeiCountAttribute()
    {
        return $this->imeis()->count();
    }

    /**
     * Get the total batch quantity.
     */
    public function getTotalBatchQuantityAttribute()
    {
        return $this->batches()->sum('quantity');
    }

    /**
     * Check if this item is fully received (for IMEI products).
     */
    public function getIsFullyReceivedAttribute()
    {
        if ($this->product->inventory_tracking_type === 'serial') {
            return $this->scanned_imei_count >= $this->quantity;
        } elseif ($this->product->inventory_tracking_type === 'batch') {
            return $this->total_batch_quantity >= $this->quantity;
        }

        return true; // For quantity-based products
    }
}
