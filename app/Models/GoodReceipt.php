<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class GoodReceipt extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * Các trạng thái của phiếu nhập kho
     */
    const STATUS_DRAFT = 'draft'; // Trạng thái nháp, mới tạo
    const STATUS_PENDING = 'pending'; // Chờ duyệt
    const STATUS_COMPLETED = 'completed'; // Đã duyệt
    const STATUS_CANCELLED = 'cancelled'; // Đã hủy

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'purchase_order_id',
        'warehouse_id',
        'status',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
        'has_documents',
        'invoice_number',
        'invoice_series',
        'invoice_template',
        'invoice_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'approved_at' => 'datetime',
        'invoice_date' => 'date',
        'has_documents' => 'boolean',
    ];

    /**
     * Get the activity log options for the model.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['code', 'purchase_order_id', 'warehouse_id', 'status', 'notes', 'created_by'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the warehouse that owns the good receipt.
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * Get the purchase order that owns the good receipt.
     */
    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    /**
     * Get the user who created the good receipt.
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the items for the good receipt.
     */
    public function items()
    {
        return $this->hasMany(GoodReceiptItem::class);
    }

    /**
     * Get the status text attribute.
     */
    public function getStatusTextAttribute()
    {
        return match ($this->status) {
            self::STATUS_DRAFT => 'Nháp',
            self::STATUS_PENDING => 'Chờ duyệt',
            self::STATUS_COMPLETED => 'Đã duyệt',
            self::STATUS_CANCELLED => 'Đã hủy',
            default => 'Không xác định',
        };
    }

    /**
     * Get the status color attribute.
     */
    public function getStatusColorAttribute()
    {
        return match ($this->status) {
            self::STATUS_DRAFT => 'secondary',
            self::STATUS_PENDING => 'warning',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_CANCELLED => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Calculate total amount of the good receipt.
     */
    public function calculateTotalAmount()
    {
        $totalAmount = $this->items->sum(function ($item) {
            $subtotalBeforeVAT = $item->quantity * $item->unit_price;
            $vatAmount = $subtotalBeforeVAT * (($item->tax_rate ?? 0) / 100);
            $subtotalAfterVAT = $subtotalBeforeVAT + $vatAmount;
            $itemDiscount = $item->quantity * ($item->discount ?? 0);
            $finalSubtotal = $subtotalAfterVAT - $itemDiscount;
            return $finalSubtotal;
        });

        return $totalAmount;
    }

    /**
     * Get the total amount attribute.
     */
    public function getTotalAmountAttribute()
    {
        return $this->calculateTotalAmount();
    }

    /**
     * Check if the good receipt can be cancelled.
     */
    public function canBeCancelled()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PENDING, self::STATUS_COMPLETED]);
    }

    /**
     * Cancel the good receipt.
     */
    public function cancel($reason = null)
    {
        if (!$this->canBeCancelled()) {
            throw new \Exception('Không thể hủy phiếu nhập kho này.');
        }

        $this->status = self::STATUS_CANCELLED;
        $this->notes = $reason ? ($this->notes ? $this->notes . "\n\nLý do hủy: " . $reason : "Lý do hủy: " . $reason) : $this->notes;
        $this->save();

        return $this;
    }

    /**
     * Check if the good receipt can be submitted for approval.
     */
    public function canSubmit()
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * Submit the good receipt for approval.
     */
    public function submit()
    {
        if (!$this->canSubmit()) {
            throw new \Exception('Không thể gửi yêu cầu duyệt phiếu nhập kho này.');
        }

        $this->status = self::STATUS_PENDING;
        $this->save();

        return $this;
    }

    /**
     * Check if the good receipt can be approved.
     */
    public function canApprove()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Approve the good receipt.
     */
    public function approve($userId)
    {
        if (!$this->canApprove()) {
            throw new \Exception('Không thể duyệt phiếu nhập kho này.');
        }

        $this->status = self::STATUS_COMPLETED;
        $this->approved_by = $userId;
        $this->approved_at = now();
        $this->save();

        return $this;
    }

    /**
     * Get the user who approved the good receipt.
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Check if the good receipt has all required documents.
     */
    public function hasRequiredDocuments()
    {
        return $this->has_documents;
    }

    /**
     * Get the attachments for the good receipt.
     */
    public function attachments()
    {
        return $this->hasMany(GoodReceiptAttachment::class);
    }

    /**
     * Get the invoices for the good receipt.
     */
    public function invoices()
    {
        return $this->hasMany(GoodReceiptInvoice::class);
    }
}
