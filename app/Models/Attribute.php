<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Attribute extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'display_name',
        'description',
        'type',
        'is_required',
        'is_filterable',
        'is_active',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_required' => 'boolean',
        'is_filterable' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the activity log options for the model.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'code', 'display_name', 'description', 'type', 'is_required', 'is_filterable', 'is_active', 'sort_order'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the values for the attribute.
     */
    public function values()
    {
        return $this->hasMany(AttributeValue::class);
    }

    /**
     * Get the active values for the attribute.
     */
    public function activeValues()
    {
        return $this->hasMany(AttributeValue::class)->where('is_active', true)->orderBy('sort_order');
    }

    /**
     * Get the products that have this attribute.
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'product_attributes')
            ->withPivot('attribute_value_id', 'text_value')
            ->withTimestamps();
    }

    /**
     * Scope a query to only include active attributes.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include filterable attributes.
     */
    public function scopeFilterable($query)
    {
        return $query->where('is_filterable', true);
    }

    /**
     * Check if the attribute is of type select.
     */
    public function isSelect()
    {
        return $this->type === 'select';
    }

    /**
     * Check if the attribute is of type text.
     */
    public function isText()
    {
        return $this->type === 'text';
    }

    /**
     * Check if the attribute is of type number.
     */
    public function isNumber()
    {
        return $this->type === 'number';
    }

    /**
     * Check if the attribute is of type boolean.
     */
    public function isBoolean()
    {
        return $this->type === 'boolean';
    }

    /**
     * Check if the attribute is of type date.
     */
    public function isDate()
    {
        return $this->type === 'date';
    }
}
