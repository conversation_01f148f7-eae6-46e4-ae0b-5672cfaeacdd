<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class PurchaseOrderAttachment extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'purchase_order_id',
        'file_name',
        'file_path',
        'file_type',
        'file_size',
        'uploaded_by',
    ];

    /**
     * Get the activity log options for the model.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['purchase_order_id', 'file_name', 'file_path', 'file_type', 'file_size', 'uploaded_by'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the purchase order that owns the attachment.
     */
    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    /**
     * Get the user who uploaded the attachment.
     */
    public function uploadedBy()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the file size in human readable format.
     */
    public function getFileSizeHumanAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the file icon based on file type.
     */
    public function getFileIconAttribute()
    {
        $fileType = $this->file_type;

        if (str_contains($fileType, 'image')) {
            return 'ri-image-line';
        } elseif (str_contains($fileType, 'pdf')) {
            return 'ri-file-pdf-line';
        } elseif (str_contains($fileType, 'word') || str_contains($fileType, 'document')) {
            return 'ri-file-word-line';
        } elseif (str_contains($fileType, 'excel') || str_contains($fileType, 'spreadsheet')) {
            return 'ri-file-excel-line';
        } elseif (str_contains($fileType, 'powerpoint') || str_contains($fileType, 'presentation')) {
            return 'ri-file-ppt-line';
        } elseif (str_contains($fileType, 'zip') || str_contains($fileType, 'rar') || str_contains($fileType, 'archive')) {
            return 'ri-file-zip-line';
        } elseif (str_contains($fileType, 'text')) {
            return 'ri-file-text-line';
        } else {
            return 'ri-file-line';
        }
    }
}
