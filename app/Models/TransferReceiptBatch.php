<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransferReceiptBatch extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'transfer_receipt_item_id',
        'batch_number',
        'quantity',
        'expiry_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'float',
        'expiry_date' => 'date',
    ];

    /**
     * Get the transfer receipt item that owns the batch.
     */
    public function transferReceiptItem()
    {
        return $this->belongsTo(TransferReceiptItem::class);
    }
}
