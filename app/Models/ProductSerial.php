<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class ProductSerial extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_id',
        'serial_number',
        'inventory_item_id',
        'purchase_order_item_id', // Sẽ bỏ qua nếu cột không tồn tại
        'good_receipt_item_id', // Sẽ bỏ qua nếu cột không tồn tại
        'warehouse_id',
        'warehouse_area_id',
        'status',
        'notes',
    ];

    /**
     * The attributes that should be guarded against mass assignment.
     *
     * @var array<int, string>
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'string',
    ];

    /**
     * Get the activity log options for the model.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['product_id', 'serial_number', 'inventory_item_id', 'purchase_order_item_id', 'good_receipt_item_id', 'warehouse_id', 'warehouse_area_id', 'status', 'notes'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the product that owns the serial.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the inventory item that owns the serial.
     */
    public function inventoryItem()
    {
        return $this->belongsTo(InventoryItem::class);
    }

    /**
     * Get the purchase order item that owns the serial.
     */
    public function purchaseOrderItem()
    {
        return $this->belongsTo(PurchaseOrderItem::class);
    }

    /**
     * Get the good receipt item that owns the serial.
     */
    public function goodReceiptItem()
    {
        return $this->belongsTo(GoodReceiptItem::class);
    }

    /**
     * Get the warehouse that owns the serial.
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * Get the warehouse area that owns the serial.
     */
    public function warehouseArea()
    {
        return $this->belongsTo(WarehouseArea::class);
    }

    /**
     * Các trạng thái của serial
     */
    const STATUS_IN_STOCK = 'in_stock';
    const STATUS_SOLD = 'sold';
    const STATUS_TRANSFERRED = 'transferred';
    const STATUS_RETURNED = 'returned';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_PENDING = 'pending';

    /**
     * Scope a query to only include in stock serials.
     */
    public function scopeInStock($query)
    {
        return $query->where('status', self::STATUS_IN_STOCK);
    }

    /**
     * Scope a query to only include sold serials.
     */
    public function scopeSold($query)
    {
        return $query->where('status', self::STATUS_SOLD);
    }

    /**
     * Scope a query to only include transferred serials.
     */
    public function scopeTransferred($query)
    {
        return $query->where('status', self::STATUS_TRANSFERRED);
    }

    /**
     * Scope a query to only include returned serials.
     */
    public function scopeReturned($query)
    {
        return $query->where('status', self::STATUS_RETURNED);
    }

    /**
     * Scope a query to only include cancelled serials.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', self::STATUS_CANCELLED);
    }

    /**
     * Scope a query to only include pending serials.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }
}
