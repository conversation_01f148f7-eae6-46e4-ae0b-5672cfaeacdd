<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransferOrderItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'transfer_order_id',
        'product_id',
        'quantity',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'float',
    ];

    /**
     * Get the transfer order that owns the item.
     */
    public function transferOrder()
    {
        return $this->belongsTo(TransferOrder::class);
    }

    /**
     * Get the product that owns the item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the IMEIs for the transfer order item.
     */
    public function imeis()
    {
        return $this->hasMany(TransferOrderImei::class);
    }

    /**
     * Get the batches for the transfer order item.
     */
    public function batches()
    {
        return $this->hasMany(TransferOrderBatch::class);
    }

    /**
     * Get the total scanned IMEI count.
     */
    public function getScannedImeiCountAttribute()
    {
        return $this->imeis()->count();
    }

    /**
     * Get the total batch quantity.
     */
    public function getTotalBatchQuantityAttribute()
    {
        return $this->batches()->sum('quantity');
    }

    /**
     * Check if this item is fully prepared (for IMEI/batch products).
     */
    public function getIsFullyPreparedAttribute()
    {
        if ($this->product->inventory_tracking_type === 'serial') {
            return $this->scanned_imei_count >= $this->quantity;
        } elseif ($this->product->inventory_tracking_type === 'batch') {
            return $this->total_batch_quantity >= $this->quantity;
        }

        return true; // For quantity-based products
    }
}
