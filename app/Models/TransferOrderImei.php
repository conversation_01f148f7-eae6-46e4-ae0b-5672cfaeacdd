<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransferOrderImei extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'transfer_order_item_id',
        'imei',
        'scanned_by',
        'scanned_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'scanned_at' => 'datetime',
    ];

    /**
     * Get the transfer order item that owns the IMEI.
     */
    public function transferOrderItem()
    {
        return $this->belongsTo(TransferOrderItem::class);
    }

    /**
     * Get the user who scanned the IMEI.
     */
    public function scannedBy()
    {
        return $this->belongsTo(User::class, 'scanned_by');
    }
}
