<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class TransferOrder extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'transfer_request_id',
        'from_warehouse_id',
        'to_warehouse_id',
        'transit_warehouse_id',
        'status',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'approved_at' => 'datetime',
    ];

    /**
     * Status constants
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Get the activity log options for the model.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['code', 'transfer_request_id', 'from_warehouse_id', 'to_warehouse_id', 'transit_warehouse_id', 'status', 'notes'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the transfer request that owns the transfer order.
     */
    public function transferRequest()
    {
        return $this->belongsTo(TransferRequest::class);
    }

    /**
     * Get the from warehouse that owns the transfer order.
     */
    public function fromWarehouse()
    {
        return $this->belongsTo(Warehouse::class, 'from_warehouse_id');
    }

    /**
     * Get the to warehouse that owns the transfer order.
     */
    public function toWarehouse()
    {
        return $this->belongsTo(Warehouse::class, 'to_warehouse_id');
    }

    /**
     * Get the transit warehouse that owns the transfer order.
     */
    public function transitWarehouse()
    {
        return $this->belongsTo(Warehouse::class, 'transit_warehouse_id');
    }

    /**
     * Get the user that created the transfer order.
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user that approved the transfer order.
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the items for the transfer order.
     */
    public function items()
    {
        return $this->hasMany(TransferOrderItem::class);
    }

    /**
     * Get the transfer receipt for this order.
     */
    public function transferReceipt()
    {
        return $this->hasOne(TransferReceipt::class);
    }

    /**
     * Scope a query to only include draft transfer orders.
     */
    public function scopeDraft($query)
    {
        return $query->where('status', self::STATUS_DRAFT);
    }

    /**
     * Scope a query to only include pending transfer orders.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope a query to only include approved transfer orders.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope a query to only include rejected transfer orders.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', self::STATUS_REJECTED);
    }

    /**
     * Scope a query to only include cancelled transfer orders.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', self::STATUS_CANCELLED);
    }

    /**
     * Check if the transfer order can be edited.
     */
    public function canBeEdited()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PENDING]);
    }

    /**
     * Check if the transfer order can be submitted for approval.
     */
    public function canBeSubmitted()
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * Check if the transfer order can be approved.
     */
    public function canBeApproved()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the transfer order can be rejected.
     */
    public function canBeRejected()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the transfer order can be cancelled.
     */
    public function canBeCancelled()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PENDING]);
    }

    /**
     * Get the status text.
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            self::STATUS_DRAFT => 'Nháp',
            self::STATUS_PENDING => 'Chờ duyệt',
            self::STATUS_APPROVED => 'Đã duyệt',
            self::STATUS_REJECTED => 'Từ chối',
            self::STATUS_CANCELLED => 'Đã hủy',
            default => 'Không xác định',
        };
    }

    /**
     * Get the status badge class.
     */
    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            self::STATUS_DRAFT => 'bg-label-secondary',
            self::STATUS_PENDING => 'bg-label-warning',
            self::STATUS_APPROVED => 'bg-label-success',
            self::STATUS_REJECTED => 'bg-label-danger',
            self::STATUS_CANCELLED => 'bg-label-dark',
            default => 'bg-label-secondary',
        };
    }

    /**
     * Generate unique code for transfer order.
     */
    public static function generateCode()
    {
        $prefix = 'TO';
        $date = now()->format('Ymd');
        $lastOrder = self::whereDate('created_at', now())
            ->where('code', 'like', $prefix . $date . '%')
            ->orderBy('code', 'desc')
            ->first();

        if ($lastOrder) {
            $lastNumber = (int) substr($lastOrder->code, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $date . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
