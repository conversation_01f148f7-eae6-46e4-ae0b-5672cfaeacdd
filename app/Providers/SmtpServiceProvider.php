<?php

namespace App\Providers;

use App\Contracts\SettingsInterface;
use App\Repositories\SettingsRepository;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class SmtpServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(SettingsInterface::class, SettingsRepository::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(SettingsRepository $settingsRepository): void
    {
        if (Schema::hasTable('settings')) {
            $smtp = $settingsRepository->getSmtp();
            if ($smtp) {
                config(['mail.mailers.smtp' => [
                    'transport' => 'smtp',
                    'host' => $smtp->smtp_host,
                    'port' => $smtp->smtp_port,
                    'encryption' => $smtp->smtp_encryption,
                    'username' => $smtp->smtp_username,
                    'password' => $smtp->smtp_password,
                    'timeout' => null,
                    'auth_mode' => null,
                ],
                    'mail.from' => [
                        'address' => $smtp->smtp_sender_email,
                        'name' => $smtp->smtp_sender_name
                    ]]);
            }
        }

    }
}
