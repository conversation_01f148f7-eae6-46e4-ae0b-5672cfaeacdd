<?php

namespace App\Providers;

use App\Helpers\Classes\Helper;
use App\Services\MemoryLimit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Vite;
use Mcamara\LaravelLocalization\Traits\LoadsTranslatedCachedRoutes;
use Spatie\Health\Checks\Checks\DatabaseCheck;
use Spatie\Health\Checks\Checks\DebugModeCheck;
use Spatie\Health\Checks\Checks\EnvironmentCheck;
use Spatie\Health\Facades\Health;

class AppServiceProvider extends ServiceProvider
{
  use LoadsTranslatedCachedRoutes;
  /**
   * Register any application services.
   */
  public function register(): void
  {
    // Register repositories
    $this->app->bind(\App\Contracts\PurchaseOrderInterface::class, \App\Repositories\PurchaseOrderRepository::class);
    $this->app->bind(\App\Contracts\GoodReceiptInterface::class, \App\Repositories\GoodReceiptRepository::class);
    $this->app->bind(\App\Contracts\TransferRequestInterface::class, \App\Repositories\TransferRequestRepository::class);
    $this->app->bind(\App\Contracts\TransferOrderInterface::class, \App\Repositories\TransferOrderRepository::class);
    $this->app->bind(\App\Contracts\TransferReceiptInterface::class, \App\Repositories\TransferReceiptRepository::class);
  }

  /**
   * Bootstrap any application services.
   */
  /**
   * Khởi động ứng dụng.
   *
   * Hàm này chủ yếu được dùng để cấu hình và khởi tạo các thiết lập và tính năng của ứng dụng.
   */
  public function boot(): void
  {
    // Set memory limit from .env
    if (env('MEMORY_LIMIT')) {
      setServerMemoryLimit(env('MEMORY_LIMIT'));
    }

    // Cấu hình thuộc tính thẻ style của Vite
    Vite::useStyleTagAttributes(function (?string $src, string $url, ?array $chunk, ?array $manifest) {
      // Đặt tên lớp CSS dựa trên đường dẫn nguồn
      if ($src !== null) {
        return [
          'class' => preg_match("/(resources\/assets\/vendor\/scss\/(rtl\/)?core)-?.*/i", $src) ? 'template-customizer-core-css' :
            (preg_match("/(resources\/assets\/vendor\/scss\/(rtl\/)?theme)-?.*/i", $src) ? 'template-customizer-theme-css' : '')
        ];
      }
      return [];
    });

    // Định nghĩa ngôn ngữ mặc định cho Laravel Localization
    RouteServiceProvider::loadCachedRoutesUsing(fn() => $this->loadCachedRoutes());

    // Kiểm tra trạng thái kết nối cơ sở dữ liệu
    $dbConnectionStatus = Helper::dbConnectionStatus();

    // Nếu kết nối cơ sở dữ liệu thành công
    if ($dbConnectionStatus) {
      // Đặt độ dài mặc định của trường trong cơ sở dữ liệu
      Schema::defaultStringLength(191);
      // Thực hiện các tác vụ chạy nền
      $this->jobRuns();
    }
    // Ép ứng dụng sử dụng giao thức HTTPS
    $this->forceSchemeHttps();

    // Đặt đường dẫn ngôn ngữ cho ứng dụng
    app()->useLangPath(
      base_path('lang')
    );

    // Cấu hình cơ chế kiểm tra sức khỏe ứng dụng
    Health::checks([
      DebugModeCheck::new(),
      EnvironmentCheck::new(),
      DatabaseCheck::new(),
      // UsedDiskSpaceCheck::new(),
      MemoryLimit::new(),
    ]);

    Gate::before(function ($user, $ability) {
      // Allow super_admin role to bypass all permission checks
      return $user->hasRole('super_admin') || $user->isSuperAdmin() ? true : null;
    });


  }

  /**
   * Thực hiện logic chạy công việc
   * Nếu bảng jobs tồn tại, thì cập nhật các công việc không phải trong hàng đợi mặc định thành hàng đợi mặc định và thực hiện một lần công việc hàng đợi
   *
   * @return void
   */
  public function jobRuns(): void
  {
    // Kiểm tra xem bảng jobs có tồn tại hay không
    if (Schema::hasTable('jobs')) {
      // Lấy tất cả các công việc có id lớn hơn 0
      $wordlist = DB::table('jobs')->where('id', '>', 0)->get();

      // Nếu danh sách công việc không trống
      if (count($wordlist) > 0) {
        // Đổi mỗi công việc không phải hàng đợi mặc định thành hàng đợi mặc định
        DB::table('jobs')
          ->where('queue', '<>', 'default')
          ->update(['queue' => 'default']);

        // Thực hiện một lần công việc hàng đợi
        Artisan::call('queue:work --once');
      }
    }
  }


  public function forceSchemeHttps(): void
  {
    if ($this->app->environment('production')) {
      URL::forceScheme('https');
    }
  }
}