<?php

namespace App\Providers;

use App\Enums\Permissions;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\PermissionRegistrar;

class PermissionServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Skip in console to avoid performance issues during migrations
        // Use permissions:sync command instead for console operations
        if ($this->app->runningInConsole() && !$this->app->runningUnitTests()) {
            return;
        }

        // Skip in testing environment unless specifically testing permissions
        if ($this->app->environment('testing') && !$this->app->runningUnitTests()) {
            return;
        }

        // Auto-sync permissions in web environment
        $this->syncPermissions();
    }

    /**
     * Synchronize permissions from enum to database
     */
    protected function syncPermissions(): void
    {
        try {
            // Get all permissions from enum
            $enumPermissions = Permissions::getAll();

            // Get all permissions from database
            $dbPermissions = Permission::all()->pluck('name')->toArray();

            // Find permissions to add (in enum but not in database)
            $permissionsToAdd = array_diff($enumPermissions, $dbPermissions);

            // Add new permissions
            if (count($permissionsToAdd) > 0) {
                foreach ($permissionsToAdd as $permission) {
                    Permission::create(['name' => $permission, 'guard_name' => 'web']);
                }

                // Clear permission cache
                app(PermissionRegistrar::class)->forgetCachedPermissions();
            }
        } catch (\Exception $e) {
            // Log error but don't crash the application
            Log::error('Error syncing permissions: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
