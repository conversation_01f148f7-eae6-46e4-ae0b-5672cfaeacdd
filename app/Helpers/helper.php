<?php

use App\Models\Settings;
use \Illuminate\Support\Facades\Log;
/**
 * Hàm tạo ảnh thu nhỏ (thumbnail)
 *
 * @param string $source_file Đường dẫn hoặc URL của hình ảnh gốc
 * @param int $max_width Chiều rộng tối đa của ảnh thu nhỏ, mặc định là 450
 * @param int $max_height Chiều cao tối đa của ảnh thu nhỏ, mặc định là 450
 * @param int $quality Chất lượng của hình ảnh xuất ra, mặc định là 80
 *
 * @return string URL của ảnh thu nhỏ, nếu không thể tạo thành công thì trả về đường dẫn hoặc URL hình ảnh gốc hoặc false
 */
function ThumbImage($source_file, $max_width = 450, $max_height = 450, $quality = 80)
{
  // Kiểm tra tính năng tạo thumbnail đã bị vô hiệu hóa hay không, hoặc nguồn là một URL trực tuyến
  if (setting('image_thumbnail') == 0 || \Illuminate\Support\Str::contains($source_file, 'http')) {
    return $source_file;
  }

  // Lấy đường dẫn tuyệt đối của tệp hình ảnh nguồn
  $source_file = public_path($source_file);
  // Đặt đĩa lưu trữ là 'thumbs'
  $disk = 'thumbs';
  // Thư mục đích, ở đây để trống vì đã thiết lập thư mục gốc trong cấu hình đĩa
  $dst_dir = '';
  // Nếu thư mục đích không tồn tại, thì tạo nó
  if (!Storage::disk($disk)->exists($dst_dir)) {
    Storage::disk($disk)->makeDirectory($dst_dir, $mode = 7777, true, true);
  }

  // Lấy tên tệp từ nguồn
  $dst_file_name = basename($source_file);
  // Xác định xem nguồn có phải là URL không
  $is_url = filter_var($source_file, FILTER_VALIDATE_URL);
  if ($is_url) {
    // Phân tích URL để lấy phần mở rộng
    $url_parts = pathinfo($source_file);
    $extension = strtolower($url_parts['extension']);
    // Bảng ánh xạ kiểu MIME
    $mime_map = [
      'jpg' => 'image/jpeg',
      'jpeg' => 'image/jpeg',
      'png' => 'image/png',
      'gif' => 'image/gif',
    ];
    // Lấy kiểu MIME dựa trên phần mở rộng
    if (array_key_exists($extension, $mime_map)) {
      $mime = $mime_map[$extension];
    } else {
      $mime = mime_content_type($source_file);
    }
    // Thêm phần mở rộng vào tên tệp
    $dst_file_name .= '.' . $extension;
  } else {
    // Kiểm tra xem tệp có tồn tại hay không
    if (!file_exists($source_file)) {
      return $source_file;
    }

    // Lấy kích thước và kiểu MIME của hình ảnh
    $imgsize = getimagesize($source_file);
    $width = $imgsize[0];
    $height = $imgsize[1];
    $mime = $imgsize['mime'];
  }
  // Nếu ảnh thu nhỏ đã tồn tại, trả về URL của nó
  if (Storage::disk($disk)->exists($dst_file_name)) {
    return Storage::disk($disk)->url($dst_file_name);
  } else {
    // Nếu nguồn là URL, tạo tệp tạm và tải nội dung xuống
    if ($is_url) {
      $temp_source_file = tempnam(sys_get_temp_dir(), 'thumb');
      file_put_contents($temp_source_file, file_get_contents($source_file));
      $source_file = $temp_source_file;
    }
    // Lấy đường dẫn đầy đủ đến tệp đích
    $dst_dir = Storage::disk($disk)->path($dst_dir . $dst_file_name);
    // Chọn phương thức xử lý hình ảnh dựa trên kiểu MIME
    switch ($mime) {
      case 'image/gif':
        $image_create = 'imagecreatefromgif';
        $image = 'imagegif';

        break;

      case 'image/png':
        $image_create = 'imagecreatefrompng';
        $image = 'imagepng';
        $quality = 7;

        break;

      case 'image/jpeg':
        $image_create = 'imagecreatefromjpeg';
        $image = 'imagejpeg';
        $quality = 80;

        break;

      default:
        // Xử lý các loại tập tin không hỗ trợ hoặc kiểu MIME không hợp lệ
        if ($is_url) {
          unlink($source_file);
        }

        return false;

        break;
    }

    // Tạo tài nguyên hình ảnh mới
    $dst_img = imagecreatetruecolor($max_width, $max_height);
    $src_img = $image_create($source_file);

    // Lấy chiều rộng và chiều cao của hình ảnh gốc
    $width = imagesx($src_img);
    $height = imagesy($src_img);

    // Tính toán chiều rộng và chiều cao mới
    $width_new = $height * $max_width / $max_height;
    $height_new = $width * $max_height / $max_width;

    // Cắt và tái mẫu hình ảnh dựa trên chiều rộng hoặc chiều cao mới
    if ($width_new > $width) {
      $h_point = (($height - $height_new) / 2);
      imagecopyresampled($dst_img, $src_img, 0, 0, 0, $h_point, $max_width, $max_height, $width, $height_new);
    } else {
      $w_point = (($width - $width_new) / 2);
      imagecopyresampled($dst_img, $src_img, 0, 0, $w_point, 0, $max_width, $max_height, $width_new, $height);
    }
    // Lưu hình ảnh
    $image($dst_img, $dst_dir, $quality);

    // Nếu nguồn là URL và tệp tạm tồn tại, xóa tệp tạm
    if ($is_url && file_exists($source_file)) {
      unlink($source_file);
    }

    // Giải phóng tài nguyên hình ảnh
    if ($dst_img) {
      imagedestroy($dst_img);
    }
    if ($src_img) {
      imagedestroy($src_img);
    }

    // Trả về URL của ảnh thu nhỏ
    return Storage::disk($disk)->url($dst_file_name);
  }
}

/**
 * Xóa tất cả các tệp và thư mục con trong thư mục ảnh thu nhỏ đã tải lên
 * Hàm này chủ yếu được sử dụng để dọn dẹp các ảnh thu nhỏ không còn cần thiết, nhằm tiết kiệm không gian lưu trữ
 */
function PurgeThumbImages()
{
  // Định nghĩa đường dẫn đến thư mục ảnh thu nhỏ
  $dst_dir = public_path('uploads/thumbnail/');

  try {
    // Cố gắng xóa thư mục ảnh thu nhỏ cùng với toàn bộ nội dung của nó
    \Illuminate\Support\Facades\File::deleteDirectory($dst_dir);
  } catch (\Exception $e) {
    // Nếu có lỗi xảy ra trong quá trình xóa, ghi nhật ký lỗi
    \Log::error($e->getMessage());
  }
}

/**
 * Trích xuất kiểu MIME từ chuỗi dữ liệu
 *
 * Hàm này chủ yếu dùng để phân tích chuỗi dữ liệu hình ảnh được mã hóa Base64 và trích xuất kiểu MIME của nó.
 * Nếu chuỗi dữ liệu bắt đầu theo định dạng URL dữ liệu Base64 hình ảnh cụ thể, hàm sẽ trả về kiểu MIME;
 * nếu không, hàm sẽ trả về null.
 *
 * @param string $data Chuỗi dữ liệu cần phân tích, định dạng mong đợi là "data:image/[a-z]+;base64,"
 * @return string|null Trả về kiểu MIME nếu chuỗi dữ liệu phù hợp với định dạng mong đợi, ngược lại trả về null
 */
function extractMimeType($data)
{
  // Định nghĩa mẫu biểu thức chính quy để khớp với kiểu MIME trong chuỗi dữ liệu
  $pattern = '/^data:(image\/[a-z]+);base64,/';

  // Sử dụng hàm preg_match để kiểm tra xem chuỗi dữ liệu có khớp với mẫu biểu thức chính quy hay không
  // Nếu khớp, mảng $matches sẽ chứa kiểu MIME đã khớp
  if (preg_match($pattern, $data, $matches)) {
    // Nếu khớp, trả về kiểu MIME đã trích xuất
    return $matches[1];
  }

  // Nếu không khớp, trả về null
  return null;
}


/**
 * Trích xuất dữ liệu hình ảnh Base64 từ Data URI
 *
 * Hàm này phân tích chuỗi Data URI đã cho, nếu URI bắt đầu bằng dữ liệu hình ảnh Base64 dạng JPEG hoặc PNG,
 * thì nó sẽ trả về phần dữ liệu hình ảnh. Nếu URI không đúng định dạng mong đợi, hàm sẽ trả về null.
 *
 * @param string $dataURI Data URI cần phân tích
 * @return string|null Dữ liệu hình ảnh Base64 nếu URI đúng định dạng, ngược lại trả về null
 */
function extractBase64ImageData($dataURI)
{
  // Định nghĩa mẫu biểu thức chính quy để khớp với tiền tố của dữ liệu hình ảnh trong Data URI
  $pattern = '/^data:image\/(?:jpeg|png);base64,/';

  // Sử dụng biểu thức chính quy để kiểm tra xem $dataURI có chứa tiền tố hình ảnh mong đợi hay không
  if (preg_match($pattern, $dataURI, $matches)) {
    // Nếu khớp, trả về chuỗi bắt đầu từ sau phần tiền tố khớp, tức là dữ liệu hình ảnh
    return substr($dataURI, strlen($matches[0]));
  }

  // Nếu $dataURI không đúng định dạng mong đợi, trả về null
  return null;
}

/**
 * Định dạng giá
 *
 * Dựa trên phần nguyên của giá, nếu giá bằng chính phần nguyên của nó thì định dạng với 0 chữ số sau dấu phẩy,
 * nếu không thì định dạng với 2 chữ số sau dấu phẩy. Điều này đảm bảo rằng giá trị hiển thị một cách hợp lý và nhất quán.
 *
 * @param float $price Giá chưa được định dạng, có thể là số thực hoặc số nguyên
 * @return string Giá sau khi đã được định dạng: nếu giá ban đầu là số nguyên thì không có chữ số sau dấu phẩy;
 *               nếu giá ban đầu là số thực thì giữ lại 2 chữ số sau dấu phẩy
 */
function formatPrice($price)
{
  // Kiểm tra xem giá có bằng phần nguyên của nó hay không
  if ($price == intval($price)) {
    // Nếu đúng, định dạng với 0 chữ số sau dấu phẩy
    return number_format($price, 0);
  } else {
    // Nếu không, định dạng với 2 chữ số sau dấu phẩy
    return number_format($price, 2);
  }
}

//Settings
function get_settings($attribute)
{
  $settings = Settings::first();
  return $settings->$attribute ?? '';
}

//Memory Limit
function getServerMemoryLimit()
{
  return (int)ini_get('memory_limit');
}

function backgroundColor($status): string
{
  return match ($status) {
    Spatie\Health\Enums\Status::ok()->value => 'border-success',
    Spatie\Health\Enums\Status::warning()->value => 'border-warning',
    Spatie\Health\Enums\Status::skipped()->value => 'border-info',
    Spatie\Health\Enums\Status::failed()->value, Spatie\Health\Enums\Status::crashed()->value => 'border-danger',
    default => 'border-secondary',
  };
}

function textColor($status): string
{
  return match ($status) {
    Spatie\Health\Enums\Status::ok()->value => 'text-success',
    Spatie\Health\Enums\Status::warning()->value => 'text-warning',
    Spatie\Health\Enums\Status::skipped()->value => 'text-info',
    Spatie\Health\Enums\Status::failed()->value, Spatie\Health\Enums\Status::crashed()->value => 'text-danger',
    default => 'text-secondary',
  };
}

function icon($status): string
{
  return match ($status) {
    Spatie\Health\Enums\Status::ok()->value => 'ri-check-line',
    Spatie\Health\Enums\Status::warning()->value => 'ri-error-warning-line',
    Spatie\Health\Enums\Status::skipped()->value => 'ri-arrow-right-circle-line',
    Spatie\Health\Enums\Status::failed()->value, Spatie\Health\Enums\Status::crashed()->value => 'ri-close-line',
    default => '',
  };
}

function createErrorLog(Exception $e, $channel, $controller)
{
  $error = [
    'controller' => $controller,
    'message' => $e->getMessage(),
    'file' => $e->getFile(),
    'line' => $e->getLine(),
    'trace' => $e->getTraceAsString(),
  ];

  Log::channel($channel)->error($controller,$error);
}
