{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-dompdf": "^3.1", "laravel/framework": "^11.0", "laravel/jetstream": "^5.1", "laravel/pulse": "^1.2", "laravel/reverb": "^1.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "livewire/livewire": "^3.0", "maatwebsite/excel": "^3.1", "mcamara/laravel-localization": "^2.0", "opcodesio/log-viewer": "^3.11", "psr/simple-cache": "2.0", "spatie/cpu-load-health-check": "^1.0", "spatie/laravel-activitylog": "^4.8", "spatie/laravel-health": "*", "spatie/laravel-permission": "^6.9"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^10.5", "pixinvent/materialize-laravel-bootstrap-jetstream": "^2.0", "spatie/laravel-ignition": "^2.4", "spatie/laravel-ray": "^1.37"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}, "files": ["app/Helpers/Classes/Helper.php", "app/Helpers/helper.php", "app/Helpers/Functions/memory.php"]}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}