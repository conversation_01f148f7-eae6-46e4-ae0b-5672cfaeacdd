<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

echo "=== Testing Repository Constructors ===\n";

try {
    // Test repositories that extend BaseRepository by creating them directly
    $repositories = [
        'TransferRequestRepository' => [\App\Repositories\TransferRequestRepository::class, \App\Models\TransferRequest::class],
        'TransferOrderRepository' => [\App\Repositories\TransferOrderRepository::class, \App\Models\TransferOrder::class],
        'TransferReceiptRepository' => [\App\Repositories\TransferReceiptRepository::class, \App\Models\TransferReceipt::class],
        'ProductRepository' => [\App\Repositories\ProductRepository::class, \App\Models\Product::class],
        'WarehouseRepository' => [\App\Repositories\WarehouseRepository::class, \App\Models\Warehouse::class],
        'SettingsRepository' => [\App\Repositories\SettingsRepository::class, \App\Models\Settings::class],
        'RolesRepository' => [\App\Repositories\RolesRepository::class, \Spatie\Permission\Models\Role::class],
        'UsersRepository' => [\App\Repositories\UsersRepository::class, \App\Models\User::class],
        'PermissionRepository' => [\App\Repositories\PermissionRepository::class, \Spatie\Permission\Models\Permission::class],
    ];

    foreach ($repositories as $name => $config) {
        try {
            $repositoryClass = $config[0];
            $modelClass = $config[1];
            $model = new $modelClass();
            $repository = new $repositoryClass($model);
            echo "✅ {$name}: OK\n";
        } catch (Exception $e) {
            echo "❌ {$name}: FAIL - " . $e->getMessage() . "\n";
        }
    }

    // Test ProductAttributeRepository (has multiple dependencies)
    echo "\n=== Testing ProductAttributeRepository ===\n";
    try {
        $repository = new \App\Repositories\ProductAttributeRepository(
            new \App\Models\ProductAttribute(),
            new \App\Models\Product(),
            new \App\Models\Attribute(),
            new \App\Models\AttributeValue()
        );
        echo "✅ ProductAttributeRepository: OK\n";
    } catch (Exception $e) {
        echo "❌ ProductAttributeRepository: FAIL - " . $e->getMessage() . "\n";
    }

    // Test repositories that don't extend BaseRepository (should still work)
    $otherRepositories = [
        'PurchaseOrderRepository' => [\App\Repositories\PurchaseOrderRepository::class, \App\Models\PurchaseOrder::class],
        'GoodReceiptRepository' => [\App\Repositories\GoodReceiptRepository::class, \App\Models\GoodReceipt::class],
    ];

    echo "\n=== Testing Other Repositories ===\n";
    foreach ($otherRepositories as $name => $config) {
        try {
            $repositoryClass = $config[0];
            $modelClass = $config[1];
            $model = new $modelClass();
            $repository = new $repositoryClass($model);
            echo "✅ {$name}: OK\n";
        } catch (Exception $e) {
            echo "❌ {$name}: FAIL - " . $e->getMessage() . "\n";
        }
    }

    echo "\n=== Test Completed ===\n";

} catch (Exception $e) {
    echo "❌ General Error: " . $e->getMessage() . "\n";
}
