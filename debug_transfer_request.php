<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\InventoryItem;
use App\Models\Warehouse;
use App\Models\Product;
use Illuminate\Support\Facades\DB;

echo "=== DEBUG TRANSFER REQUEST PRODUCTS ===\n\n";

// 1. <PERSON><PERSON>m tra tổng số inventory items
echo "1. Tổng số inventory items: " . InventoryItem::count() . "\n";

// 2. Kiểm tra inventory items có available_quantity > 0
$availableItems = InventoryItem::where('available_quantity', '>', 0)->count();
echo "2. Inventory items có available_quantity > 0: " . $availableItems . "\n\n";

// 3. Kiểm tra warehouses
echo "3. Danh sách warehouses:\n";
$warehouses = Warehouse::where('is_active', true)->get();
foreach ($warehouses as $warehouse) {
    echo "   - ID: {$warehouse->id}, Name: {$warehouse->name}\n";
}
echo "\n";

// 4. <PERSON><PERSON>m tra products
echo "4. Tổng số products active: " . Product::where('is_active', true)->count() . "\n\n";

// 5. Test query giống như trong controller
echo "5. Test query từ controller:\n";
if ($warehouses->count() > 0) {
    $firstWarehouse = $warehouses->first();
    echo "   Testing với warehouse: {$firstWarehouse->name} (ID: {$firstWarehouse->id})\n";

    $products = DB::table('inventory_items as ii')
        ->join('products as p', 'ii.product_id', '=', 'p.id')
        ->where('ii.warehouse_id', $firstWarehouse->id)
        ->where('ii.available_quantity', '>', 0)
        ->where('p.is_active', true)
        ->select(
            'p.id',
            'p.code',
            'p.name',
            'p.unit',
            'ii.available_quantity'
        )
        ->get();

    echo "   Kết quả: " . $products->count() . " sản phẩm\n";

    if ($products->count() > 0) {
        echo "   Sample products:\n";
        foreach ($products->take(3) as $product) {
            echo "     - {$product->name} ({$product->code}) - Tồn: {$product->available_quantity}\n";
        }
    }
} else {
    echo "   Không có warehouse nào!\n";
}

echo "\n";

// 6. Kiểm tra route và URL
echo "6. Kiểm tra route:\n";
try {
    $route = route('transfer-requests.warehouse-products', ['warehouseId' => 1]);
    echo "   Route URL: {$route}\n";
} catch (Exception $e) {
    echo "   Lỗi route: " . $e->getMessage() . "\n";
}

// 7. Kiểm tra chi tiết inventory items
echo "\n7. Chi tiết inventory items:\n";
$inventoryItems = InventoryItem::with(['product', 'warehouse'])->get();
foreach ($inventoryItems as $item) {
    echo "   - Warehouse: {$item->warehouse->name}\n";
    echo "     Product: {$item->product->name} ({$item->product->code})\n";
    echo "     Quantity: {$item->quantity}\n";
    echo "     Available: {$item->available_quantity}\n";
    echo "     Reserved: {$item->reserved_quantity}\n";
    echo "     ---\n";
}

echo "\n=== END DEBUG ===\n";
